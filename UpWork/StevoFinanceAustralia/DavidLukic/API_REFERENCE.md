# 🔌 ASX Data Scraper - API Reference

## 📋 Core Functions Reference

### 🕷️ **Scraping Functions**

#### `scrape_announcements(asx_codes, headless=True)`
**Purpose**: Main scraping function for ASX announcements

**Parameters**:
- `asx_codes` (list): List of ASX company codes to scrape
- `headless` (bool): Whether to run browser in headless mode

**Returns**:
- `tuple`: (results_4c, results_5b, results_options)
  - `results_4c`: List of 4C report data
  - `results_5b`: List of 5B report data  
  - `results_options`: List of options data

**Example**:
```python
from sync.announcement_scraper import scrape_announcements

# Scrape specific companies
codes = ['CBA', 'BHP', 'CSL']
results_4c, results_5b, options = scrape_announcements(codes, headless=True)

# Scrape all ASX codes
all_results = scrape_announcements('all', headless=False)
```

#### `setup_driver(headless=True)`
**Purpose**: Configure and initialize Chrome WebDriver

**Parameters**:
- `headless` (bool): Browser visibility mode

**Returns**:
- `WebDriver`: Configured Chrome WebDriver instance

**Example**:
```python
from sync.announcement_scraper import setup_driver

# Headless mode (faster)
driver = setup_driver(headless=True)

# GUI mode (for debugging)
driver = setup_driver(headless=False)
```

### 📄 **PDF Processing Functions**

#### `extract_financial_data(pdf_text, pdf_type)`
**Purpose**: Extract financial data from PDF text content

**Parameters**:
- `pdf_text` (str): Raw text content from PDF
- `pdf_type` (str): Type of report ('4C' or '5B')

**Returns**:
- `dict`: Extracted financial data with keys 8.1-8.7

**Example**:
```python
from sync.pdfextractor import extract_financial_data

# Process 4C report
data_4c = extract_financial_data(pdf_text, '4C')
# Returns: {'8.1': value, '8.2': value, '8.3': value, '8.4': value, '8.5': value}

# Process 5B report  
data_5b = extract_financial_data(pdf_text, '5B')
# Returns: {'8.1': value, ..., '8.7': value}
```

#### `extract_unquoted_option_data(pdf_text)`
**Purpose**: Extract unquoted option information from PDF

**Parameters**:
- `pdf_text` (str): Raw PDF text content

**Returns**:
- `list`: List of option data dictionaries

**Example**:
```python
from sync.pdfextractor2 import extract_unquoted_option_data

options = extract_unquoted_option_data(pdf_text)
# Returns: [{'code': 'ABCDO', 'description': 'OPTION EXPIRING...', 'quantity': '1000000'}]
```

### 📊 **Data Management Functions**

#### `download_asx_directory(headless=True)`
**Purpose**: Download latest ASX company directory

**Parameters**:
- `headless` (bool): Browser mode for download

**Returns**:
- `str`: Path to downloaded CSV directory

**Example**:
```python
from sync.download_csv import download_asx_directory

csv_path = download_asx_directory(headless=True)
print(f"ASX directory downloaded to: {csv_path}")
```

#### `get_result_folders()`
**Purpose**: Get list of available result folders

**Returns**:
- `list`: List of result folder paths

**Example**:
```python
from streamlit_utils import get_result_folders

folders = get_result_folders()
for folder in folders:
    print(f"Result folder: {folder}")
```

### 📈 **Visualization Functions**

#### `create_financial_charts(df, chart_type='line')`
**Purpose**: Generate interactive financial charts

**Parameters**:
- `df` (DataFrame): Financial data
- `chart_type` (str): Type of chart ('line', 'bar', 'scatter')

**Returns**:
- `plotly.Figure`: Interactive chart object

**Example**:
```python
from streamlit_utils import create_financial_charts
import pandas as pd

# Load data
df = pd.read_csv('results/results_4c.csv')

# Create line chart
fig = create_financial_charts(df, chart_type='line')

# Display in Streamlit
st.plotly_chart(fig)
```

#### `generate_comparison_chart(companies, metric)`
**Purpose**: Create multi-company comparison charts

**Parameters**:
- `companies` (list): List of company codes
- `metric` (str): Financial metric to compare

**Returns**:
- `plotly.Figure`: Comparison chart

**Example**:
```python
from streamlit_utils import generate_comparison_chart

# Compare cash flow for major banks
companies = ['CBA', 'ANZ', 'WBC', 'NAB']
fig = generate_comparison_chart(companies, '8.1')
```

## 🔧 **Configuration Classes**

### `ScrapingConfig`
**Purpose**: Configuration object for scraping operations

**Attributes**:
```python
class ScrapingConfig:
    def __init__(self):
        self.headless_mode = True
        self.delay_between_requests = 2
        self.max_retries = 3
        self.timeout_seconds = 30
        self.selected_codes = []
        self.scrape_4c = True
        self.scrape_5b = True
        self.scrape_options = True
        self.date_range = "Last 3 months"
```

**Example**:
```python
config = ScrapingConfig()
config.headless_mode = False
config.delay_between_requests = 5
config.selected_codes = ['BHP', 'RIO', 'FMG']
```

## 🛠️ **Utility Functions**

#### `get_correct_chromedriver_path()`
**Purpose**: Get corrected Chrome driver path

**Returns**:
- `str`: Path to working Chrome driver executable

**Example**:
```python
from scraper_functions import get_correct_chromedriver_path

driver_path = get_correct_chromedriver_path()
print(f"Chrome driver located at: {driver_path}")
```

#### `validate_asx_code(code)`
**Purpose**: Validate ASX company code format

**Parameters**:
- `code` (str): ASX code to validate

**Returns**:
- `bool`: True if valid ASX code format

**Example**:
```python
from streamlit_utils import validate_asx_code

is_valid = validate_asx_code('CBA')  # True
is_valid = validate_asx_code('INVALID')  # False
```

#### `format_currency(value)`
**Purpose**: Format financial values as currency

**Parameters**:
- `value` (float): Numeric value to format

**Returns**:
- `str`: Formatted currency string

**Example**:
```python
from streamlit_utils import format_currency

formatted = format_currency(1500000)  # "$1,500,000"
```

## 📊 **Data Structures**

### **4C Report Data Structure**
```python
{
    'Company': 'COMPANY NAME LIMITED',
    'ASX_Code': 'ABC',
    'GICS_Industry': 'Technology',
    'Listing_Date': '2020-01-15',
    'Market_Cap': 1500000000,
    '8.1': 2500000,  # Net cash from operating activities
    '8.2': -800000,  # Net cash from investing activities  
    '8.3': 1200000,  # Net cash from financing activities
    '8.4': 5500000,  # Cash and cash equivalents
    '8.5': 10000000, # Unused finance facilities
    'Scraping_Date': '2025-07-04',
    'Report_Period': 'Q1 2025'
}
```

### **5B Report Data Structure**
```python
{
    'Company': 'MINING COMPANY LIMITED',
    'ASX_Code': 'MIN',
    'GICS_Industry': 'Materials',
    '8.1': 1500000,  # Net cash from operating activities
    '8.2': -2500000, # Payments for exploration & evaluation
    '8.3': 800000,   # Total relevant incoming/outgoings
    '8.4': 3200000,  # Cash and cash equivalents
    '8.5': 5000000,  # Unused finance facilities
    '8.6': 8200000,  # Total available funding
    '8.7': 3.2,      # Estimated quarters of funding available
    'Scraping_Date': '2025-07-04'
}
```

### **Options Data Structure**
```python
{
    'Company': 'COMPANY NAME LIMITED',
    'ASX_Code': 'ABC',
    'Option_Code': 'ABCDO',
    'Description': 'OPTION EXPIRING 21-AUG-2025 EX $0.10',
    'Quantity': '35,683,063',
    'Exercise_Price': '$0.10',
    'Expiry_Date': '21-AUG-2025',
    'Document_Type': 'Appendix 2A',
    'Scraping_Date': '2025-07-04'
}
```

## 🔄 **Error Handling**

### **Common Exceptions**
```python
# Chrome driver issues
class ChromeDriverError(Exception):
    """Raised when Chrome driver cannot be initialized"""
    pass

# PDF processing errors  
class PDFExtractionError(Exception):
    """Raised when PDF data extraction fails"""
    pass

# Network connectivity issues
class ScrapingTimeoutError(Exception):
    """Raised when scraping operations timeout"""
    pass
```

### **Error Handling Example**
```python
try:
    results = scrape_announcements(['CBA'], headless=True)
except ChromeDriverError as e:
    print(f"Chrome driver error: {e}")
except ScrapingTimeoutError as e:
    print(f"Scraping timeout: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## 🧪 **Testing Functions**

#### `run_demo_scraping()`
**Purpose**: Execute demo scraping with sample data

**Returns**:
- `bool`: Success status

**Example**:
```python
from demo_data_generator import run_demo_scraping

success = run_demo_scraping()
if success:
    print("Demo data generated successfully")
```

#### `validate_data_integrity(csv_path)`
**Purpose**: Validate scraped data integrity

**Parameters**:
- `csv_path` (str): Path to CSV file to validate

**Returns**:
- `dict`: Validation results

**Example**:
```python
from streamlit_utils import validate_data_integrity

results = validate_data_integrity('results/results_4c.csv')
print(f"Validation passed: {results['valid']}")
print(f"Issues found: {results['issues']}")
```

---

**This API reference provides comprehensive documentation for all major functions and classes in the ASX Data Scraper system. For implementation examples, see the main application code and documentation files.**
