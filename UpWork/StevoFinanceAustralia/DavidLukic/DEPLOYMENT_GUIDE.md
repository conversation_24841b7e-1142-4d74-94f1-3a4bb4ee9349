# 🚀 ASX Data Scraper - Deployment Guide

## 🎯 Deployment Overview

This guide covers various deployment scenarios for the ASX Data Scraper, from local development to production cloud deployment.

## 💻 Local Development Setup

### **Prerequisites**
- Python 3.8+ installed
- Google Chrome browser
- Git (for cloning repository)
- 4GB+ RAM recommended
- 2GB+ free disk space

### **Step-by-Step Installation**

#### **1. Environment Setup**
```bash
# Clone the repository
git clone <repository-url>
cd DavidLukic

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Verify Python version
python --version  # Should be 3.8+
```

#### **2. Dependencies Installation**
```bash
# Install all required packages
pip install -r requirements.txt

# Verify critical packages
python -c "import streamlit, selenium, webdriver_manager; print('All packages installed successfully')"
```

#### **3. Chrome Driver Setup**
```bash
# The application automatically manages Chrome driver
# Ensure Google Chrome is installed and up-to-date
google-chrome --version  # Linux
# or check Chrome version in browser settings
```

#### **4. Application Launch**
```bash
# Simple launcher (recommended)
python run_streamlit_simple.py

# Alternative launcher with package checking
python run_streamlit.py

# Direct Streamlit launch
streamlit run streamlit_app.py
```

#### **5. Verification**
- Open browser to `http://localhost:8501`
- Check that all navigation buttons work
- Verify Chrome driver status in sidebar
- Test demo data generation

## 🐳 Docker Deployment

### **Dockerfile**
```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Google Chrome
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create results directory
RUN mkdir -p results

# Expose Streamlit port
EXPOSE 8501

# Set environment variables
ENV PYTHONPATH=/app
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_SERVER_ENABLE_CORS=false

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# Run application
CMD ["python", "run_streamlit_simple.py"]
```

### **Docker Compose**
```yaml
version: '3.8'

services:
  asx-scraper:
    build: .
    ports:
      - "8501:8501"
    volumes:
      - ./results:/app/results
      - ./logs:/app/logs
    environment:
      - STREAMLIT_SERVER_HEADLESS=true
      - CHROME_HEADLESS=true
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### **Build and Run**
```bash
# Build Docker image
docker build -t asx-scraper .

# Run container
docker run -p 8501:8501 -v $(pwd)/results:/app/results asx-scraper

# Using Docker Compose
docker-compose up -d
```

## ☁️ Cloud Deployment

### **AWS EC2 Deployment**

#### **1. EC2 Instance Setup**
```bash
# Launch EC2 instance (Ubuntu 20.04 LTS)
# Instance type: t3.medium or larger
# Security group: Allow inbound traffic on port 8501

# Connect to instance
ssh -i your-key.pem ubuntu@your-ec2-ip
```

#### **2. Server Configuration**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install -y python3 python3-pip python3-venv git

# Install Google Chrome
wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update
sudo apt install -y google-chrome-stable

# Clone and setup application
git clone <repository-url>
cd DavidLukic
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### **3. Systemd Service**
```bash
# Create service file
sudo nano /etc/systemd/system/asx-scraper.service
```

```ini
[Unit]
Description=ASX Data Scraper
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/DavidLukic
Environment=PATH=/home/<USER>/DavidLukic/venv/bin
ExecStart=/home/<USER>/DavidLukic/venv/bin/python run_streamlit_simple.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable asx-scraper
sudo systemctl start asx-scraper

# Check status
sudo systemctl status asx-scraper
```

#### **4. Nginx Reverse Proxy**
```bash
# Install Nginx
sudo apt install -y nginx

# Configure Nginx
sudo nano /etc/nginx/sites-available/asx-scraper
```

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/asx-scraper /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### **Azure Container Instances**

#### **1. Azure CLI Setup**
```bash
# Install Azure CLI
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# Login to Azure
az login

# Create resource group
az group create --name asx-scraper-rg --location eastus
```

#### **2. Container Deployment**
```bash
# Build and push to Azure Container Registry
az acr create --resource-group asx-scraper-rg --name asxscraperregistry --sku Basic
az acr login --name asxscraperregistry

# Tag and push image
docker tag asx-scraper asxscraperregistry.azurecr.io/asx-scraper:latest
docker push asxscraperregistry.azurecr.io/asx-scraper:latest

# Deploy container instance
az container create \
    --resource-group asx-scraper-rg \
    --name asx-scraper-instance \
    --image asxscraperregistry.azurecr.io/asx-scraper:latest \
    --cpu 2 \
    --memory 4 \
    --ports 8501 \
    --ip-address public \
    --environment-variables STREAMLIT_SERVER_HEADLESS=true
```

### **Google Cloud Run**

#### **1. Setup**
```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL
gcloud init

# Enable required APIs
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

#### **2. Deployment**
```bash
# Build and push to Google Container Registry
gcloud builds submit --tag gcr.io/your-project-id/asx-scraper

# Deploy to Cloud Run
gcloud run deploy asx-scraper \
    --image gcr.io/your-project-id/asx-scraper \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory 4Gi \
    --cpu 2 \
    --port 8501
```

## 🔧 Production Configuration

### **Environment Variables**
```bash
# Production environment variables
export STREAMLIT_SERVER_HEADLESS=true
export STREAMLIT_SERVER_ENABLE_CORS=false
export STREAMLIT_SERVER_PORT=8501
export CHROME_HEADLESS=true
export SCRAPING_DELAY=3
export MAX_RETRIES=5
export LOG_LEVEL=INFO
```

### **Performance Tuning**
```python
# streamlit_config.toml
[server]
headless = true
port = 8501
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[theme]
base = "light"
```

### **Security Considerations**
- Use HTTPS in production (SSL/TLS certificates)
- Implement rate limiting for scraping operations
- Set up proper firewall rules
- Regular security updates for dependencies
- Monitor for unusual activity patterns

### **Monitoring Setup**
```bash
# Install monitoring tools
pip install prometheus-client grafana-api

# Setup log rotation
sudo nano /etc/logrotate.d/asx-scraper
```

```
/home/<USER>/DavidLukic/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
}
```

## 🔍 Troubleshooting

### **Common Issues**

#### **Chrome Driver Problems**
```bash
# Check Chrome installation
google-chrome --version

# Verify driver path
python -c "from scraper_functions import get_correct_chromedriver_path; print(get_correct_chromedriver_path())"

# Manual driver download
wget https://chromedriver.storage.googleapis.com/LATEST_RELEASE
```

#### **Memory Issues**
```bash
# Monitor memory usage
htop
free -h

# Increase swap space
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

#### **Network Connectivity**
```bash
# Test ASX website connectivity
curl -I https://www.asx.com.au

# Check DNS resolution
nslookup www.asx.com.au

# Test port accessibility
telnet localhost 8501
```

### **Performance Optimization**
- Use headless mode for production
- Implement caching strategies
- Optimize Chrome driver settings
- Monitor resource usage
- Set appropriate request delays

### **Backup Strategy**
```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backup/asx-scraper-$DATE.tar.gz /home/<USER>/DavidLukic/results
find /backup -name "asx-scraper-*.tar.gz" -mtime +30 -delete
```

## 📊 Monitoring & Maintenance

### **Health Checks**
- Application responsiveness
- Chrome driver functionality
- Data quality validation
- Resource utilization monitoring

### **Regular Maintenance**
- Update dependencies monthly
- Chrome browser updates
- Log file rotation
- Data backup verification
- Performance optimization review

---

**This deployment guide covers comprehensive deployment scenarios from development to production. Choose the appropriate method based on your infrastructure requirements and technical expertise.**
