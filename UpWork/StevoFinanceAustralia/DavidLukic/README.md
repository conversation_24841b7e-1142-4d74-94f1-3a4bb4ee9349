# 🚀 ASX Data Scraper & Analyzer

A professional-grade web application for scraping and analyzing Australian Securities Exchange (ASX) financial data. Built with Python, Streamlit, and Selenium for comprehensive financial data extraction and visualization.

![ASX Scraper](https://img.shields.io/badge/ASX-Data%20Scraper-blue)
![Python](https://img.shields.io/badge/Python-3.8+-green)
![Streamlit](https://img.shields.io/badge/Streamlit-1.28+-red)
![License](https://img.shields.io/badge/License-MIT-yellow)

## 🌟 Features

### 📊 **Data Scraping**
- **Appendix 4C Reports**: Quarterly cash flow statements
- **Appendix 5B Reports**: Mining exploration entity quarterly reports
- **Options Reports**: Unquoted option data (2A/3B/3G forms)
- **Automated PDF Processing**: Extract financial data from PDF documents
- **Bulk Processing**: Scrape all ASX codes (~2000+ companies) or specific selections

### 🎨 **Beautiful User Interface**
- **Modern Design**: Gradient backgrounds, smooth animations, professional styling
- **Intuitive Navigation**: Button-based sidebar navigation
- **Real-time Progress**: Live scraping progress with detailed status updates
- **Responsive Layout**: Works perfectly on desktop and mobile devices
- **Dark/Light Themes**: Automatic theme adaptation

### 📈 **Data Visualization**
- **Interactive Charts**: Plotly-powered financial visualizations
- **Comparative Analysis**: Multi-company financial comparisons
- **Time Series Analysis**: Track financial metrics over time
- **Export Capabilities**: Download charts and data in multiple formats

### ⚙️ **Advanced Configuration**
- **Headless Mode**: Choose between GUI and headless browser operation
- **Custom Delays**: Configurable request delays for respectful scraping
- **Date Range Selection**: Flexible timeframe filtering
- **Popular ASX Codes**: Quick-select buttons for major companies

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Google Chrome browser
- Virtual environment (recommended)

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd DavidLukic
   ```

2. **Create and activate virtual environment:**
   ```bash
   python -m venv venv
   
   # On Windows:
   venv\Scripts\activate
   
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Launch the application:**
   ```bash
   python run_streamlit_simple.py
   ```

5. **Open your browser:**
   Navigate to `http://localhost:8501`

## 📖 Usage Guide

### 🏠 **Home Page**
- View recent scraping results
- Quick access to popular ASX codes
- System status and statistics
- Generate demo data for testing

### 🔍 **Data Scraper**
- **Select Report Types**: Choose 4C, 5B, or Options reports
- **Choose ASX Codes**: Select specific companies or scrape all
- **Configure Settings**: Set headless mode and request delays
- **Monitor Progress**: Real-time scraping status and results

### 📈 **Data Visualization**
- **Select Data Folder**: Choose from available result sets
- **Interactive Charts**: Explore financial metrics with Plotly
- **Filter and Compare**: Analyze multiple companies simultaneously
- **Export Data**: Download visualizations and raw data

### ⚙️ **Settings**
- **Chrome Driver**: Automatic driver management and path correction
- **Performance Tuning**: Adjust scraping parameters
- **Data Management**: Clear cache and manage result folders

## 🛠️ Technical Architecture

### **Core Components**
- **`streamlit_app.py`**: Main application interface
- **`announcement_scraper.py`**: ASX website scraping logic
- **`pdfextractor.py`**: PDF financial data extraction
- **`download_csv.py`**: ASX directory management
- **`streamlit_utils.py`**: Visualization utilities

### **Data Flow**
1. **ASX Directory Download**: Fetch latest company listings
2. **Announcement Scraping**: Extract PDF links from ASX announcements
3. **PDF Processing**: Parse financial data from PDF documents
4. **Data Storage**: Save results in timestamped CSV files
5. **Visualization**: Generate interactive charts and analysis

### **File Structure**
```
DavidLukic/
├── streamlit_app.py          # Main Streamlit application
├── run_streamlit_simple.py   # Application launcher
├── requirements.txt          # Python dependencies
├── sync/                     # Core scraping modules
│   ├── announcement_scraper.py
│   ├── pdfextractor.py
│   ├── download_csv.py
│   └── main_sync.py
├── results/                  # Scraped data output
├── demo_data/               # Sample data for testing
└── docs/                    # Documentation
```

## 🔧 Configuration

### **Environment Variables**
- `CHROME_DRIVER_PATH`: Custom Chrome driver path (optional)
- `SCRAPING_DELAY`: Default delay between requests (default: 2 seconds)
- `HEADLESS_MODE`: Default browser mode (default: True)

### **Chrome Driver Management**
The application automatically manages Chrome driver installation and updates using `webdriver-manager`. If issues occur, the system includes intelligent path correction to find the correct driver executable.

## 📊 Data Output

### **CSV File Structure**
- **4C Reports**: `results_4c_TIMESTAMP.csv`
  - Company metadata (name, code, industry, market cap)
  - Financial metrics (8.1-8.5 fields)
  - Formatted currency values

- **5B Reports**: `results_5b_TIMESTAMP.csv`
  - Extended financial data (8.1-8.7 fields)
  - Exploration and evaluation payments
  - Funding availability metrics

- **Options Reports**: `results_options_TIMESTAMP.csv`
  - Unquoted option details
  - Exercise prices and expiry dates
  - Security descriptions

### **Folder Organization**
Results are organized in timestamped folders:
```
results/
├── HH_MM-MM-DD-YYYY/
│   ├── results_4c_timestamp.csv
│   ├── results_5b_timestamp.csv
│   ├── results_options_timestamp.csv
│   └── asx_directory.csv
```

## 🚨 Troubleshooting

### **Common Issues**

1. **Chrome Driver Not Found**
   - Solution: The app automatically downloads and configures Chrome driver
   - Manual fix: Ensure Google Chrome is installed

2. **Package Import Errors**
   - Solution: Reinstall requirements: `pip install -r requirements.txt`
   - Check virtual environment activation

3. **Scraping Timeouts**
   - Solution: Increase delay between requests in settings
   - Check internet connection stability

4. **PDF Extraction Failures**
   - Solution: Some PDFs may have non-standard formats
   - Check PDF accessibility and format

### **Performance Optimization**
- Use headless mode for faster scraping
- Adjust request delays based on system performance
- Monitor system resources during bulk operations
- Clear cache regularly for optimal performance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -am 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Developer

**David Lukic**
- 🌐 Portfolio: [https://davidlukic99.github.io/](https://davidlukic99.github.io/)
- 📧 Email: [<EMAIL>](mailto:<EMAIL>)
- 💼 Professional ASX Data Solutions

## 🙏 Acknowledgments

- ASX for providing public access to financial data
- Streamlit team for the excellent web framework
- Selenium and webdriver-manager for browser automation
- Plotly for interactive visualization capabilities

---

**Built with ❤️ for the Australian financial community**
