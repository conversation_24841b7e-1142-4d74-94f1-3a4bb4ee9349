# ASX Data Scraper & Analyzer - Streamlit UI

A comprehensive web-based interface for scraping and analyzing ASX financial data including 4C, 5B, and options reports.

## 🚀 Quick Start

### Option 1: Using the Launcher Script (Recommended)
```bash
python run_streamlit.py
```

### Option 2: Direct Streamlit Command
```bash
# Install requirements first
pip install -r requirements.txt

# Run the app
streamlit run streamlit_app.py
```

The application will automatically open in your default web browser at `http://localhost:8501`

## 📋 Features

### 🏠 Home Page
- Overview of application capabilities
- Quick start guide
- Recent results summary
- System status

### 🔍 Data Scraper
- **Report Type Selection**: Choose between 4C, 5B, and Options reports
- **ASX Code Selection**: 
  - All ASX codes from directory
  - Specific codes (manual entry)
  - Upload CSV file with codes
- **Configuration Management**: Save and load scraper configurations
- **Real-time Progress**: Live progress tracking with status updates
- **Results Summary**: Immediate feedback on scraping results

### 📈 Data Visualization
- **Folder Selection**: Choose from available result folders
- **Multiple Tabs**:
  - **Overview**: Key metrics and summary statistics
  - **Financial Metrics**: Detailed financial analysis with charts
  - **Trends**: Time-series analysis and industry trends
  - **Raw Data**: Searchable data tables with export functionality

### ⚙️ Settings
- Application preferences
- Scraping parameters
- Data management options

## 📊 Visualization Features

### Financial Metrics Analysis
- **Overview Charts**: Average financial metrics by report type
- **Company Comparison**: Top companies by cash position
- **Funding Distribution**: Quarters of funding analysis
- **Correlation Matrix**: Relationships between financial metrics

### Trend Analysis
- **Cash Flow Trends**: Time-series analysis of cash flows
- **Industry Analysis**: Report distribution by industry
- **Metric Time Series**: Individual metric trends over time

### Interactive Features
- **Hover Details**: Rich tooltips with additional information
- **Zoom & Pan**: Interactive chart navigation
- **Data Export**: Download filtered data as CSV
- **Search & Filter**: Find specific companies or data points

## 🗂️ Data Structure

The application works with the following file structure:

```
results_HH_MM-DD-MM-YYYY/
├── financial_data_4C.csv      # Appendix 4C reports
├── financial_data_5B.csv      # Appendix 5B reports
├── unquoted_option_data.csv   # Options reports (2A/3B/3G)
└── asx_directory_*.csv        # ASX company directory
```

### CSV File Formats

#### 4C Reports (financial_data_4C.csv)
- `asx_code`: ASX stock code
- `company_name`: Company name
- `gics_industry_group`: Industry classification
- `listing_date`: Stock listing date
- `market_cap`: Market capitalization
- `date`: Report date
- `title`: Report title
- `url`: PDF document URL
- `report_type`: "4C"
- `net_cash_operating_activities`: 8.1 - Net cash from operations
- `cash_and_cash_equivalents`: 8.2 - Cash position
- `unused_finance_facilities`: 8.3 - Available credit
- `total_available_funding`: 8.4 - Total funding
- `estimated_quarters_funding`: 8.5 - Quarters of funding

#### 5B Reports (financial_data_5B.csv)
- Similar to 4C with additional fields:
- `payments_exploration_evaluation`: 8.2 - Exploration payments
- `total_relevant_outgoings`: 8.3 - Total outgoings

## 🛠️ Configuration Management

### Saving Configurations
1. Set up your scraper parameters
2. Enter a configuration name
3. Click "💾 Save Config"
4. Configuration is saved for future use

### Loading Configurations
1. Select from "Load saved configuration" dropdown
2. Configuration automatically applies to current session
3. Modify as needed before running

### Configuration Files
Saved in `scraper_configs/` directory as JSON files.

## 📈 Usage Examples

### Example 1: Scrape Specific Companies
1. Go to "🔍 Data Scraper"
2. Select "Specific codes"
3. Enter: `CBA, BHP, CSL, WBC, ANZ`
4. Choose report types
5. Click "🚀 Start Scraping"

### Example 2: Analyze Results
1. Go to "📈 Data Visualization"
2. Select a results folder
3. Explore different tabs:
   - View overview metrics
   - Analyze financial trends
   - Compare companies
   - Export specific data

### Example 3: Industry Analysis
1. Load results with multiple companies
2. Go to "Trends" tab
3. View "Industry Analysis" chart
4. Identify top industries by report volume

## 🔧 Technical Requirements

### Python Requirements
- Python 3.8 or higher
- See `requirements.txt` for package dependencies

### System Requirements
- Chrome browser (for Selenium WebDriver)
- Internet connection for ASX data access
- Minimum 4GB RAM recommended
- 1GB free disk space for data storage

## 🚨 Troubleshooting

### Common Issues

#### "Module not found" errors
```bash
pip install -r requirements.txt
```

#### Chrome driver issues
The application uses `webdriver-manager` to automatically handle Chrome driver installation.

#### Port already in use
If port 8501 is busy, Streamlit will automatically try the next available port.

#### Memory issues with large datasets
- Reduce the number of ASX codes
- Process data in smaller batches
- Increase system RAM if possible

### Performance Tips

1. **Selective Scraping**: Only scrape needed report types
2. **Batch Processing**: Process smaller sets of ASX codes
3. **Regular Cleanup**: Remove old result folders
4. **Browser Settings**: Use headless mode for faster scraping

## 📝 Data Export

### CSV Export
- All visualizations support data export
- Filtered data can be downloaded
- Original format preserved

### Supported Formats
- CSV (primary format)
- Excel (via pandas conversion)
- JSON (for configuration files)

## 🔒 Data Privacy

- All data processing happens locally
- No data sent to external servers (except ASX website)
- Results stored in local filesystem
- No user tracking or analytics

## 🆘 Support

For issues or questions:
1. Check this README
2. Review error messages in the application
3. Check the terminal/console for detailed logs
4. Verify all requirements are installed

## 🔄 Updates

To update the application:
1. Pull latest code changes
2. Update requirements: `pip install -r requirements.txt --upgrade`
3. Restart the application

---

**Note**: This application is designed for educational and research purposes. Please respect ASX's terms of service and implement appropriate delays between requests.
