"""
Configuration settings for the ASX Data Scraper Streamlit application.
"""

import os

# Application settings
APP_TITLE = "ASX Data Scraper & Analyzer"
APP_ICON = "📊"
APP_VERSION = "1.0.0"

# Streamlit configuration
STREAMLIT_CONFIG = {
    'page_title': APP_TITLE,
    'page_icon': APP_ICON,
    'layout': 'wide',
    'initial_sidebar_state': 'expanded'
}

# File paths
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
SYNC_DIR = os.path.join(BASE_DIR, 'sync')
RESULTS_PATTERN = os.path.join(BASE_DIR, 'results_*')
CONFIG_DIR = os.path.join(BASE_DIR, 'scraper_configs')
SETTINGS_FILE = os.path.join(BASE_DIR, 'app_settings.json')

# Default scraper settings
DEFAULT_SCRAPER_CONFIG = {
    'scrape_4c': True,
    'scrape_5b': True,
    'scrape_options': True,
    'delay_between_requests': 3,
    'timeout_duration': 30,
    'headless_mode': False
}

# Visualization settings
CHART_CONFIG = {
    'default_height': 400,
    'color_palette': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
    'background_color': 'white',
    'grid_color': '#f0f0f0'
}

# Data processing settings
DATA_CONFIG = {
    'max_display_rows': 1000,
    'date_format': '%Y-%m-%d',
    'currency_format': '${:,.0f}k',
    'percentage_format': '{:.1f}%'
}

# Report type configurations
REPORT_TYPES = {
    '4C': {
        'name': 'Appendix 4C',
        'description': 'Quarterly cash flow reports',
        'fields': [
            'net_cash_operating_activities',
            'cash_and_cash_equivalents',
            'unused_finance_facilities',
            'total_available_funding',
            'estimated_quarters_funding'
        ],
        'currency_fields': [
            'net_cash_operating_activities',
            'cash_and_cash_equivalents',
            'unused_finance_facilities',
            'total_available_funding'
        ]
    },
    '5B': {
        'name': 'Appendix 5B',
        'description': 'Mining exploration reports',
        'fields': [
            'net_cash_operating_activities',
            'payments_exploration_evaluation',
            'total_relevant_outgoings',
            'cash_and_cash_equivalents',
            'unused_finance_facilities',
            'total_available_funding',
            'estimated_quarters_funding'
        ],
        'currency_fields': [
            'net_cash_operating_activities',
            'payments_exploration_evaluation',
            'total_relevant_outgoings',
            'cash_and_cash_equivalents',
            'unused_finance_facilities',
            'total_available_funding'
        ]
    },
    'OPTIONS': {
        'name': 'Options Reports',
        'description': 'Unquoted option reports (2A/3B/3G)',
        'fields': [],  # Will be determined dynamically
        'currency_fields': []
    }
}

# UI Messages
MESSAGES = {
    'welcome': "Welcome to the ASX Data Scraper & Analyzer",
    'no_data': "No data available for the selected criteria",
    'loading': "Loading data...",
    'scraping_started': "Scraping started successfully",
    'scraping_completed': "Scraping completed successfully",
    'scraping_failed': "Scraping failed",
    'config_saved': "Configuration saved successfully",
    'config_loaded': "Configuration loaded successfully"
}

# Error messages
ERROR_MESSAGES = {
    'no_codes_selected': "Please select ASX codes to scrape",
    'no_report_types': "Please select at least one report type",
    'invalid_date_range': "Invalid date range selected",
    'file_not_found': "File not found",
    'import_error': "Error importing required modules",
    'scraping_error': "Error during scraping process",
    'data_processing_error': "Error processing data"
}

# Help text
HELP_TEXT = {
    '4c_reports': "Appendix 4C reports contain quarterly cash flow information for listed entities",
    '5b_reports': "Appendix 5B reports contain quarterly cash flow information for mining exploration entities",
    'options_reports': "Reports related to unquoted options (forms 2A, 3B, 3G)",
    'asx_codes': "Enter ASX stock codes (e.g., CBA, BHP, CSL) one per line or comma-separated",
    'date_range': "Select the timeframe for data collection",
    'all_codes': "This will scrape all available ASX codes from the directory (may take several hours)"
}

# Performance settings
PERFORMANCE_CONFIG = {
    'chunk_size': 100,  # Number of codes to process in each batch
    'max_concurrent_requests': 5,
    'request_delay_min': 1,
    'request_delay_max': 5,
    'retry_attempts': 3,
    'timeout_seconds': 30
}

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': os.path.join(BASE_DIR, 'app.log'),
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

def get_config(section=None):
    """Get configuration section or all config"""
    config = {
        'app': {
            'title': APP_TITLE,
            'icon': APP_ICON,
            'version': APP_VERSION
        },
        'streamlit': STREAMLIT_CONFIG,
        'paths': {
            'base_dir': BASE_DIR,
            'sync_dir': SYNC_DIR,
            'results_pattern': RESULTS_PATTERN,
            'config_dir': CONFIG_DIR,
            'settings_file': SETTINGS_FILE
        },
        'scraper': DEFAULT_SCRAPER_CONFIG,
        'charts': CHART_CONFIG,
        'data': DATA_CONFIG,
        'reports': REPORT_TYPES,
        'messages': MESSAGES,
        'errors': ERROR_MESSAGES,
        'help': HELP_TEXT,
        'performance': PERFORMANCE_CONFIG,
        'logging': LOGGING_CONFIG
    }
    
    if section:
        return config.get(section, {})
    return config

def update_config(section, key, value):
    """Update a configuration value"""
    # This would be implemented to persist config changes
    pass

# Environment-specific overrides
if os.getenv('STREAMLIT_ENV') == 'development':
    DEFAULT_SCRAPER_CONFIG['headless_mode'] = False
    LOGGING_CONFIG['level'] = 'DEBUG'
elif os.getenv('STREAMLIT_ENV') == 'production':
    DEFAULT_SCRAPER_CONFIG['headless_mode'] = True
    LOGGING_CONFIG['level'] = 'WARNING'
