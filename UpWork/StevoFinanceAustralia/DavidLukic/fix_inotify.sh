#!/bin/bash

# Fix inotify watch limit issue for Streamlit
# This script increases the inotify limits temporarily

echo "🔧 Fixing inotify watch limit for Streamlit..."
echo "================================================"

# Check current limits
echo "📊 Current inotify limits:"
echo "   max_user_watches: $(cat /proc/sys/fs/inotify/max_user_watches)"
echo "   max_user_instances: $(cat /proc/sys/fs/inotify/max_user_instances)"

# Increase limits temporarily (requires sudo)
echo ""
echo "🚀 Increasing inotify limits temporarily..."

# Try to increase limits
if sudo sysctl fs.inotify.max_user_watches=524288 2>/dev/null; then
    echo "✅ Increased max_user_watches to 524288"
else
    echo "⚠️  Could not increase max_user_watches (may need sudo)"
fi

if sudo sysctl fs.inotify.max_user_instances=512 2>/dev/null; then
    echo "✅ Increased max_user_instances to 512"
else
    echo "⚠️  Could not increase max_user_instances (may need sudo)"
fi

echo ""
echo "📊 New inotify limits:"
echo "   max_user_watches: $(cat /proc/sys/fs/inotify/max_user_watches)"
echo "   max_user_instances: $(cat /proc/sys/fs/inotify/max_user_instances)"

echo ""
echo "💡 To make these changes permanent, add these lines to /etc/sysctl.conf:"
echo "   fs.inotify.max_user_watches=524288"
echo "   fs.inotify.max_user_instances=512"

echo ""
echo "🎯 You can now try running the Streamlit app again:"
echo "   python run_streamlit.py"
echo "   or"
echo "   python run_streamlit_simple.py (recommended)"
