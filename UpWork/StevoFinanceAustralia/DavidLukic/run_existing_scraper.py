#!/usr/bin/env python3
"""
<PERSON>ript to run the existing scraper from Streamlit interface.
This bypasses Chrome driver issues by using your working sync scripts.
"""

import os
import sys
import subprocess
import json
from datetime import datetime

def run_existing_scraper(asx_codes=None, output_dir=None):
    """
    Run the existing main_sync.py script with optional ASX code filtering
    
    Args:
        asx_codes (list): Optional list of ASX codes to filter
        output_dir (str): Optional output directory name
    
    Returns:
        dict: Result information
    """
    
    try:
        # Get the sync directory
        sync_dir = os.path.join(os.path.dirname(__file__), 'sync')
        
        if not os.path.exists(sync_dir):
            return {
                'success': False,
                'error': 'Sync directory not found'
            }
        
        # If specific ASX codes are provided, create a filtered version
        if asx_codes:
            # Create a temporary script that filters ASX codes
            filtered_script = create_filtered_script(asx_codes, sync_dir)
            script_to_run = filtered_script
        else:
            # Use the original main_sync.py
            script_to_run = os.path.join(sync_dir, 'main_sync.py')
        
        print(f"Running script: {script_to_run}")
        print(f"Working directory: {sync_dir}")
        
        # Run the script
        result = subprocess.run(
            [sys.executable, script_to_run],
            cwd=sync_dir,
            capture_output=True,
            text=True,
            timeout=3600  # 1 hour timeout
        )
        
        # Clean up temporary script if created
        if asx_codes and os.path.exists(script_to_run):
            os.remove(script_to_run)
        
        if result.returncode == 0:
            # Parse the output to find result folder
            output_lines = result.stdout.split('\n')
            result_folder = None
            
            for line in output_lines:
                if 'results saved to folder:' in line.lower():
                    result_folder = line.split(':')[-1].strip()
                    break
                elif 'created results folder:' in line.lower():
                    result_folder = line.split(':')[-1].strip()
                    break
            
            # Count files in result folder
            files_created = []
            summary = {'4c_count': 0, '5b_count': 0, 'options_count': 0}
            
            if result_folder and os.path.exists(result_folder):
                for file in os.listdir(result_folder):
                    if file.endswith('.csv'):
                        files_created.append(os.path.join(result_folder, file))
                        
                        # Count records
                        try:
                            import pandas as pd
                            df = pd.read_csv(os.path.join(result_folder, file))
                            
                            if '4C' in file:
                                summary['4c_count'] = len(df)
                            elif '5B' in file:
                                summary['5b_count'] = len(df)
                            elif 'option' in file.lower():
                                summary['options_count'] = len(df)
                        except:
                            pass
            
            return {
                'success': True,
                'result_folder': result_folder,
                'files_created': files_created,
                'summary': summary,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
        else:
            return {
                'success': False,
                'error': f"Script failed with return code {result.returncode}",
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'error': 'Script timed out after 1 hour'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def create_filtered_script(asx_codes, sync_dir):
    """Create a temporary script that filters ASX codes"""
    
    # Read the original main_sync.py
    original_script = os.path.join(sync_dir, 'main_sync.py')
    
    with open(original_script, 'r') as f:
        content = f.read()
    
    # Create filtered version
    filtered_content = content.replace(
        "asx_codes = df_companies['ASX code'].to_list()",
        f"asx_codes = {asx_codes}  # Filtered ASX codes from Streamlit"
    )
    
    # Add comment at the top
    filtered_content = f"""# Temporary filtered version of main_sync.py
# Generated by Streamlit interface
# ASX codes: {asx_codes}

{filtered_content}
"""
    
    # Write temporary script
    temp_script = os.path.join(sync_dir, 'temp_filtered_main.py')
    
    with open(temp_script, 'w') as f:
        f.write(filtered_content)
    
    return temp_script

def test_existing_scraper():
    """Test if the existing scraper can run"""
    try:
        sync_dir = os.path.join(os.path.dirname(__file__), 'sync')
        main_script = os.path.join(sync_dir, 'main_sync.py')
        
        if not os.path.exists(main_script):
            return False, "main_sync.py not found"
        
        # Quick syntax check
        result = subprocess.run(
            [sys.executable, '-m', 'py_compile', main_script],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            return True, "Script syntax is valid"
        else:
            return False, f"Syntax error: {result.stderr}"
            
    except Exception as e:
        return False, str(e)

if __name__ == "__main__":
    # Test mode
    print("🧪 Testing existing scraper...")
    
    success, message = test_existing_scraper()
    
    if success:
        print(f"✅ {message}")
        
        # Test with a small set of codes
        print("\n🚀 Testing with sample ASX codes...")
        result = run_existing_scraper(['CBA', 'BHP'])
        
        if result['success']:
            print("✅ Test run successful!")
            print(f"📁 Result folder: {result.get('result_folder', 'Not found')}")
            print(f"📊 Summary: {result['summary']}")
        else:
            print(f"❌ Test run failed: {result['error']}")
    else:
        print(f"❌ {message}")
