#!/usr/bin/env python3
"""
Launcher script for the ASX Data Scraper Streamlit application.
This script handles environment setup and launches the Streamlit app.
"""

import os
import sys
import subprocess
import platform

def check_requirements():
    """Check if required packages are installed"""
    required_packages = [
        'streamlit',
        'pandas',
        'plotly',
        'selenium',
        'webdriver-manager'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_requirements():
    """Install missing requirements"""
    print("Installing required packages...")
    
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    requirements_file = os.path.join(script_dir, 'requirements.txt')
    
    if os.path.exists(requirements_file):
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', requirements_file])
            print("✅ Requirements installed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Error installing requirements: {e}")
            return False
    else:
        print(f"❌ Requirements file not found: {requirements_file}")
        return False

def launch_streamlit():
    """Launch the Streamlit application"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    app_file = os.path.join(script_dir, 'streamlit_app.py')
    
    if not os.path.exists(app_file):
        print(f"❌ Streamlit app file not found: {app_file}")
        return False
    
    print("🚀 Launching ASX Data Scraper & Analyzer...")
    print(f"📁 Working directory: {script_dir}")
    print("🌐 The app will open in your default web browser")
    print("⏹️  Press Ctrl+C to stop the application")
    print("-" * 50)
    
    try:
        # Change to the script directory
        os.chdir(script_dir)

        # Launch Streamlit with file watcher disabled to avoid inotify issues
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 'streamlit_app.py',
            '--server.port', '8501',
            '--server.address', 'localhost',
            '--browser.gatherUsageStats', 'false',
            '--server.fileWatcherType', 'none'
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error launching Streamlit: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("=" * 60)
    print("🎯 ASX Data Scraper & Analyzer - Launcher")
    print("=" * 60)
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {python_version.major}.{python_version.minor}.{python_version.micro}")
        sys.exit(1)
    
    print(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    print(f"💻 Platform: {platform.system()} {platform.release()}")
    
    # Check requirements
    print("\n📦 Checking requirements...")
    missing_packages = check_requirements()
    
    if missing_packages:
        print(f"⚠️  Missing packages: {', '.join(missing_packages)}")
        
        # Ask user if they want to install
        response = input("Would you like to install missing packages? (y/n): ").lower().strip()
        
        if response in ['y', 'yes']:
            if not install_requirements():
                print("❌ Failed to install requirements. Please install manually:")
                print("   pip install -r requirements.txt")
                sys.exit(1)
        else:
            print("❌ Cannot proceed without required packages")
            sys.exit(1)
    else:
        print("✅ All required packages are installed")
    
    # Launch the application
    print("\n🚀 Starting application...")
    if not launch_streamlit():
        sys.exit(1)

if __name__ == "__main__":
    main()
