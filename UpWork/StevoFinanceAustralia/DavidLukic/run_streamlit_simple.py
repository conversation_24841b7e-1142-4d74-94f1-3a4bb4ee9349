#!/usr/bin/env python3
"""
Simple launcher for the ASX Data Scraper Streamlit application.
This version disables file watching to avoid inotify issues on Linux.
"""

import os
import sys
import subprocess

def main():
    """Simple launcher that disables file watching"""
    print("🚀 ASX Data Scraper & Analyzer - Simple Launcher")
    print("=" * 50)
    
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    app_file = os.path.join(script_dir, 'streamlit_app.py')
    
    if not os.path.exists(app_file):
        print(f"❌ Streamlit app file not found: {app_file}")
        return False
    
    print("🚀 Launching ASX Data Scraper & Analyzer...")
    print("📁 Working directory:", script_dir)
    print("🌐 The app will open at: http://localhost:8501")
    print("⏹️  Press Ctrl+C to stop the application")
    print("📝 Note: File watching is disabled to prevent inotify issues")
    print("-" * 50)
    
    try:
        # Change to the script directory
        os.chdir(script_dir)
        
        # Set environment variable to disable file watching
        env = os.environ.copy()
        env['STREAMLIT_SERVER_FILE_WATCHER_TYPE'] = 'none'
        
        # Launch Streamlit with file watcher disabled
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 'streamlit_app.py',
            '--server.port', '8501',
            '--server.address', 'localhost',
            '--browser.gatherUsageStats', 'false',
            '--server.fileWatcherType', 'none',
            '--server.runOnSave', 'false'
        ], env=env)
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except FileNotFoundError:
        print("❌ Streamlit not found. Please install it:")
        print("   pip install streamlit")
        return False
    except Exception as e:
        print(f"❌ Error launching Streamlit: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
