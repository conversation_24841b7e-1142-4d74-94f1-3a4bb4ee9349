"""
Extracted functions from the sync modules for use in Streamlit app.
This avoids importing modules that execute code at import time.
"""

import pandas as pd
import os
import sys

# Add sync directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'sync'))

def format_currency(value):
    """Format financial values as currency in thousands"""
    if value == '-' or value == 'N/A' or value == 'Not found':
        return value
    try:
        # Remove any existing formatting and convert to float
        if isinstance(value, str):
            clean_value = value.replace(',', '').replace('$', '').replace('(', '').replace(')', '')
            if clean_value == '-' or clean_value.lower() == 'n/a':
                return value
            num_value = float(clean_value)
        else:
            num_value = float(value)

        # Convert to thousands and format with commas
        thousands_value = int(num_value)
        return f"${thousands_value:,},000"
    except (ValueError, TypeError):
        return value

def add_company_info(results, company_lookup):
    """Add company information to results"""
    enhanced_results = []
    for result in results:
        asx_code = result['asx_code']
        if asx_code in company_lookup:
            company_info = company_lookup[asx_code]
            enhanced_result = {
                'asx_code': asx_code,
                'company_name': company_info['Company name'],
                'gics_industry_group': company_info['GICs industry group'],
                'listing_date': company_info['Listing date'],
                'market_cap': company_info['Market Cap'],
                **{k: v for k, v in result.items() if k != 'asx_code'}
            }
            enhanced_results.append(enhanced_result)
        else:
            # If company info not found, add empty fields
            enhanced_result = {
                'asx_code': asx_code,
                'company_name': 'Not found',
                'gics_industry_group': 'Not found',
                'listing_date': 'Not found',
                'market_cap': 'Not found',
                **{k: v for k, v in result.items() if k != 'asx_code'}
            }
            enhanced_results.append(enhanced_result)
    return enhanced_results

def format_financial_values_4c(results):
    """Format 4C financial values (8.1-8.4 as currency, 8.5 as-is)"""
    formatted_results = []
    for result in results.copy():
        result['net_cash_operating_activities'] = format_currency(result.get('net_cash_operating_activities', 'Not found'))
        result['cash_and_cash_equivalents'] = format_currency(result.get('cash_and_cash_equivalents', 'Not found'))
        result['unused_finance_facilities'] = format_currency(result.get('unused_finance_facilities', 'Not found'))
        result['total_available_funding'] = format_currency(result.get('total_available_funding', 'Not found'))
        # Leave 8.5 (estimated_quarters_funding) as-is
        formatted_results.append(result)
    return formatted_results

def format_financial_values_5b(results):
    """Format 5B financial values (8.1-8.6 as currency, 8.7 as-is)"""
    formatted_results = []
    for result in results.copy():
        result['net_cash_operating_activities'] = format_currency(result.get('net_cash_operating_activities', 'Not found'))
        result['payments_exploration_evaluation'] = format_currency(result.get('payments_exploration_evaluation', 'Not found'))
        result['total_relevant_outgoings'] = format_currency(result.get('total_relevant_outgoings', 'Not found'))
        result['cash_and_cash_equivalents'] = format_currency(result.get('cash_and_cash_equivalents', 'Not found'))
        result['unused_finance_facilities'] = format_currency(result.get('unused_finance_facilities', 'Not found'))
        result['total_available_funding'] = format_currency(result.get('total_available_funding', 'Not found'))
        # Leave 8.7 (estimated_quarters_funding) as-is
        formatted_results.append(result)
    return formatted_results

def get_correct_chromedriver_path():
    """Get the correct Chrome driver path, fixing webdriver-manager issues"""
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        import glob

        # Clear any cached driver that might be corrupted
        driver_manager = ChromeDriverManager()

        # Get the driver path from webdriver-manager
        driver_path = driver_manager.install()
        print(f"WebDriver Manager returned: {driver_path}")

        # Check if it's pointing to the wrong file (common webdriver-manager bug)
        if 'THIRD_PARTY_NOTICES' in driver_path or not driver_path.endswith('chromedriver'):
            print("Detected incorrect driver path, searching for actual chromedriver...")

            # Find the actual chromedriver executable in the same directory
            driver_dir = os.path.dirname(driver_path)
            actual_driver = os.path.join(driver_dir, 'chromedriver')

            print(f"Looking for chromedriver in: {driver_dir}")

            if os.path.exists(actual_driver):
                print(f"Found chromedriver at: {actual_driver}")
                # Make sure it's executable
                os.chmod(actual_driver, 0o755)
                return actual_driver

            # Search more broadly in the webdriver-manager directory
            wdm_base = os.path.expanduser("~/.wdm")
            if os.path.exists(wdm_base):
                print(f"Searching for chromedriver in: {wdm_base}")
                possible_drivers = glob.glob(os.path.join(wdm_base, '**/chromedriver'), recursive=True)

                for possible_driver in possible_drivers:
                    print(f"Checking: {possible_driver}")
                    if os.path.isfile(possible_driver):
                        # Make sure it's executable
                        os.chmod(possible_driver, 0o755)
                        print(f"Using chromedriver: {possible_driver}")
                        return possible_driver

        # If the original path looks correct, use it
        if os.path.exists(driver_path) and os.path.isfile(driver_path):
            os.chmod(driver_path, 0o755)
            return driver_path

        raise Exception("Could not find a valid chromedriver executable")

    except Exception as e:
        print(f"Error getting chromedriver path: {e}")
        return None

def safe_download_asx_directory(headless=True):
    """Safely download ASX directory using the EXACT same method as your working sync scripts"""
    try:
        # Use the EXACT same approach as your working sync/download_csv.py
        from sync.download_csv import download_asx_directory
        print(f"Using your existing working download_csv module (headless={headless})...")
        return download_asx_directory(headless=headless)

    except Exception as e:
        print(f"Error with existing download module: {e}")
        print("This suggests the same Chrome driver issue affects your original scripts too.")
        print("Try running: cd sync && python main_sync.py")
        print("If that works, the issue is with our import method.")
        return None

def safe_scrape_announcements(asx_codes, headless=True):
    """Safely scrape announcements using the EXACT same method as your working sync scripts"""
    try:
        # Use the EXACT same approach as your working sync/announcement_scraper.py
        from sync.announcement_scraper import scrape_announcements
        print(f"Using your existing working announcement_scraper for {len(asx_codes)} codes (headless={headless})...")
        return scrape_announcements(asx_codes, headless=headless)
    except Exception as e:
        print(f"Error scraping announcements: {e}")
        print("This suggests the same Chrome driver issue affects your original scripts too.")
        print("Try running: cd sync && python main_sync.py")
        return [], [], []
