"""
Extracted functions from the sync modules for use in Streamlit app.
This avoids importing modules that execute code at import time.
"""

import pandas as pd
import os
import sys

# Add sync directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'sync'))

def format_currency(value):
    """Format financial values as currency in thousands"""
    if value == '-' or value == 'N/A' or value == 'Not found':
        return value
    try:
        # Remove any existing formatting and convert to float
        if isinstance(value, str):
            clean_value = value.replace(',', '').replace('$', '').replace('(', '').replace(')', '')
            if clean_value == '-' or clean_value.lower() == 'n/a':
                return value
            num_value = float(clean_value)
        else:
            num_value = float(value)

        # Convert to thousands and format with commas
        thousands_value = int(num_value)
        return f"${thousands_value:,},000"
    except (ValueError, TypeError):
        return value

def add_company_info(results, company_lookup):
    """Add company information to results"""
    enhanced_results = []
    for result in results:
        asx_code = result['asx_code']
        if asx_code in company_lookup:
            company_info = company_lookup[asx_code]
            enhanced_result = {
                'asx_code': asx_code,
                'company_name': company_info['Company name'],
                'gics_industry_group': company_info['GICs industry group'],
                'listing_date': company_info['Listing date'],
                'market_cap': company_info['Market Cap'],
                **{k: v for k, v in result.items() if k != 'asx_code'}
            }
            enhanced_results.append(enhanced_result)
        else:
            # If company info not found, add empty fields
            enhanced_result = {
                'asx_code': asx_code,
                'company_name': 'Not found',
                'gics_industry_group': 'Not found',
                'listing_date': 'Not found',
                'market_cap': 'Not found',
                **{k: v for k, v in result.items() if k != 'asx_code'}
            }
            enhanced_results.append(enhanced_result)
    return enhanced_results

def format_financial_values_4c(results):
    """Format 4C financial values (8.1-8.4 as currency, 8.5 as-is)"""
    formatted_results = []
    for result in results.copy():
        result['net_cash_operating_activities'] = format_currency(result.get('net_cash_operating_activities', 'Not found'))
        result['cash_and_cash_equivalents'] = format_currency(result.get('cash_and_cash_equivalents', 'Not found'))
        result['unused_finance_facilities'] = format_currency(result.get('unused_finance_facilities', 'Not found'))
        result['total_available_funding'] = format_currency(result.get('total_available_funding', 'Not found'))
        # Leave 8.5 (estimated_quarters_funding) as-is
        formatted_results.append(result)
    return formatted_results

def format_financial_values_5b(results):
    """Format 5B financial values (8.1-8.6 as currency, 8.7 as-is)"""
    formatted_results = []
    for result in results.copy():
        result['net_cash_operating_activities'] = format_currency(result.get('net_cash_operating_activities', 'Not found'))
        result['payments_exploration_evaluation'] = format_currency(result.get('payments_exploration_evaluation', 'Not found'))
        result['total_relevant_outgoings'] = format_currency(result.get('total_relevant_outgoings', 'Not found'))
        result['cash_and_cash_equivalents'] = format_currency(result.get('cash_and_cash_equivalents', 'Not found'))
        result['unused_finance_facilities'] = format_currency(result.get('unused_finance_facilities', 'Not found'))
        result['total_available_funding'] = format_currency(result.get('total_available_funding', 'Not found'))
        # Leave 8.7 (estimated_quarters_funding) as-is
        formatted_results.append(result)
    return formatted_results

def safe_download_asx_directory():
    """Safely download ASX directory with error handling"""
    try:
        from sync.download_csv import download_asx_directory
        return download_asx_directory()
    except Exception as e:
        print(f"Error downloading ASX directory: {e}")
        return None

def safe_scrape_announcements(asx_codes):
    """Safely scrape announcements with error handling"""
    try:
        from sync.announcement_scraper import scrape_announcements
        return scrape_announcements(asx_codes)
    except Exception as e:
        print(f"Error scraping announcements: {e}")
        return [], [], []
