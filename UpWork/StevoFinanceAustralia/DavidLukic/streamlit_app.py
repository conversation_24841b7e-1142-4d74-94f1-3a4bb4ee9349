import streamlit as st
import pandas as pd
import os
import sys
import subprocess
import time
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import glob
import json

# Import custom modules
from streamlit_utils import (
    create_financial_overview_chart, create_cash_flow_trend_chart,
    create_industry_analysis_chart, create_funding_quarters_distribution,
    create_company_comparison_chart, create_correlation_heatmap,
    create_summary_metrics, create_time_series_chart
)
from streamlit_scraper import (
    run_scraper_with_progress, validate_scraper_config,
    get_asx_codes_from_input, save_scraper_config, load_scraper_config,
    get_saved_configs, create_scraper_summary_card
)

# Add the sync directory to the path to import modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'sync'))

# Page configuration
st.set_page_config(
    page_title="ASX Data Scraper & Analyzer",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .status-running {
        color: #ff6b6b;
        font-weight: bold;
    }
    .status-complete {
        color: #51cf66;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def main():
    st.markdown('<h1 class="main-header">📊 ASX Data Scraper & Analyzer</h1>', unsafe_allow_html=True)
    
    # Sidebar navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["🏠 Home", "🔍 Data Scraper", "📈 Data Visualization", "⚙️ Settings"]
    )
    
    if page == "🏠 Home":
        show_home_page()
    elif page == "🔍 Data Scraper":
        show_scraper_page()
    elif page == "📈 Data Visualization":
        show_visualization_page()
    elif page == "⚙️ Settings":
        show_settings_page()

def show_home_page():
    st.header("Welcome to ASX Data Scraper & Analyzer")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📋 What this app does:")
        st.markdown("""
        - **Scrape ASX announcements** for 4C, 5B, and options reports
        - **Extract financial data** from PDF documents
        - **Visualize trends** and patterns in the data
        - **Export results** to CSV files with timestamps
        """)
        
        st.subheader("🚀 Quick Start:")
        st.markdown("""
        1. Go to **Data Scraper** to configure and run scraping
        2. Choose report types (4C, 5B, Options)
        3. Set date range for data collection
        4. View results in **Data Visualization**
        """)
    
    with col2:
        st.subheader("📊 Recent Results:")
        
        # Show available result folders
        result_folders = get_result_folders()
        if result_folders:
            st.write(f"Found {len(result_folders)} result folders:")
            for folder in result_folders[:5]:  # Show last 5
                folder_name = os.path.basename(folder)
                st.write(f"• {folder_name}")
                
            if len(result_folders) > 5:
                st.write(f"... and {len(result_folders) - 5} more")
        else:
            st.info("No results found yet. Run the scraper to generate data!")

def show_scraper_page():
    st.header("🔍 ASX Data Scraper")

    # Saved configurations
    st.subheader("⚙️ Configuration Management")

    col1, col2, col3 = st.columns(3)

    with col1:
        saved_configs = get_saved_configs()
        if saved_configs:
            selected_config = st.selectbox("Load saved configuration:", ["None"] + saved_configs)
            if selected_config != "None":
                config = load_scraper_config(selected_config)
                if config:
                    st.session_state.update(config)
                    st.success(f"Loaded configuration: {selected_config}")

    with col2:
        config_name = st.text_input("Save current config as:", placeholder="my_config")
        if st.button("💾 Save Config") and config_name:
            current_config = {
                'scrape_4c': st.session_state.get('scrape_4c', True),
                'scrape_5b': st.session_state.get('scrape_5b', True),
                'scrape_options': st.session_state.get('scrape_options', True),
                'selected_codes': st.session_state.get('selected_codes', [])
            }
            save_scraper_config(current_config, config_name)
            st.success(f"Configuration saved as: {config_name}")

    with col3:
        if st.button("🔄 Reset to Defaults"):
            for key in ['scrape_4c', 'scrape_5b', 'scrape_options', 'selected_codes']:
                if key in st.session_state:
                    del st.session_state[key]
            st.rerun()

    # Configuration section
    st.subheader("📋 Scraper Configuration")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Report Types to Scrape:**")
        scrape_4c = st.checkbox("Appendix 4C Reports",
                               value=st.session_state.get('scrape_4c', True),
                               help="Quarterly cash flow reports",
                               key='scrape_4c')
        scrape_5b = st.checkbox("Appendix 5B Reports",
                               value=st.session_state.get('scrape_5b', True),
                               help="Mining exploration reports",
                               key='scrape_5b')
        scrape_options = st.checkbox("Options Reports (2A/3B/3G)",
                                   value=st.session_state.get('scrape_options', True),
                                   help="Unquoted option reports",
                                   key='scrape_options')

    with col2:
        st.markdown("**Date Range:**")
        date_range = st.selectbox(
            "Select timeframe:",
            ["All available", "Last 30 days", "Last 90 days", "Last 6 months", "Last year", "Custom range"]
        )

        if date_range == "Custom range":
            start_date = st.date_input("Start date", value=datetime.now() - timedelta(days=90))
            end_date = st.date_input("End date", value=datetime.now())

    # ASX Codes selection
    st.subheader("🏢 ASX Codes Selection")

    code_selection = st.radio(
        "Choose how to select ASX codes:",
        ["All ASX codes", "Specific codes", "Upload CSV file"]
    )

    selected_codes = []

    if code_selection == "All ASX codes":
        st.info("Will scrape all available ASX codes from the directory")
        selected_codes = "all"
    elif code_selection == "Specific codes":
        codes_input = st.text_area(
            "Enter ASX codes (one per line or comma-separated):",
            placeholder="CBA\nBHP\nCSL\nor\nCBA, BHP, CSL",
            value=st.session_state.get('codes_input', '')
        )
        if codes_input:
            selected_codes = get_asx_codes_from_input(codes_input)
            st.write(f"Selected {len(selected_codes)} codes: {', '.join(selected_codes[:10])}")
            if len(selected_codes) > 10:
                st.write(f"... and {len(selected_codes) - 10} more")
    elif code_selection == "Upload CSV file":
        uploaded_file = st.file_uploader("Choose a CSV file with ASX codes", type="csv")
        if uploaded_file:
            df = pd.read_csv(uploaded_file)
            st.write("CSV Preview:")
            st.dataframe(df.head())

            # Let user select which column contains ASX codes
            code_column = st.selectbox("Select column with ASX codes:", df.columns)
            if code_column:
                selected_codes = df[code_column].dropna().astype(str).str.upper().tolist()
                st.write(f"Found {len(selected_codes)} ASX codes")

    # Configuration summary
    if selected_codes:
        config = {
            'selected_codes': selected_codes,
            'scrape_4c': scrape_4c,
            'scrape_5b': scrape_5b,
            'scrape_options': scrape_options,
            'date_range': date_range
        }

        create_scraper_summary_card(config)

        # Validate configuration
        errors = validate_scraper_config(config)
        if errors:
            for error in errors:
                st.error(f"❌ {error}")

        # Start scraping button
        st.subheader("🚀 Run Scraper")

        # Add comprehensive note about scraping options
        st.warning("⚠️ **Chrome Driver Issue Detected**: Live scraping requires Chrome browser setup. Use alternatives below:")

        with st.expander("🔧 Chrome Setup Instructions (Optional)"):
            st.markdown("""
            **To enable live scraping, install Chrome browser:**

            ```bash
            # Ubuntu/Debian
            sudo apt update
            sudo apt install google-chrome-stable

            # Or download from: https://www.google.com/chrome/
            ```

            **Install Chrome dependencies:**
            ```bash
            sudo apt install -y libnss3 libgconf-2-4 libxss1 libappindicator1 \\
                libindicator7 fonts-liberation libasound2 libatk-bridge2.0-0 \\
                libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 \\
                libxkbcommon0 libgtk-3-0
            ```

            **Test Chrome setup:**
            ```bash
            python test_chromedriver.py
            ```
            """)

        # Recommended alternatives section
        st.subheader("🎯 Recommended Options")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**🎭 Generate Demo Data (Recommended)**")
            st.write("Create realistic sample data for testing all features")
            if st.button("🎭 Create Demo Data", type="primary", key="demo_main"):
                with st.spinner("Generating demo data..."):
                    try:
                        import subprocess
                        result = subprocess.run([sys.executable, 'demo_data_generator.py'],
                                              capture_output=True, text=True, cwd=os.path.dirname(__file__))
                        if result.returncode == 0:
                            st.success("✅ Demo data generated successfully!")
                            st.info("📊 Go to 'Data Visualization' to explore the demo data.")
                            st.balloons()
                        else:
                            st.error(f"Demo data generation failed: {result.stderr}")
                    except Exception as e:
                        st.error(f"Error generating demo data: {e}")

        with col2:
            st.markdown("**📊 Use Existing Data**")
            result_folders = get_result_folders()
            if result_folders:
                st.write(f"Found {len(result_folders)} existing result folders")
                st.write("📈 Go to 'Data Visualization' to explore them")

                # Show latest folder
                latest_folder = os.path.basename(result_folders[0])
                st.info(f"Latest: {latest_folder}")
            else:
                st.write("No existing result folders found")

        # Advanced options (collapsed by default)
        with st.expander("🔧 Advanced Scraping Options (Requires Chrome Setup)"):
            if not errors:
                st.warning("⚠️ These options require Chrome browser to be properly installed")

                col_a, col_b = st.columns(2)

                with col_a:
                    if st.button("🚀 Try Chrome Scraping", type="secondary"):
                        with st.spinner("Testing Chrome scraper..."):
                            result = run_scraper_with_progress(config)
                            if result:
                                st.session_state['last_scraping_result'] = result
                            else:
                                st.error("Chrome scraping failed. Install Chrome browser first.")

                with col_b:
                    if st.button("📜 Run Command Line", type="secondary"):
                        st.info("💡 **Manual Alternative:**")
                        if config['selected_codes'] == 'all':
                            st.code("cd sync && python main_sync.py", language="bash")
                        else:
                            codes_str = ', '.join(config['selected_codes'][:5])
                            if len(config['selected_codes']) > 5:
                                codes_str += f" (and {len(config['selected_codes']) - 5} more)"
                            st.code("cd sync && python main_sync.py", language="bash")
                            st.write(f"Selected codes: {codes_str}")
                        st.write("Run this command in your terminal")
            else:
                st.error("Please fix configuration errors first")
        else:
            st.button("🚀 Start Scraping", type="primary", disabled=True)
    else:
        st.warning("⚠️ Please select ASX codes to scrape")

        # Suggest alternatives
        st.markdown("### 💡 Alternative Options:")
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**📊 Use Existing Data:**")
            result_folders = get_result_folders()
            if result_folders:
                st.write(f"Found {len(result_folders)} existing result folders")
                st.write("Go to 'Data Visualization' to explore them")
            else:
                st.write("No existing result folders found")

        with col2:
            st.markdown("**🎭 Generate Demo Data:**")
            if st.button("Create Demo Data", key="demo_alt"):
                with st.spinner("Generating demo data..."):
                    try:
                        import subprocess
                        result = subprocess.run([sys.executable, 'demo_data_generator.py'],
                                              capture_output=True, text=True, cwd=os.path.dirname(__file__))
                        if result.returncode == 0:
                            st.success("✅ Demo data generated!")
                            st.rerun()
                        else:
                            st.error("Demo generation failed")
                    except Exception as e:
                        st.error(f"Error: {e}")



def show_visualization_page():
    st.header("📈 Data Visualization")
    
    # Folder selection
    st.subheader("Select Data Folder")
    
    result_folders = get_result_folders()
    
    if not result_folders:
        st.warning("No result folders found. Please run the scraper first.")
        return
    
    # Let user select folder
    folder_names = [os.path.basename(folder) for folder in result_folders]
    selected_folder_name = st.selectbox("Choose a results folder:", folder_names)
    
    if selected_folder_name:
        selected_folder = next(folder for folder in result_folders if os.path.basename(folder) == selected_folder_name)
        
        # Load and display data
        load_and_visualize_data(selected_folder)

def load_and_visualize_data(folder_path):
    """Load data from selected folder and create visualizations"""
    st.subheader(f"Data from: {os.path.basename(folder_path)}")
    
    # Check available files
    files = os.listdir(folder_path)
    csv_files = [f for f in files if f.endswith('.csv')]
    
    if not csv_files:
        st.error("No CSV files found in the selected folder")
        return
    
    # Tabs for different visualizations
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Overview", "💰 Financial Metrics", "📈 Trends", "📋 Raw Data"])
    
    with tab1:
        show_overview_tab(folder_path, csv_files)
    
    with tab2:
        show_financial_metrics_tab(folder_path, csv_files)
    
    with tab3:
        show_trends_tab(folder_path, csv_files)
    
    with tab4:
        show_raw_data_tab(folder_path, csv_files)

def show_overview_tab(folder_path, csv_files):
    """Show overview statistics"""
    st.subheader("📊 Data Overview")
    
    # Load data from available files
    data_summary = {}
    
    for file in csv_files:
        if file.startswith('financial_data_4C'):
            df = pd.read_csv(os.path.join(folder_path, file))
            data_summary['4C Reports'] = len(df)
        elif file.startswith('financial_data_5B'):
            df = pd.read_csv(os.path.join(folder_path, file))
            data_summary['5B Reports'] = len(df)
        elif file.startswith('unquoted_option'):
            df = pd.read_csv(os.path.join(folder_path, file))
            data_summary['Options Reports'] = len(df)
    
    # Display metrics
    cols = st.columns(len(data_summary))
    for i, (key, value) in enumerate(data_summary.items()):
        with cols[i]:
            st.metric(key, value)

def show_financial_metrics_tab(folder_path, csv_files):
    """Show financial metrics visualizations"""
    st.subheader("💰 Financial Metrics Analysis")

    # Load financial data
    financial_data = {}

    for file in csv_files:
        if file.startswith('financial_data_4C'):
            df = pd.read_csv(os.path.join(folder_path, file))
            financial_data['4C'] = df
        elif file.startswith('financial_data_5B'):
            df = pd.read_csv(os.path.join(folder_path, file))
            financial_data['5B'] = df

    if not financial_data:
        st.warning("No financial data files found")
        return

    # Report type selection
    available_types = list(financial_data.keys())
    selected_type = st.selectbox("Select report type:", available_types)

    if selected_type:
        df = financial_data[selected_type]

        # Create summary metrics
        metrics = create_summary_metrics(df, selected_type)

        if metrics:
            st.markdown("#### 📊 Key Metrics")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Total Reports", metrics.get('total_reports', 'N/A'))
            with col2:
                st.metric("Unique Companies", metrics.get('unique_companies', 'N/A'))
            with col3:
                st.metric("Avg Cash Position", metrics.get('avg_cash_position', 'N/A'))
            with col4:
                st.metric("Positive Cash Flow %", metrics.get('positive_cash_flow_pct', 'N/A'))

        # Financial overview chart
        st.markdown("#### 📈 Financial Overview")
        overview_chart = create_financial_overview_chart(df, selected_type)
        if overview_chart:
            st.plotly_chart(overview_chart, use_container_width=True)
        else:
            st.info("No data available for financial overview chart")

        # Company comparison
        st.markdown("#### 🏢 Top Companies by Cash Position")
        comparison_chart = create_company_comparison_chart(df)
        if comparison_chart:
            st.plotly_chart(comparison_chart, use_container_width=True)
        else:
            st.info("No data available for company comparison")

        # Funding quarters distribution (if available)
        if 'estimated_quarters_funding' in df.columns:
            st.markdown("#### ⏱️ Funding Quarters Distribution")
            quarters_chart = create_funding_quarters_distribution(df)
            if quarters_chart:
                st.plotly_chart(quarters_chart, use_container_width=True)
            else:
                st.info("No funding quarters data available")

        # Correlation analysis
        st.markdown("#### 🔗 Financial Metrics Correlation")
        corr_chart = create_correlation_heatmap(df)
        if corr_chart:
            st.plotly_chart(corr_chart, use_container_width=True)
        else:
            st.info("Insufficient data for correlation analysis")

def show_trends_tab(folder_path, csv_files):
    """Show trend analysis"""
    st.subheader("📈 Trend Analysis")

    # Load financial data
    financial_data = {}

    for file in csv_files:
        if file.startswith('financial_data_4C'):
            df = pd.read_csv(os.path.join(folder_path, file))
            financial_data['4C'] = df
        elif file.startswith('financial_data_5B'):
            df = pd.read_csv(os.path.join(folder_path, file))
            financial_data['5B'] = df

    if not financial_data:
        st.warning("No financial data files found")
        return

    # Report type selection
    available_types = list(financial_data.keys())
    selected_type = st.selectbox("Select report type for trend analysis:", available_types)

    if selected_type:
        df = financial_data[selected_type]

        # Cash flow trend
        st.markdown("#### 💰 Cash Flow Trend Over Time")
        cash_flow_chart = create_cash_flow_trend_chart(df)
        if cash_flow_chart:
            st.plotly_chart(cash_flow_chart, use_container_width=True)
        else:
            st.info("No cash flow trend data available")

        # Industry analysis
        st.markdown("#### 🏭 Industry Analysis")
        industry_chart = create_industry_analysis_chart(df)
        if industry_chart:
            st.plotly_chart(industry_chart, use_container_width=True)
        else:
            st.info("No industry data available")

        # Time series for different metrics
        st.markdown("#### 📊 Metric Time Series")

        # Metric selection
        available_metrics = []
        if 'net_cash_operating_activities' in df.columns:
            available_metrics.append(('net_cash_operating_activities', 'Net Cash from Operations'))
        if 'cash_and_cash_equivalents' in df.columns:
            available_metrics.append(('cash_and_cash_equivalents', 'Cash & Equivalents'))
        if 'total_available_funding' in df.columns:
            available_metrics.append(('total_available_funding', 'Total Available Funding'))
        if 'estimated_quarters_funding' in df.columns:
            available_metrics.append(('estimated_quarters_funding', 'Estimated Quarters Funding'))

        if available_metrics:
            selected_metric = st.selectbox(
                "Select metric for time series:",
                available_metrics,
                format_func=lambda x: x[1]
            )

            if selected_metric:
                metric_column, metric_title = selected_metric
                time_series_chart = create_time_series_chart(df, metric_column, f"{metric_title} Over Time")
                if time_series_chart:
                    st.plotly_chart(time_series_chart, use_container_width=True)
                else:
                    st.info(f"No data available for {metric_title} time series")
        else:
            st.info("No metrics available for time series analysis")

def show_raw_data_tab(folder_path, csv_files):
    """Show raw data tables"""
    st.subheader("📋 Raw Data")
    
    # Let user select which file to view
    selected_file = st.selectbox("Select file to view:", csv_files)
    
    if selected_file:
        df = pd.read_csv(os.path.join(folder_path, selected_file))
        
        st.write(f"**{selected_file}** ({len(df)} rows)")
        
        # Search and filter options
        col1, col2 = st.columns(2)
        with col1:
            search_term = st.text_input("Search in data:")
        with col2:
            show_rows = st.number_input("Rows to display:", min_value=10, max_value=1000, value=100)
        
        # Filter data if search term provided
        if search_term:
            mask = df.astype(str).apply(lambda x: x.str.contains(search_term, case=False, na=False)).any(axis=1)
            df_filtered = df[mask]
            st.write(f"Found {len(df_filtered)} matching rows")
        else:
            df_filtered = df
        
        # Display data
        st.dataframe(df_filtered.head(show_rows), use_container_width=True)
        
        # Download button
        csv = df_filtered.to_csv(index=False)
        st.download_button(
            label="📥 Download filtered data as CSV",
            data=csv,
            file_name=f"filtered_{selected_file}",
            mime="text/csv"
        )

def show_settings_page():
    """Show settings and configuration"""
    st.header("⚙️ Settings")
    
    st.subheader("Application Settings")
    
    # Theme settings
    st.markdown("**Display Settings:**")
    theme = st.selectbox("Theme:", ["Light", "Dark", "Auto"])
    
    # Scraping settings
    st.markdown("**Scraping Settings:**")
    delay_between_requests = st.slider("Delay between requests (seconds):", 1, 10, 3)
    timeout_duration = st.slider("Request timeout (seconds):", 10, 60, 30)
    
    # Data settings
    st.markdown("**Data Settings:**")
    auto_cleanup = st.checkbox("Auto-cleanup old result folders", value=False)
    if auto_cleanup:
        cleanup_days = st.number_input("Keep results for (days):", min_value=1, max_value=365, value=30)
    
    # Save settings
    if st.button("💾 Save Settings"):
        settings = {
            'theme': theme,
            'delay_between_requests': delay_between_requests,
            'timeout_duration': timeout_duration,
            'auto_cleanup': auto_cleanup,
            'cleanup_days': cleanup_days if auto_cleanup else None
        }
        save_settings(settings)
        st.success("Settings saved successfully!")

def get_result_folders():
    """Get list of result folders"""
    base_dir = os.path.dirname(__file__)
    pattern = os.path.join(base_dir, "results_*")
    folders = glob.glob(pattern)
    # Sort by modification time (newest first)
    folders.sort(key=os.path.getmtime, reverse=True)
    return folders

def save_settings(settings):
    """Save settings to file"""
    settings_file = os.path.join(os.path.dirname(__file__), 'app_settings.json')
    with open(settings_file, 'w') as f:
        json.dump(settings, f, indent=2)

def load_settings():
    """Load settings from file"""
    settings_file = os.path.join(os.path.dirname(__file__), 'app_settings.json')
    if os.path.exists(settings_file):
        with open(settings_file, 'r') as f:
            return json.load(f)
    return {}

if __name__ == "__main__":
    main()
