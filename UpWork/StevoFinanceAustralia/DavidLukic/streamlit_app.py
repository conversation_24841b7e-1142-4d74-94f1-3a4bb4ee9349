import streamlit as st
import pandas as pd
import os
import sys
import subprocess
import time
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import glob
import json

# Import custom modules
from streamlit_utils import (
    create_financial_overview_chart, create_cash_flow_trend_chart,
    create_industry_analysis_chart, create_funding_quarters_distribution,
    create_company_comparison_chart, create_correlation_heatmap,
    create_summary_metrics, create_time_series_chart
)
from streamlit_scraper import (
    run_scraper_with_progress, validate_scraper_config,
    get_asx_codes_from_input, save_scraper_config, load_scraper_config,
    get_saved_configs, create_scraper_summary_card
)

# Add the sync directory to the path to import modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'sync'))

# Page configuration
st.set_page_config(
    page_title="ASX Data Scraper & Analyzer",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced Custom CSS for stunning UI
st.markdown("""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* Global Styles */
    .stApp {
        font-family: 'Inter', sans-serif;
    }

    /* Main Header */
    .main-header {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
        margin-bottom: 2rem;
        padding: 1rem 0;
    }

    /* Subtitle */
    .subtitle {
        font-size: 1.2rem;
        color: #6c757d;
        text-align: center;
        margin-bottom: 3rem;
        font-weight: 400;
    }

    /* Enhanced Metric Cards */
    .metric-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        border-radius: 1rem;
        border: 1px solid #dee2e6;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    /* Status Indicators */
    .status-running {
        color: #ff6b6b;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .status-complete {
        color: #51cf66;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .status-warning {
        color: #ffd43b;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Enhanced Buttons */
    .stButton > button {
        border-radius: 0.75rem;
        border: none;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .stButton > button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Primary Button Gradient */
    .stButton > button[kind="primary"] {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    /* Secondary Button */
    .stButton > button[kind="secondary"] {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    /* Navigation Buttons */
    .nav-button {
        width: 100%;
        padding: 1rem;
        margin: 0.5rem 0;
        border: none;
        border-radius: 0.75rem;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: left;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .nav-button:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .nav-button.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .nav-button .icon {
        font-size: 1.2rem;
        width: 1.5rem;
        text-align: center;
    }

    /* Developer Section */
    .developer-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 1rem;
        margin: 1rem 0;
        text-align: center;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .developer-card h4 {
        margin: 0 0 1rem 0;
        font-size: 1.1rem;
    }

    .developer-card a {
        color: #ffffff;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .developer-card a:hover {
        color: #ffd43b;
        text-decoration: underline;
    }

    .contact-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.25rem 0;
        padding: 0.5rem;
        border-radius: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }

    .contact-link:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
    }

    /* Success Button */
    .success-button {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    /* Card Containers */
    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 1rem;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    /* Progress Bar Enhancement */
    .stProgress > div > div > div {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 1rem;
    }

    /* Sidebar Enhancement */
    .css-1d391kg {
        background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    }

    /* Info Boxes */
    .info-box {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        padding: 1rem;
        border-radius: 0.75rem;
        border-left: 4px solid #2196f3;
        margin: 1rem 0;
        color: #000000;
    }

    .info-box p {
        color: #000000 !important;
        font-weight: 500;
    }

    .warning-box {
        background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        padding: 1rem;
        border-radius: 0.75rem;
        border-left: 4px solid #ff9800;
        margin: 1rem 0;
        color: #000000;
    }

    .warning-box p {
        color: #000000 !important;
        font-weight: 500;
    }

    .success-box {
        background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        padding: 1rem;
        border-radius: 0.75rem;
        border-left: 4px solid #4caf50;
        margin: 1rem 0;
        color: #000000;
    }

    .success-box p {
        color: #000000 !important;
        font-weight: 500;
    }

    /* Tab Enhancement */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }

    .stTabs [data-baseweb="tab"] {
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
    }

    /* Selectbox Enhancement */
    .stSelectbox > div > div {
        border-radius: 0.75rem;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .stSelectbox > div > div:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Text Input Enhancement */
    .stTextInput > div > div > input {
        border-radius: 0.75rem;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .stTextInput > div > div > input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Checkbox Enhancement */
    .stCheckbox > label {
        font-weight: 500;
        color: #495057;
    }

    /* Animation Classes */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .pulse {
        animation: pulse 2s infinite;
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        .feature-card {
            background: #2d3748;
            border-color: #4a5568;
            color: #e2e8f0;
        }

        .metric-card {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: #e2e8f0;
        }
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Enhanced Header with Animation
    st.markdown("""
    <div class="fade-in-up">
        <h1 class="main-header">📊 ASX Data Scraper & Analyzer</h1>
        <p class="subtitle">Professional Financial Data Analysis Platform</p>
    </div>
    """, unsafe_allow_html=True)

    # Enhanced Sidebar with Beautiful Button Navigation
    with st.sidebar:
        st.markdown("""
        <div style="text-align: center; padding: 1rem 0;">
            <h2 style="color: #667eea; font-weight: 600; margin-bottom: 0.5rem;">🚀 Navigation</h2>
            <p style="color: #6c757d; font-size: 0.9rem;">Choose your destination</p>
        </div>
        """, unsafe_allow_html=True)

        # Handle navigation state
        if 'navigate_to' in st.session_state:
            current_page = st.session_state['navigate_to']
            del st.session_state['navigate_to']
        elif 'current_page' not in st.session_state:
            current_page = "🏠 Home"
        else:
            current_page = st.session_state['current_page']

        # Beautiful button navigation
        pages = [
            ("🏠", "Home", "🏠 Home"),
            ("🔍", "Data Scraper", "🔍 Data Scraper"),
            ("📈", "Visualization", "📈 Data Visualization"),
            ("⚙️", "Settings", "⚙️ Settings")
        ]

        for icon, title, page_key in pages:
            is_active = current_page == page_key
            button_class = "active" if is_active else ""

            # Create custom button HTML
            button_html = f"""
            <div class="nav-button {button_class}" onclick="this.style.background='linear-gradient(135deg, #667eea 0%, #764ba2 100%)'; this.style.color='white';">
                <span class="icon">{icon}</span>
                <span>{title}</span>
            </div>
            """

            # Use columns to create clickable area
            if st.button(f"{icon} {title}", key=f"nav_{page_key}", use_container_width=True):
                st.session_state['current_page'] = page_key
                st.rerun()

        page = current_page

        # Developer Section
        st.markdown("---")
        st.markdown("""
        <div class="developer-card">
            <h4>👨‍💻 About Developer</h4>
            <p style="margin: 0.5rem 0; font-size: 0.9rem;">Built with ❤️ by David Lukic</p>

            <div style="margin: 1rem 0;">
                <a href="https://davidlukic99.github.io/" target="_blank" class="contact-link">
                    🌐 Portfolio Website
                </a>
            </div>

            <div style="margin: 1rem 0;">
                <a href="mailto:<EMAIL>" class="contact-link">
                    📧 <EMAIL>
                </a>
            </div>

            <p style="font-size: 0.8rem; margin-top: 1rem; opacity: 0.9;">
                Professional ASX Data Scraping & Analysis Platform
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Add sidebar stats
        st.markdown("### 📊 Quick Stats")

        result_folders = get_result_folders()
        col1, col2 = st.columns(2)
        with col1:
            st.metric("📁 Results", len(result_folders))
        with col2:
            # Count total CSV files
            total_files = 0
            for folder in result_folders:
                try:
                    files = os.listdir(folder)
                    total_files += len([f for f in files if f.endswith('.csv')])
                except:
                    pass
            st.metric("📄 Files", total_files)

        # Add system status
        st.markdown("### 🔧 System Status")

        # Check Chrome driver status
        try:
            from scraper_functions import get_correct_chromedriver_path
            driver_path = get_correct_chromedriver_path()
            if driver_path and os.path.exists(driver_path):
                st.success("✅ Chrome Driver Ready")
            else:
                st.warning("⚠️ Chrome Driver Issues")
        except:
            st.error("❌ Chrome Driver Error")

        # Add quick actions
        st.markdown("### ⚡ Quick Actions")
        if st.button("🎭 Generate Demo Data", key="sidebar_demo"):
            with st.spinner("Creating demo data..."):
                try:
                    import subprocess
                    result = subprocess.run([sys.executable, 'demo_data_generator.py'],
                                          capture_output=True, text=True, cwd=os.path.dirname(__file__))
                    if result.returncode == 0:
                        st.success("✅ Demo data created!")
                        st.rerun()
                    else:
                        st.error("❌ Failed to create demo data")
                except Exception as e:
                    st.error(f"Error: {e}")

        # Add footer
        st.markdown("---")
        st.markdown("""
        <div style="text-align: center; color: #6c757d; font-size: 0.8rem; padding: 1rem 0;">
            <p>🚀 Powered by Streamlit</p>
            <p>📊 ASX Data Analytics</p>
        </div>
        """, unsafe_allow_html=True)
    
    if page == "🏠 Home":
        show_home_page()
    elif page == "🔍 Data Scraper":
        show_scraper_page()
    elif page == "📈 Data Visualization":
        show_visualization_page()
    elif page == "⚙️ Settings":
        show_settings_page()

def show_home_page():
    # Hero Section
    st.markdown("""
    <div class="fade-in-up" style="text-align: center; padding: 2rem 0;">
        <h2 style="color: #495057; font-weight: 300; font-size: 1.5rem; margin-bottom: 3rem;">
            Transform ASX financial data into actionable insights with our professional-grade analytics platform
        </h2>
    </div>
    """, unsafe_allow_html=True)

    # Feature Cards
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class="feature-card fade-in-up">
            <div style="text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
                <h3 style="color: #667eea; margin-bottom: 1rem;">Smart Scraping</h3>
                <p style="color: #6c757d; line-height: 1.6;">
                    Intelligent extraction of 4C, 5B, and options reports with real-time progress tracking
                </p>
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="feature-card fade-in-up" style="animation-delay: 0.2s;">
            <div style="text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
                <h3 style="color: #667eea; margin-bottom: 1rem;">Advanced Analytics</h3>
                <p style="color: #6c757d; line-height: 1.6;">
                    Interactive visualizations, trend analysis, and comprehensive financial insights
                </p>
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="feature-card fade-in-up" style="animation-delay: 0.4s;">
            <div style="text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">⚡</div>
                <h3 style="color: #667eea; margin-bottom: 1rem;">Lightning Fast</h3>
                <p style="color: #6c757d; line-height: 1.6;">
                    Optimized performance with headless browsing and efficient data processing
                </p>
            </div>
        </div>
        """, unsafe_allow_html=True)

    # Quick Start Section
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; padding: 2rem 0;">
        <h2 style="color: #495057; font-weight: 600; margin-bottom: 2rem;">🚀 Get Started in 3 Easy Steps</h2>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class="info-box" style="text-align: center;">
            <div style="font-size: 2rem; margin-bottom: 1rem;">1️⃣</div>
            <h4 style="color: #2196f3; margin-bottom: 1rem;">Configure</h4>
            <p style="color: #000000; font-weight: 500;">Select ASX codes and report types in the Data Scraper</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="warning-box" style="text-align: center;">
            <div style="font-size: 2rem; margin-bottom: 1rem;">2️⃣</div>
            <h4 style="color: #ff9800; margin-bottom: 1rem;">Execute</h4>
            <p style="color: #000000; font-weight: 500;">Run the scraper with real-time progress monitoring</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="success-box" style="text-align: center;">
            <div style="font-size: 2rem; margin-bottom: 1rem;">3️⃣</div>
            <h4 style="color: #4caf50; margin-bottom: 1rem;">Analyze</h4>
            <p style="color: #000000; font-weight: 500;">Explore interactive visualizations and insights</p>
        </div>
        """, unsafe_allow_html=True)

    # Recent Results Dashboard
    st.markdown("---")
    st.markdown("""
    <h2 style="color: #495057; font-weight: 600; margin: 2rem 0 1rem 0;">📊 Data Overview</h2>
    """, unsafe_allow_html=True)

    result_folders = get_result_folders()

    if result_folders:
        # Stats Overview
        col1, col2, col3, col4 = st.columns(4)

        total_files = 0
        total_4c = 0
        total_5b = 0
        total_options = 0

        for folder in result_folders:
            try:
                files = os.listdir(folder)
                csv_files = [f for f in files if f.endswith('.csv')]
                total_files += len(csv_files)

                for file in csv_files:
                    if '4C' in file:
                        try:
                            df = pd.read_csv(os.path.join(folder, file))
                            total_4c += len(df)
                        except:
                            pass
                    elif '5B' in file:
                        try:
                            df = pd.read_csv(os.path.join(folder, file))
                            total_5b += len(df)
                        except:
                            pass
                    elif 'option' in file.lower():
                        try:
                            df = pd.read_csv(os.path.join(folder, file))
                            total_options += len(df)
                        except:
                            pass
            except:
                pass

        with col1:
            st.markdown("""
            <div class="metric-card">
                <h3 style="color: #667eea; margin: 0; font-size: 2rem;">{}</h3>
                <p style="color: #6c757d; margin: 0.5rem 0 0 0;">Result Folders</p>
            </div>
            """.format(len(result_folders)), unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div class="metric-card">
                <h3 style="color: #28a745; margin: 0; font-size: 2rem;">{}</h3>
                <p style="color: #6c757d; margin: 0.5rem 0 0 0;">4C Reports</p>
            </div>
            """.format(total_4c), unsafe_allow_html=True)

        with col3:
            st.markdown("""
            <div class="metric-card">
                <h3 style="color: #ffc107; margin: 0; font-size: 2rem;">{}</h3>
                <p style="color: #6c757d; margin: 0.5rem 0 0 0;">5B Reports</p>
            </div>
            """.format(total_5b), unsafe_allow_html=True)

        with col4:
            st.markdown("""
            <div class="metric-card">
                <h3 style="color: #dc3545; margin: 0; font-size: 2rem;">{}</h3>
                <p style="color: #6c757d; margin: 0.5rem 0 0 0;">Options Reports</p>
            </div>
            """.format(total_options), unsafe_allow_html=True)

        # Recent Folders
        st.markdown("### 📁 Recent Result Folders")
        for i, folder in enumerate(result_folders[:3]):
            folder_name = os.path.basename(folder)
            col_a, col_b = st.columns([3, 1])
            with col_a:
                st.write(f"📂 **{folder_name}**")
            with col_b:
                if st.button("View", key=f"view_{i}"):
                    st.session_state['selected_folder'] = folder_name
                    st.session_state['navigate_to'] = "📈 Data Visualization"
                    st.rerun()
    else:
        st.markdown("""
        <div class="info-box" style="text-align: center; padding: 2rem;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🎭</div>
            <h3 style="color: #2196f3; margin-bottom: 1rem;">No Data Yet</h3>
            <p style="margin-bottom: 2rem;">Get started by generating demo data or running the scraper</p>
        </div>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🎭 Generate Demo Data", type="primary", use_container_width=True):
                with st.spinner("Creating demo data..."):
                    try:
                        import subprocess
                        result = subprocess.run([sys.executable, 'demo_data_generator.py'],
                                              capture_output=True, text=True, cwd=os.path.dirname(__file__))
                        if result.returncode == 0:
                            st.success("✅ Demo data created!")
                            st.rerun()
                        else:
                            st.error("❌ Failed to create demo data")
                    except Exception as e:
                        st.error(f"Error: {e}")

        with col2:
            if st.button("🔍 Start Scraping", type="secondary", use_container_width=True):
                st.session_state['navigate_to'] = "🔍 Data Scraper"
                st.rerun()

def show_scraper_page():
    # Enhanced Header
    st.markdown("""
    <div class="fade-in-up">
        <h1 style="color: #667eea; font-weight: 600; margin-bottom: 0.5rem;">🔍 ASX Data Scraper</h1>
        <p style="color: #6c757d; font-size: 1.1rem; margin-bottom: 2rem;">Configure and execute intelligent financial data extraction</p>
    </div>
    """, unsafe_allow_html=True)

    # Configuration Management Section
    st.markdown("""
    <div class="feature-card">
        <h3 style="color: #495057; margin-bottom: 1.5rem;">⚙️ Configuration Management</h3>
    """, unsafe_allow_html=True)

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("**📂 Load Configuration**")
        saved_configs = get_saved_configs()
        if saved_configs:
            selected_config = st.selectbox("Choose saved config:", ["None"] + saved_configs, label_visibility="collapsed")
            if selected_config != "None":
                config = load_scraper_config(selected_config)
                if config:
                    st.session_state.update(config)
                    st.success(f"✅ Loaded: {selected_config}")
        else:
            st.info("No saved configurations")

    with col2:
        st.markdown("**💾 Save Configuration**")
        config_name = st.text_input("Configuration name:", placeholder="my_config", label_visibility="collapsed")
        if st.button("💾 Save Current Config", use_container_width=True) and config_name:
            current_config = {
                'scrape_4c': st.session_state.get('scrape_4c', True),
                'scrape_5b': st.session_state.get('scrape_5b', True),
                'scrape_options': st.session_state.get('scrape_options', True),
                'selected_codes': st.session_state.get('selected_codes', []),
                'headless_mode': st.session_state.get('headless_mode', True)
            }
            save_scraper_config(current_config, config_name)
            st.success(f"✅ Saved: {config_name}")

    with col3:
        st.markdown("**🔄 Reset Options**")
        st.write("")  # Spacing
        if st.button("🔄 Reset to Defaults", use_container_width=True):
            for key in ['scrape_4c', 'scrape_5b', 'scrape_options', 'selected_codes', 'headless_mode']:
                if key in st.session_state:
                    del st.session_state[key]
            st.success("✅ Reset complete")
            st.rerun()

    st.markdown("</div>", unsafe_allow_html=True)

    # Enhanced Configuration Section
    st.markdown("""
    <div class="feature-card">
        <h3 style="color: #495057; margin-bottom: 1.5rem;">📋 Scraper Configuration</h3>
    """, unsafe_allow_html=True)

    # Report Types and Browser Settings
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**📊 Report Types**")

        # Enhanced checkboxes with descriptions
        scrape_4c = st.checkbox(
            "📈 Appendix 4C Reports",
            value=st.session_state.get('scrape_4c', True),
            help="Quarterly cash flow reports for listed entities",
            key='scrape_4c'
        )

        scrape_5b = st.checkbox(
            "⛏️ Appendix 5B Reports",
            value=st.session_state.get('scrape_5b', True),
            help="Mining exploration entity quarterly reports",
            key='scrape_5b'
        )

        scrape_options = st.checkbox(
            "📋 Options Reports (2A/3B/3G)",
            value=st.session_state.get('scrape_options', True),
            help="Unquoted option and director interest reports",
            key='scrape_options'
        )

        # Report type summary
        selected_types = []
        if scrape_4c: selected_types.append("4C")
        if scrape_5b: selected_types.append("5B")
        if scrape_options: selected_types.append("Options")

        if selected_types:
            st.success(f"✅ Selected: {', '.join(selected_types)}")
        else:
            st.warning("⚠️ No report types selected")

    with col2:
        st.markdown("**🔧 Browser & Performance Settings**")

        # Headless mode toggle
        headless_mode = st.toggle(
            "🖥️ Headless Mode",
            value=st.session_state.get('headless_mode', True),
            help="Run browser in background (faster, no GUI)",
            key='headless_mode'
        )

        if headless_mode:
            st.info("🚀 **Headless Mode**: Browser runs in background for optimal performance")
        else:
            st.warning("👁️ **GUI Mode**: Browser window will be visible (slower but you can watch)")

        # Performance settings
        st.markdown("**⚡ Performance Options**")

        delay_between_requests = st.slider(
            "Delay between requests (seconds):",
            min_value=1, max_value=10, value=3,
            help="Higher values are more respectful to ASX servers"
        )

        # Date range selection
        st.markdown("**📅 Date Range**")
        date_range = st.selectbox(
            "Select timeframe:",
            ["All available", "Last 30 days", "Last 90 days", "Last 6 months", "Last year", "Custom range"],
            help="Choose the time period for data collection"
        )

        if date_range == "Custom range":
            col_a, col_b = st.columns(2)
            with col_a:
                start_date = st.date_input("Start date", value=datetime.now() - timedelta(days=90))
            with col_b:
                end_date = st.date_input("End date", value=datetime.now())

    st.markdown("</div>", unsafe_allow_html=True)

    # Enhanced ASX Codes Selection
    st.markdown("""
    <div class="feature-card">
        <h3 style="color: #495057; margin-bottom: 1.5rem;">🏢 ASX Codes Selection</h3>
    """, unsafe_allow_html=True)

    # Selection method with enhanced UI
    st.markdown("**📋 Selection Method**")
    code_selection = st.radio(
        "",
        ["🌐 All ASX codes", "🎯 Specific codes", "📁 Upload CSV file"],
        horizontal=True,
        label_visibility="collapsed"
    )

    selected_codes = []

    if code_selection == "🌐 All ASX codes":
        st.markdown("""
        <div class="info-box">
            <h4 style="color: #2196f3; margin-bottom: 0.5rem;">🌐 All ASX Codes Mode</h4>
            <p style="margin: 0; color: #000000; font-weight: 500;">Will scrape all available ASX codes from the directory (~2000+ companies)</p>
            <p style="margin: 0.5rem 0 0 0; font-weight: 600; color: #ff9800;">⚠️ This may take several hours to complete</p>
        </div>
        """, unsafe_allow_html=True)
        selected_codes = "all"

    elif code_selection == "🎯 Specific codes":
        st.markdown("**✏️ Enter ASX Codes**")

        # Quick selection buttons for popular codes
        st.markdown("**🔥 Popular Codes (click to add):**")
        popular_codes = ["CBA", "BHP", "CSL", "WBC", "ANZ", "NAB", "WES", "TLS", "WOW", "RIO"]

        cols = st.columns(5)
        current_input = st.session_state.get('codes_input', '')

        for i, code in enumerate(popular_codes):
            with cols[i % 5]:
                if st.button(f"📊 {code}", key=f"pop_{code}", use_container_width=True):
                    if current_input:
                        if code not in current_input:
                            st.session_state['codes_input'] = current_input + f", {code}"
                    else:
                        st.session_state['codes_input'] = code
                    st.rerun()

        # Text input for codes
        codes_input = st.text_area(
            "Enter ASX codes:",
            placeholder="CBA, BHP, CSL\nor\nCBA\nBHP\nCSL",
            value=st.session_state.get('codes_input', ''),
            height=100,
            key='codes_input_area'
        )

        # Update session state
        if codes_input != st.session_state.get('codes_input', ''):
            st.session_state['codes_input'] = codes_input

        if codes_input:
            selected_codes = get_asx_codes_from_input(codes_input)

            if selected_codes:
                st.markdown("""
                <div class="success-box">
                    <h4 style="color: #4caf50; margin-bottom: 0.5rem;">✅ Codes Validated</h4>
                    <p style="margin: 0;">Selected {} codes: {}</p>
                </div>
                """.format(
                    len(selected_codes),
                    ', '.join(selected_codes[:10]) + (f" ... and {len(selected_codes) - 10} more" if len(selected_codes) > 10 else "")
                ), unsafe_allow_html=True)
            else:
                st.error("❌ No valid ASX codes found")

    elif code_selection == "📁 Upload CSV file":
        st.markdown("**📁 Upload CSV File**")
        uploaded_file = st.file_uploader(
            "Choose a CSV file containing ASX codes:",
            type="csv",
            help="Upload a CSV file with ASX codes in any column"
        )

        if uploaded_file:
            try:
                df = pd.read_csv(uploaded_file)

                st.markdown("**📊 File Preview:**")
                st.dataframe(df.head(), use_container_width=True)

                # Column selection
                code_column = st.selectbox(
                    "Select the column containing ASX codes:",
                    df.columns,
                    help="Choose which column contains the ASX stock codes"
                )

                if code_column:
                    selected_codes = df[code_column].dropna().astype(str).str.upper().tolist()
                    # Remove duplicates while preserving order
                    selected_codes = list(dict.fromkeys(selected_codes))

                    st.markdown("""
                    <div class="success-box">
                        <h4 style="color: #4caf50; margin-bottom: 0.5rem;">✅ CSV Processed</h4>
                        <p style="margin: 0;">Found {} unique ASX codes from column '{}'</p>
                    </div>
                    """.format(len(selected_codes), code_column), unsafe_allow_html=True)

            except Exception as e:
                st.error(f"❌ Error reading CSV file: {e}")

    st.markdown("</div>", unsafe_allow_html=True)

    # Configuration summary
    if selected_codes:
        config = {
            'selected_codes': selected_codes,
            'scrape_4c': scrape_4c,
            'scrape_5b': scrape_5b,
            'scrape_options': scrape_options,
            'date_range': date_range,
            'headless_mode': headless_mode,
            'delay_between_requests': delay_between_requests
        }

        create_scraper_summary_card(config)

        # Validate configuration
        errors = validate_scraper_config(config)
        if errors:
            for error in errors:
                st.error(f"❌ {error}")

        # Start scraping button
        st.subheader("🚀 Run Scraper")

        # Add note about scraping functionality
        st.info("📝 **Note**: Chrome driver has been fixed and scraping should work. If you encounter issues, demo data and existing result folders are available for visualization.")

        # Start scraping button
        if not errors:
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button("🚀 Start Scraping", type="primary"):
                    with st.spinner("Starting scraper..."):
                        result = run_scraper_with_progress(config)
                        if result:
                            st.session_state['last_scraping_result'] = result
                        else:
                            st.error("Scraping failed. Please check the console for details.")

            with col2:
                if st.button("🎭 Generate Demo Data", type="secondary"):
                    with st.spinner("Generating demo data..."):
                        try:
                            import subprocess
                            result = subprocess.run([sys.executable, 'demo_data_generator.py'],
                                                  capture_output=True, text=True, cwd=os.path.dirname(__file__))
                            if result.returncode == 0:
                                st.success("✅ Demo data generated successfully!")
                                st.info("📊 Go to 'Data Visualization' to explore the demo data.")
                                st.balloons()
                            else:
                                st.error(f"Demo data generation failed: {result.stderr}")
                        except Exception as e:
                            st.error(f"Error generating demo data: {e}")

            with col3:
                if st.button("📊 View Existing Data", type="secondary"):
                    st.info("📈 Go to 'Data Visualization' tab to explore existing result folders")
        else:
            st.error("Please fix configuration errors first")
    else:
        st.warning("⚠️ Please select ASX codes to scrape")

        # Suggest alternatives
        st.markdown("### 💡 Alternative Options:")
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**📊 Use Existing Data:**")
            result_folders = get_result_folders()
            if result_folders:
                st.write(f"Found {len(result_folders)} existing result folders")
                st.write("Go to 'Data Visualization' to explore them")
            else:
                st.write("No existing result folders found")

        with col2:
            st.markdown("**🎭 Generate Demo Data:**")
            if st.button("Create Demo Data", key="demo_alt"):
                with st.spinner("Generating demo data..."):
                    try:
                        import subprocess
                        result = subprocess.run([sys.executable, 'demo_data_generator.py'],
                                              capture_output=True, text=True, cwd=os.path.dirname(__file__))
                        if result.returncode == 0:
                            st.success("✅ Demo data generated!")
                            st.rerun()
                        else:
                            st.error("Demo generation failed")
                    except Exception as e:
                        st.error(f"Error: {e}")



def show_visualization_page():
    st.header("📈 Data Visualization")
    
    # Folder selection
    st.subheader("Select Data Folder")
    
    result_folders = get_result_folders()
    
    if not result_folders:
        st.warning("No result folders found. Please run the scraper first.")
        return
    
    # Let user select folder
    folder_names = [os.path.basename(folder) for folder in result_folders]
    selected_folder_name = st.selectbox("Choose a results folder:", folder_names)
    
    if selected_folder_name:
        selected_folder = next(folder for folder in result_folders if os.path.basename(folder) == selected_folder_name)
        
        # Load and display data
        load_and_visualize_data(selected_folder)

def load_and_visualize_data(folder_path):
    """Load data from selected folder and create visualizations"""
    st.subheader(f"Data from: {os.path.basename(folder_path)}")
    
    # Check available files
    files = os.listdir(folder_path)
    csv_files = [f for f in files if f.endswith('.csv')]
    
    if not csv_files:
        st.error("No CSV files found in the selected folder")
        return
    
    # Tabs for different visualizations
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Overview", "💰 Financial Metrics", "📈 Trends", "📋 Raw Data"])
    
    with tab1:
        show_overview_tab(folder_path, csv_files)
    
    with tab2:
        show_financial_metrics_tab(folder_path, csv_files)
    
    with tab3:
        show_trends_tab(folder_path, csv_files)
    
    with tab4:
        show_raw_data_tab(folder_path, csv_files)

def show_overview_tab(folder_path, csv_files):
    """Show overview statistics"""
    st.subheader("📊 Data Overview")
    
    # Load data from available files
    data_summary = {}
    
    for file in csv_files:
        if file.startswith('financial_data_4C'):
            df = pd.read_csv(os.path.join(folder_path, file))
            data_summary['4C Reports'] = len(df)
        elif file.startswith('financial_data_5B'):
            df = pd.read_csv(os.path.join(folder_path, file))
            data_summary['5B Reports'] = len(df)
        elif file.startswith('unquoted_option'):
            df = pd.read_csv(os.path.join(folder_path, file))
            data_summary['Options Reports'] = len(df)
    
    # Display metrics
    cols = st.columns(len(data_summary))
    for i, (key, value) in enumerate(data_summary.items()):
        with cols[i]:
            st.metric(key, value)

def show_financial_metrics_tab(folder_path, csv_files):
    """Show financial metrics visualizations"""
    st.subheader("💰 Financial Metrics Analysis")

    # Load financial data
    financial_data = {}

    for file in csv_files:
        if file.startswith('financial_data_4C'):
            df = pd.read_csv(os.path.join(folder_path, file))
            financial_data['4C'] = df
        elif file.startswith('financial_data_5B'):
            df = pd.read_csv(os.path.join(folder_path, file))
            financial_data['5B'] = df

    if not financial_data:
        st.warning("No financial data files found")
        return

    # Report type selection
    available_types = list(financial_data.keys())
    selected_type = st.selectbox("Select report type:", available_types)

    if selected_type:
        df = financial_data[selected_type]

        # Create summary metrics
        metrics = create_summary_metrics(df, selected_type)

        if metrics:
            st.markdown("#### 📊 Key Metrics")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Total Reports", metrics.get('total_reports', 'N/A'))
            with col2:
                st.metric("Unique Companies", metrics.get('unique_companies', 'N/A'))
            with col3:
                st.metric("Avg Cash Position", metrics.get('avg_cash_position', 'N/A'))
            with col4:
                st.metric("Positive Cash Flow %", metrics.get('positive_cash_flow_pct', 'N/A'))

        # Financial overview chart
        st.markdown("#### 📈 Financial Overview")
        overview_chart = create_financial_overview_chart(df, selected_type)
        if overview_chart:
            st.plotly_chart(overview_chart, use_container_width=True)
        else:
            st.info("No data available for financial overview chart")

        # Company comparison
        st.markdown("#### 🏢 Top Companies by Cash Position")
        comparison_chart = create_company_comparison_chart(df)
        if comparison_chart:
            st.plotly_chart(comparison_chart, use_container_width=True)
        else:
            st.info("No data available for company comparison")

        # Funding quarters distribution (if available)
        if 'estimated_quarters_funding' in df.columns:
            st.markdown("#### ⏱️ Funding Quarters Distribution")
            quarters_chart = create_funding_quarters_distribution(df)
            if quarters_chart:
                st.plotly_chart(quarters_chart, use_container_width=True)
            else:
                st.info("No funding quarters data available")

        # Correlation analysis
        st.markdown("#### 🔗 Financial Metrics Correlation")
        corr_chart = create_correlation_heatmap(df)
        if corr_chart:
            st.plotly_chart(corr_chart, use_container_width=True)
        else:
            st.info("Insufficient data for correlation analysis")

def show_trends_tab(folder_path, csv_files):
    """Show trend analysis"""
    st.subheader("📈 Trend Analysis")

    # Load financial data
    financial_data = {}

    for file in csv_files:
        if file.startswith('financial_data_4C'):
            df = pd.read_csv(os.path.join(folder_path, file))
            financial_data['4C'] = df
        elif file.startswith('financial_data_5B'):
            df = pd.read_csv(os.path.join(folder_path, file))
            financial_data['5B'] = df

    if not financial_data:
        st.warning("No financial data files found")
        return

    # Report type selection
    available_types = list(financial_data.keys())
    selected_type = st.selectbox("Select report type for trend analysis:", available_types)

    if selected_type:
        df = financial_data[selected_type]

        # Cash flow trend
        st.markdown("#### 💰 Cash Flow Trend Over Time")
        cash_flow_chart = create_cash_flow_trend_chart(df)
        if cash_flow_chart:
            st.plotly_chart(cash_flow_chart, use_container_width=True)
        else:
            st.info("No cash flow trend data available")

        # Industry analysis
        st.markdown("#### 🏭 Industry Analysis")
        industry_chart = create_industry_analysis_chart(df)
        if industry_chart:
            st.plotly_chart(industry_chart, use_container_width=True)
        else:
            st.info("No industry data available")

        # Time series for different metrics
        st.markdown("#### 📊 Metric Time Series")

        # Metric selection
        available_metrics = []
        if 'net_cash_operating_activities' in df.columns:
            available_metrics.append(('net_cash_operating_activities', 'Net Cash from Operations'))
        if 'cash_and_cash_equivalents' in df.columns:
            available_metrics.append(('cash_and_cash_equivalents', 'Cash & Equivalents'))
        if 'total_available_funding' in df.columns:
            available_metrics.append(('total_available_funding', 'Total Available Funding'))
        if 'estimated_quarters_funding' in df.columns:
            available_metrics.append(('estimated_quarters_funding', 'Estimated Quarters Funding'))

        if available_metrics:
            selected_metric = st.selectbox(
                "Select metric for time series:",
                available_metrics,
                format_func=lambda x: x[1]
            )

            if selected_metric:
                metric_column, metric_title = selected_metric
                time_series_chart = create_time_series_chart(df, metric_column, f"{metric_title} Over Time")
                if time_series_chart:
                    st.plotly_chart(time_series_chart, use_container_width=True)
                else:
                    st.info(f"No data available for {metric_title} time series")
        else:
            st.info("No metrics available for time series analysis")

def show_raw_data_tab(folder_path, csv_files):
    """Show raw data tables"""
    st.subheader("📋 Raw Data")
    
    # Let user select which file to view
    selected_file = st.selectbox("Select file to view:", csv_files)
    
    if selected_file:
        df = pd.read_csv(os.path.join(folder_path, selected_file))
        
        st.write(f"**{selected_file}** ({len(df)} rows)")
        
        # Search and filter options
        col1, col2 = st.columns(2)
        with col1:
            search_term = st.text_input("Search in data:")
        with col2:
            show_rows = st.number_input("Rows to display:", min_value=10, max_value=1000, value=100)
        
        # Filter data if search term provided
        if search_term:
            mask = df.astype(str).apply(lambda x: x.str.contains(search_term, case=False, na=False)).any(axis=1)
            df_filtered = df[mask]
            st.write(f"Found {len(df_filtered)} matching rows")
        else:
            df_filtered = df
        
        # Display data
        st.dataframe(df_filtered.head(show_rows), use_container_width=True)
        
        # Download button
        csv = df_filtered.to_csv(index=False)
        st.download_button(
            label="📥 Download filtered data as CSV",
            data=csv,
            file_name=f"filtered_{selected_file}",
            mime="text/csv"
        )

def show_settings_page():
    """Show settings and configuration"""
    st.header("⚙️ Settings")
    
    st.subheader("Application Settings")
    
    # Theme settings
    st.markdown("**Display Settings:**")
    theme = st.selectbox("Theme:", ["Light", "Dark", "Auto"])
    
    # Scraping settings
    st.markdown("**Scraping Settings:**")
    delay_between_requests = st.slider("Delay between requests (seconds):", 1, 10, 3)
    timeout_duration = st.slider("Request timeout (seconds):", 10, 60, 30)
    
    # Data settings
    st.markdown("**Data Settings:**")
    auto_cleanup = st.checkbox("Auto-cleanup old result folders", value=False)
    if auto_cleanup:
        cleanup_days = st.number_input("Keep results for (days):", min_value=1, max_value=365, value=30)
    
    # Save settings
    if st.button("💾 Save Settings"):
        settings = {
            'theme': theme,
            'delay_between_requests': delay_between_requests,
            'timeout_duration': timeout_duration,
            'auto_cleanup': auto_cleanup,
            'cleanup_days': cleanup_days if auto_cleanup else None
        }
        save_settings(settings)
        st.success("Settings saved successfully!")

def get_result_folders():
    """Get list of result folders"""
    base_dir = os.path.dirname(__file__)
    pattern = os.path.join(base_dir, "results_*")
    folders = glob.glob(pattern)
    # Sort by modification time (newest first)
    folders.sort(key=os.path.getmtime, reverse=True)
    return folders

def save_settings(settings):
    """Save settings to file"""
    settings_file = os.path.join(os.path.dirname(__file__), 'app_settings.json')
    with open(settings_file, 'w') as f:
        json.dump(settings, f, indent=2)

def load_settings():
    """Load settings from file"""
    settings_file = os.path.join(os.path.dirname(__file__), 'app_settings.json')
    if os.path.exists(settings_file):
        with open(settings_file, 'r') as f:
            return json.load(f)
    return {}

if __name__ == "__main__":
    main()
