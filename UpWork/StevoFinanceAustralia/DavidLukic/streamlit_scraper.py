import streamlit as st
import pandas as pd
import os
import sys
import subprocess
import threading
import time
from datetime import datetime, timedelta
import queue
import json

# Import the safe scraper functions
from scraper_functions import (
    safe_download_asx_directory, safe_scrape_announcements,
    add_company_info, format_financial_values_4c, format_financial_values_5b
)

class StreamlitScraper:
    """Wrapper class for running the scraper with Streamlit integration"""
    
    def __init__(self):
        self.progress_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.stop_event = threading.Event()
        
    def run_scraper_async(self, config):
        """Run the scraper in a separate thread"""
        thread = threading.Thread(target=self._run_scraper, args=(config,))
        thread.daemon = True
        thread.start()
        return thread
    
    def _run_scraper(self, config):
        """Internal method to run the scraper"""
        try:
            # Initialize progress
            self.progress_queue.put({"progress": 0, "status": "Starting scraper..."})

            # Step 1: Download ASX directory
            self.progress_queue.put({"progress": 10, "status": "Downloading ASX directory..."})
            headless_mode = config.get('headless_mode', True)
            csv_location = safe_download_asx_directory(headless=headless_mode)

            if not csv_location:
                raise Exception("Failed to download ASX directory")
            
            if self.stop_event.is_set():
                return
            
            # Step 2: Load company data
            self.progress_queue.put({"progress": 20, "status": "Loading company data..."})
            csv_file = [f for f in os.listdir(csv_location) if f.endswith('.csv')][0]
            csv_file_path = os.path.join(csv_location, csv_file)
            df_companies = pd.read_csv(csv_file_path)
            company_lookup = df_companies.set_index('ASX code').to_dict('index')
            
            # Get ASX codes based on configuration
            if config['selected_codes'] == 'all':
                asx_codes = df_companies['ASX code'].to_list()
            else:
                asx_codes = config['selected_codes']
            
            self.progress_queue.put({"progress": 30, "status": f"Found {len(asx_codes)} ASX codes to process"})
            
            if self.stop_event.is_set():
                return
            
            # Step 3: Scrape announcements
            self.progress_queue.put({"progress": 40, "status": "Scraping announcements..."})
            
            # Filter scraping based on configuration
            if config['scrape_4c'] or config['scrape_5b'] or config['scrape_options']:
                results_4c, results_5b, results2 = safe_scrape_announcements(asx_codes, headless=headless_mode)
            else:
                results_4c, results_5b, results2 = [], [], []
            
            if self.stop_event.is_set():
                return
            
            # Step 4: Process results
            self.progress_queue.put({"progress": 70, "status": "Processing results..."})
            
            # Add company information and format values
            if results_4c:
                results_4c = add_company_info(results_4c, company_lookup)
                results_4c = format_financial_values_4c(results_4c)

            if results_5b:
                results_5b = add_company_info(results_5b, company_lookup)
                results_5b = format_financial_values_5b(results_5b)

            if results2:
                results2 = add_company_info(results2, company_lookup)
            
            # Step 5: Save results
            self.progress_queue.put({"progress": 90, "status": "Saving results..."})
            
            # Create results folder
            timestamp = datetime.now().strftime("%H_%M-%d-%m-%Y")
            results_folder = f"results_{timestamp}"
            os.makedirs(results_folder, exist_ok=True)
            
            # Save CSV files
            files_created = []
            
            if results_4c and config['scrape_4c']:
                df_4c = pd.DataFrame(results_4c)
                columns_4c = ['asx_code', 'company_name', 'gics_industry_group', 'listing_date', 'market_cap',
                              'date', 'title', 'url', 'report_type',
                              'net_cash_operating_activities', 'cash_and_cash_equivalents',
                              'unused_finance_facilities', 'total_available_funding',
                              'estimated_quarters_funding']
                df_4c_filtered = df_4c[columns_4c]
                output_4c = os.path.join(results_folder, 'financial_data_4C.csv')
                df_4c_filtered.to_csv(output_4c, index=False)
                files_created.append(output_4c)
            
            if results_5b and config['scrape_5b']:
                df_5b = pd.DataFrame(results_5b)
                columns_5b = ['asx_code', 'company_name', 'gics_industry_group', 'listing_date', 'market_cap',
                              'date', 'title', 'url', 'report_type',
                              'net_cash_operating_activities', 'payments_exploration_evaluation',
                              'total_relevant_outgoings', 'cash_and_cash_equivalents',
                              'unused_finance_facilities', 'total_available_funding',
                              'estimated_quarters_funding']
                df_5b_filtered = df_5b[columns_5b]
                output_5b = os.path.join(results_folder, 'financial_data_5B.csv')
                df_5b_filtered.to_csv(output_5b, index=False)
                files_created.append(output_5b)
            
            if results2 and config['scrape_options']:
                df2 = pd.DataFrame(results2)
                output_2 = os.path.join(results_folder, 'unquoted_option_data.csv')
                df2.to_csv(output_2, index=False)
                files_created.append(output_2)
            
            # Copy ASX directory CSV
            import shutil
            downloaded_csv_dest = os.path.join(results_folder, f'asx_directory_{timestamp}.csv')
            shutil.copy2(csv_file_path, downloaded_csv_dest)
            files_created.append(downloaded_csv_dest)
            
            # Prepare final results
            final_results = {
                'success': True,
                'results_folder': results_folder,
                'files_created': files_created,
                'summary': {
                    '4c_count': len(results_4c),
                    '5b_count': len(results_5b),
                    'options_count': len(results2),
                    'total_companies': len(asx_codes),
                    'timestamp': timestamp
                }
            }
            
            self.progress_queue.put({"progress": 100, "status": "Scraping completed successfully!"})
            self.result_queue.put(final_results)
            
        except Exception as e:
            error_result = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.result_queue.put(error_result)
            self.progress_queue.put({"progress": 0, "status": f"Error: {str(e)}"})
    
    def get_progress(self):
        """Get current progress"""
        try:
            return self.progress_queue.get_nowait()
        except queue.Empty:
            return None
    
    def get_result(self):
        """Get final result"""
        try:
            return self.result_queue.get_nowait()
        except queue.Empty:
            return None
    
    def stop(self):
        """Stop the scraper"""
        self.stop_event.set()

def run_scraper_with_progress(config):
    """Run scraper with progress tracking in Streamlit"""

    st.info("🚀 Starting scraper (this may take several minutes)...")

    # Initialize scraper
    scraper = StreamlitScraper()

    # Create progress containers
    progress_container = st.container()
    status_container = st.container()

    with progress_container:
        progress_bar = st.progress(0)

    with status_container:
        status_text = st.empty()
        stop_button = st.button("⏹️ Stop Scraping", key="stop_scraper")

    # Start scraping
    thread = scraper.run_scraper_async(config)

    # Monitor progress
    last_progress = 0
    last_status = "Starting..."

    while thread.is_alive():
        # Check for stop button
        if stop_button:
            scraper.stop()
            st.warning("Scraping stopped by user")
            break

        # Update progress
        progress_update = scraper.get_progress()
        if progress_update:
            last_progress = progress_update['progress']
            last_status = progress_update['status']

        progress_bar.progress(last_progress / 100)
        status_text.text(last_status)

        time.sleep(0.5)

    # Get final result
    result = scraper.get_result()

    if result:
        if result['success']:
            st.success("✅ Scraping completed successfully!")

            # Display summary
            summary = result['summary']
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("4C Reports", summary['4c_count'])
            with col2:
                st.metric("5B Reports", summary['5b_count'])
            with col3:
                st.metric("Options Reports", summary['options_count'])
            with col4:
                st.metric("Companies Processed", summary['total_companies'])

            # Show files created
            st.subheader("📁 Files Created")
            for file_path in result['files_created']:
                st.write(f"• {file_path}")

            # Store result in session state for later use
            st.session_state['last_scraping_result'] = result

            return result
        else:
            st.error(f"❌ Scraping failed: {result['error']}")
            return None
    else:
        st.warning("⚠️ Scraping was interrupted")
        return None

def validate_scraper_config(config):
    """Validate scraper configuration"""
    errors = []
    
    if not config.get('selected_codes'):
        errors.append("No ASX codes selected")
    
    if not any([config.get('scrape_4c'), config.get('scrape_5b'), config.get('scrape_options')]):
        errors.append("No report types selected")
    
    return errors

def get_asx_codes_from_input(codes_input):
    """Parse ASX codes from user input"""
    if not codes_input:
        return []
    
    # Handle both comma-separated and newline-separated input
    codes = codes_input.replace(',', '\n').split('\n')
    cleaned_codes = []
    
    for code in codes:
        code = code.strip().upper()
        if code and len(code) <= 5:  # Basic validation for ASX codes
            cleaned_codes.append(code)
    
    return cleaned_codes

def save_scraper_config(config, name):
    """Save scraper configuration for reuse"""
    config_dir = os.path.join(os.path.dirname(__file__), 'scraper_configs')
    os.makedirs(config_dir, exist_ok=True)
    
    config_file = os.path.join(config_dir, f"{name}.json")
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)

def load_scraper_config(name):
    """Load saved scraper configuration"""
    config_dir = os.path.join(os.path.dirname(__file__), 'scraper_configs')
    config_file = os.path.join(config_dir, f"{name}.json")
    
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            return json.load(f)
    return None

def get_saved_configs():
    """Get list of saved configurations"""
    config_dir = os.path.join(os.path.dirname(__file__), 'scraper_configs')
    if not os.path.exists(config_dir):
        return []
    
    configs = []
    for file in os.listdir(config_dir):
        if file.endswith('.json'):
            configs.append(file[:-5])  # Remove .json extension
    
    return configs

def estimate_scraping_time(num_codes, report_types):
    """Estimate scraping time based on configuration"""
    # Base time per code (in seconds)
    base_time_per_code = 5
    
    # Additional time based on report types
    type_multiplier = len([t for t in report_types if t])
    
    # Total estimated time
    total_seconds = num_codes * base_time_per_code * type_multiplier
    
    if total_seconds < 60:
        return f"~{total_seconds} seconds"
    elif total_seconds < 3600:
        return f"~{total_seconds // 60} minutes"
    else:
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        return f"~{hours}h {minutes}m"

def create_scraper_summary_card(config):
    """Create a summary card for the scraper configuration"""
    st.markdown("### 📋 Scraping Configuration Summary")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Report Types:**")
        if config.get('scrape_4c'):
            st.write("✅ Appendix 4C Reports")
        if config.get('scrape_5b'):
            st.write("✅ Appendix 5B Reports")
        if config.get('scrape_options'):
            st.write("✅ Options Reports (2A/3B/3G)")
    
    with col2:
        st.markdown("**ASX Codes:**")
        if config['selected_codes'] == 'all':
            st.write("🔄 All available ASX codes")
        else:
            num_codes = len(config['selected_codes'])
            st.write(f"📊 {num_codes} specific codes")
            if num_codes <= 10:
                st.write(f"Codes: {', '.join(config['selected_codes'])}")
    
    # Estimated time
    if config['selected_codes'] != 'all':
        num_codes = len(config['selected_codes'])
        report_types = [config.get('scrape_4c'), config.get('scrape_5b'), config.get('scrape_options')]
        estimated_time = estimate_scraping_time(num_codes, report_types)
        st.info(f"⏱️ Estimated time: {estimated_time}")
    else:
        st.info("⏱️ Estimated time: Several hours (all ASX codes)")
