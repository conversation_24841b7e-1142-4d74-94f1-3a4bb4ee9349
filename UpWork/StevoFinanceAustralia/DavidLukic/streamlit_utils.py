import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import streamlit as st
import numpy as np
from datetime import datetime, timedelta
import re

def clean_currency_value(value):
    """Clean currency values for numerical analysis"""
    if pd.isna(value) or value in ['N/A', 'Not found', '-']:
        return None
    
    if isinstance(value, str):
        # Remove currency symbols, commas, and extract number
        cleaned = re.sub(r'[^\d.-]', '', value.replace(',', ''))
        try:
            return float(cleaned)
        except ValueError:
            return None
    
    return float(value) if value is not None else None

def parse_quarters_funding(value):
    """Parse quarters funding value"""
    if pd.isna(value) or value in ['N/A', 'Not found', '-']:
        return None
    
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def create_financial_overview_chart(df, report_type='4C'):
    """Create overview chart for financial data"""
    if df.empty:
        return None
    
    # Prepare data based on report type
    if report_type == '4C':
        metrics = ['net_cash_operating_activities', 'cash_and_cash_equivalents', 
                  'total_available_funding']
        titles = ['Net Cash from Operations', 'Cash & Equivalents', 'Total Available Funding']
    else:  # 5B
        metrics = ['net_cash_operating_activities', 'payments_exploration_evaluation',
                  'cash_and_cash_equivalents', 'total_available_funding']
        titles = ['Net Cash from Operations', 'Exploration Payments', 
                 'Cash & Equivalents', 'Total Available Funding']
    
    # Clean and prepare data
    chart_data = []
    for i, metric in enumerate(metrics):
        if metric in df.columns:
            values = df[metric].apply(clean_currency_value).dropna()
            if not values.empty:
                chart_data.append({
                    'Metric': titles[i],
                    'Average': values.mean(),
                    'Median': values.median(),
                    'Count': len(values)
                })
    
    if not chart_data:
        return None
    
    chart_df = pd.DataFrame(chart_data)
    
    # Create bar chart
    fig = px.bar(chart_df, x='Metric', y='Average', 
                title=f'Average Financial Metrics - {report_type} Reports',
                labels={'Average': 'Amount ($000s)'})
    
    fig.update_layout(
        xaxis_tickangle=-45,
        height=400,
        showlegend=False
    )
    
    return fig

def create_cash_flow_trend_chart(df):
    """Create cash flow trend over time"""
    if df.empty or 'date' not in df.columns:
        return None
    
    # Convert date column
    df['date'] = pd.to_datetime(df['date'])
    
    # Clean cash flow data
    if 'net_cash_operating_activities' in df.columns:
        df['cash_flow_clean'] = df['net_cash_operating_activities'].apply(clean_currency_value)
        
        # Remove null values
        df_clean = df.dropna(subset=['cash_flow_clean'])
        
        if df_clean.empty:
            return None
        
        # Group by month for trend analysis
        df_clean['month'] = df_clean['date'].dt.to_period('M')
        monthly_data = df_clean.groupby('month').agg({
            'cash_flow_clean': ['mean', 'count'],
            'asx_code': 'nunique'
        }).reset_index()
        
        monthly_data.columns = ['Month', 'Avg_Cash_Flow', 'Report_Count', 'Unique_Companies']
        monthly_data['Month'] = monthly_data['Month'].dt.to_timestamp()
        
        # Create line chart
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=monthly_data['Month'],
            y=monthly_data['Avg_Cash_Flow'],
            mode='lines+markers',
            name='Average Cash Flow',
            line=dict(color='#1f77b4', width=3),
            marker=dict(size=8)
        ))
        
        fig.update_layout(
            title='Net Cash Flow Trend Over Time',
            xaxis_title='Month',
            yaxis_title='Average Cash Flow ($000s)',
            height=400,
            hovermode='x unified'
        )
        
        return fig
    
    return None

def create_industry_analysis_chart(df):
    """Create industry analysis chart"""
    if df.empty or 'gics_industry_group' not in df.columns:
        return None
    
    # Count companies by industry
    industry_counts = df['gics_industry_group'].value_counts().head(10)
    
    if industry_counts.empty:
        return None
    
    fig = px.bar(
        x=industry_counts.values,
        y=industry_counts.index,
        orientation='h',
        title='Top 10 Industries by Report Count',
        labels={'x': 'Number of Reports', 'y': 'Industry Group'}
    )
    
    fig.update_layout(
        height=400,
        yaxis={'categoryorder': 'total ascending'}
    )
    
    return fig

def create_funding_quarters_distribution(df):
    """Create distribution chart for estimated quarters of funding"""
    if df.empty or 'estimated_quarters_funding' not in df.columns:
        return None
    
    # Clean quarters data
    df['quarters_clean'] = df['estimated_quarters_funding'].apply(parse_quarters_funding)
    quarters_data = df.dropna(subset=['quarters_clean'])
    
    if quarters_data.empty:
        return None
    
    # Create histogram
    fig = px.histogram(
        quarters_data,
        x='quarters_clean',
        nbins=20,
        title='Distribution of Estimated Quarters of Funding',
        labels={'quarters_clean': 'Quarters of Funding', 'count': 'Number of Companies'}
    )
    
    fig.update_layout(
        height=400,
        showlegend=False
    )
    
    return fig

def create_company_comparison_chart(df, top_n=10):
    """Create comparison chart for top companies by cash position"""
    if df.empty or 'cash_and_cash_equivalents' not in df.columns:
        return None
    
    # Clean cash data
    df['cash_clean'] = df['cash_and_cash_equivalents'].apply(clean_currency_value)
    
    # Get latest data for each company
    df['date'] = pd.to_datetime(df['date'])
    latest_data = df.sort_values('date').groupby('asx_code').last().reset_index()
    
    # Filter out null values and get top companies
    latest_clean = latest_data.dropna(subset=['cash_clean'])
    top_companies = latest_clean.nlargest(top_n, 'cash_clean')
    
    if top_companies.empty:
        return None
    
    # Create bar chart
    fig = px.bar(
        top_companies,
        x='asx_code',
        y='cash_clean',
        title=f'Top {top_n} Companies by Cash Position',
        labels={'cash_clean': 'Cash & Equivalents ($000s)', 'asx_code': 'ASX Code'},
        hover_data=['company_name'] if 'company_name' in top_companies.columns else None
    )
    
    fig.update_layout(
        height=400,
        xaxis_tickangle=-45
    )
    
    return fig

def create_correlation_heatmap(df):
    """Create correlation heatmap for financial metrics"""
    if df.empty:
        return None
    
    # Select numerical columns
    numerical_cols = []
    potential_cols = ['net_cash_operating_activities', 'cash_and_cash_equivalents',
                     'unused_finance_facilities', 'total_available_funding',
                     'estimated_quarters_funding']
    
    for col in potential_cols:
        if col in df.columns:
            if col == 'estimated_quarters_funding':
                df[f'{col}_clean'] = df[col].apply(parse_quarters_funding)
            else:
                df[f'{col}_clean'] = df[col].apply(clean_currency_value)
            numerical_cols.append(f'{col}_clean')
    
    if len(numerical_cols) < 2:
        return None
    
    # Calculate correlation matrix
    corr_data = df[numerical_cols].corr()
    
    # Create heatmap
    fig = px.imshow(
        corr_data,
        title='Financial Metrics Correlation Matrix',
        color_continuous_scale='RdBu',
        aspect='auto'
    )
    
    fig.update_layout(height=400)
    
    return fig

def create_summary_metrics(df, report_type='4C'):
    """Create summary metrics for the dashboard"""
    if df.empty:
        return {}
    
    metrics = {}
    
    # Basic counts
    metrics['total_reports'] = len(df)
    metrics['unique_companies'] = df['asx_code'].nunique() if 'asx_code' in df.columns else 0
    
    # Date range
    if 'date' in df.columns:
        df['date'] = pd.to_datetime(df['date'])
        metrics['date_range'] = f"{df['date'].min().strftime('%Y-%m-%d')} to {df['date'].max().strftime('%Y-%m-%d')}"
    
    # Financial metrics
    if 'cash_and_cash_equivalents' in df.columns:
        cash_values = df['cash_and_cash_equivalents'].apply(clean_currency_value).dropna()
        if not cash_values.empty:
            metrics['avg_cash_position'] = f"${cash_values.mean():,.0f}k"
            metrics['median_cash_position'] = f"${cash_values.median():,.0f}k"
    
    if 'net_cash_operating_activities' in df.columns:
        cash_flow_values = df['net_cash_operating_activities'].apply(clean_currency_value).dropna()
        if not cash_flow_values.empty:
            metrics['avg_cash_flow'] = f"${cash_flow_values.mean():,.0f}k"
            positive_flow = (cash_flow_values > 0).sum()
            metrics['positive_cash_flow_pct'] = f"{(positive_flow / len(cash_flow_values) * 100):.1f}%"
    
    if 'estimated_quarters_funding' in df.columns:
        quarters_values = df['estimated_quarters_funding'].apply(parse_quarters_funding).dropna()
        if not quarters_values.empty:
            metrics['avg_quarters_funding'] = f"{quarters_values.mean():.1f}"
    
    return metrics

def format_large_number(num):
    """Format large numbers for display"""
    if num >= 1e9:
        return f"${num/1e9:.1f}B"
    elif num >= 1e6:
        return f"${num/1e6:.1f}M"
    elif num >= 1e3:
        return f"${num/1e3:.1f}K"
    else:
        return f"${num:.0f}"

def create_time_series_chart(df, metric_column, title):
    """Create a time series chart for any metric"""
    if df.empty or metric_column not in df.columns or 'date' not in df.columns:
        return None
    
    df['date'] = pd.to_datetime(df['date'])
    
    # Clean the metric data
    if metric_column == 'estimated_quarters_funding':
        df['metric_clean'] = df[metric_column].apply(parse_quarters_funding)
    else:
        df['metric_clean'] = df[metric_column].apply(clean_currency_value)
    
    # Remove null values
    df_clean = df.dropna(subset=['metric_clean'])
    
    if df_clean.empty:
        return None
    
    # Group by date and calculate statistics
    daily_stats = df_clean.groupby('date')['metric_clean'].agg(['mean', 'count']).reset_index()
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=daily_stats['date'],
        y=daily_stats['mean'],
        mode='lines+markers',
        name='Average',
        line=dict(color='#1f77b4', width=2),
        marker=dict(size=6)
    ))
    
    fig.update_layout(
        title=title,
        xaxis_title='Date',
        yaxis_title='Value',
        height=400,
        hovermode='x unified'
    )
    
    return fig
