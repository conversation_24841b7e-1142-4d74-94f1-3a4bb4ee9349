from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import pandas as pd
from datetime import datetime, timedelta
import time
import re
from selenium.webdriver.chrome.options import Options
import PyPDF2
import io
import requests
from pdfextractor import extract_financial_data
from pdfextractor2 import extract_unquoted_option_data
import random

def setup_driver():
    """
    Set up and return a configured Chrome WebDriver instance using webdriver-manager.
    """
    chrome_options = Options()
    # chrome_options.add_argument('--headless')  # Run in headless mode
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    # Use webdriver-manager to handle driver installation (with path fix)
    driver_path = ChromeDriverManager().install()

    # Fix webdriver-manager bug that points to wrong file
    if 'THIRD_PARTY_NOTICES' in driver_path:
        import os
        driver_dir = os.path.dirname(driver_path)
        actual_driver = os.path.join(driver_dir, 'chromedriver')
        if os.path.exists(actual_driver):
            driver_path = actual_driver
            os.chmod(actual_driver, 0o755)  # Make sure it's executable

    service = Service(driver_path)
    return webdriver.Chrome(service=service, options=chrome_options)

def scrape_announcements(asx_codes):
    """
    Scrape ASX announcements for given ASX codes and extract financial data.

    Args:
        asx_codes (list): List of ASX codes to scrape

    Returns:
        tuple: Three lists containing results_4c, results_5b, and results2 (unquoted option data)
    """
    results_4c = []  # For Appendix 4C reports
    results_5b = []  # For Appendix 5B reports
    results2 = []   # For extract_unquoted_option_data (2A/3B/3G)
    driver = setup_driver()
    wait = WebDriverWait(driver, 10)  # 10 seconds timeout
    
    try:
        for asx_code in asx_codes:
            try:
                # Construct the announcement URL
                url = f'https://www.asx.com.au/markets/trade-our-cash-market/announcements.{asx_code}'
                
                # Add delay to be respectful to the server
                time.sleep(1)
                
                # Get the announcements page
                driver.get(url)
                
                # Wait for the table to be present
                try:
                    table = wait.until(
                        EC.presence_of_element_located((By.CLASS_NAME, "table-bordered"))
                    )
                except TimeoutException:
                    print(f"No table found for {asx_code}")
                    continue
                random_pause = random.randint(4,7)
                time.sleep(random_pause)
                # from selenium.webdriver.common.keys import Keys
                # driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)

                # Get all rows except header
                rows = table.find_elements(By.TAG_NAME, "tr")[1:]
                
                for row in rows:
                    try:
                        # Extract date
                        date_cell = row.find_element(By.CLASS_NAME, "colDateTime")
                        date_text = date_cell.text.strip()
                        if 'today' in date_text.lower():
                            date = datetime.now()
                        elif 'yesterday' in date_text.lower():
                            date = datetime.now() - timedelta(days=1)
                        else:
                            date = datetime.strptime(date_text, '%d %b %Y %I:%M%p')
                        
                        # Extract announcement link and title
                        link = row.find_element(By.TAG_NAME, "a")
                        announcement_url = link.get_attribute('href')
                        # print(announcement_url)
                        title = link.text.strip()

                        # Extract document type
                        document_type_cell = row.find_elements(By.XPATH, './td/ul/li')
                        document_type = document_type_cell[-1].text.strip()
                        # print(f"document type is :{document_type}")
                        
                        # Check if it's a quarterly report (4C or 5B)
                        if '4C' in title:
                            print(f"Found 4C report for {asx_code}: {title} - document link is {announcement_url}")
                            # Extract financial data using pdfextractor with 4C format
                            financial_data = extract_financial_data(announcement_url, '4C')
                            if financial_data:
                                results_4c.append({
                                    'asx_code': asx_code,
                                    'date': date,
                                    'title': title,
                                    'url': announcement_url,
                                    'report_type': '4C',
                                    **financial_data
                                })
                        elif '5B' in title:
                            print(f"Found 5B report for {asx_code}: {title} - document link is {announcement_url}")
                            # Extract financial data using pdfextractor with 5B format
                            financial_data = extract_financial_data(announcement_url, '5B')
                            if financial_data:
                                results_5b.append({
                                    'asx_code': asx_code,
                                    'date': date,
                                    'title': title,
                                    'url': announcement_url,
                                    'report_type': '5B',
                                    **financial_data
                                })
                        elif '2A' in document_type or '3B' in document_type or '3G' in document_type:
                            print(f"Found report for {asx_code} : {document_type} - document link is {announcement_url}")
                            financial_data2 = extract_unquoted_option_data(announcement_url)
                            if financial_data2:
                                results2.append({
                                    'asx_code': asx_code,
                                    'date': date,
                                    'title': title,
                                    'url': announcement_url,
                                    **financial_data2
                                })
                            
                    except Exception as e:
                        print(f"Error processing row for {asx_code}: {str(e)}")
                        continue
                        
            except Exception as e:
                print(f"Error processing {asx_code}: {str(e)} {url}")
                continue
                
    finally:
        driver.quit()
            
    return results_4c, results_5b, results2

def save_results_to_csv(results_4c, results_5b, results2):
    """Save results to separate CSV files based on report type"""

    # Save 4C reports (8.1-8.5)
    if results_4c:
        df_4c = pd.DataFrame(results_4c)
        # Define 4C columns
        columns_4c = ['asx_code', 'date', 'title', 'url', 'report_type',
                     'net_cash_operating_activities', 'cash_and_cash_equivalents',
                     'unused_finance_facilities', 'total_available_funding',
                     'estimated_quarters_funding']
        df_4c_filtered = df_4c[columns_4c]
        df_4c_filtered.to_csv('financial_data_4C.csv', index=False)
        print(f"Saved {len(df_4c)} Appendix 4C records to financial_data_4C.csv")
    else:
        print("No 4C financial data found")

    # Save 5B reports (8.1-8.7)
    if results_5b:
        df_5b = pd.DataFrame(results_5b)
        # Define 5B columns
        columns_5b = ['asx_code', 'date', 'title', 'url', 'report_type',
                     'net_cash_operating_activities', 'payments_exploration_evaluation',
                     'total_relevant_outgoings', 'cash_and_cash_equivalents',
                     'unused_finance_facilities', 'total_available_funding',
                     'estimated_quarters_funding']
        df_5b_filtered = df_5b[columns_5b]
        df_5b_filtered.to_csv('financial_data_5B.csv', index=False)
        print(f"Saved {len(df_5b)} Appendix 5B records to financial_data_5B.csv")
    else:
        print("No 5B financial data found")

    # Save combined file if we have any financial data
    if results_4c or results_5b:
        combined_results = results_4c + results_5b
        df_combined = pd.DataFrame(combined_results)
        df_combined.to_csv('financial_data_combined.csv', index=False)
        print(f"Saved {len(combined_results)} total financial records to financial_data_combined.csv")

    # Save unquoted option data
    if results2:
        df2 = pd.DataFrame(results2)
        df2.to_csv('unquoted_option_data.csv', index=False)
        print(f"Saved {len(results2)} unquoted option records to unquoted_option_data.csv")
    else:
        print("No unquoted option data found")

# def download_pdf(url):
#     """
#     Download and extract text from PDF using Selenium.
    
#     Args:
#         url (str): URL of the PDF
        
#     Returns:
#         str: Extracted text from PDF
#     """
#     # Create a new driver instance for PDF download
#     pdf_driver = setup_driver()
#     try:
#         # Navigate directly to PDF URL
#         pdf_driver.get(url)
        
#         # Wait for PDF to load
#         time.sleep(2)  # Give some time for PDF to load
        
#         # Get the page source which contains PDF content
#         page_source = pdf_driver.page_source
        
#         # Extract text from PDF content
#         pdf_file = io.BytesIO(page_source.encode('utf-8'))
#         pdf_reader = PyPDF2.PdfReader(pdf_file)
        
#         # Extract text from all pages
#         text = ""
#         for page in pdf_reader.pages:
#             text += page.extract_text()
            
#         return text
#     except Exception as e:
#         print(f"Error downloading/processing PDF: {str(e)}")
#         return None
#     finally:
#         # Always quit the PDF driver
#         try:
#             pdf_driver.quit()
#         except:
#             pass

# def extract_financial_data(text):
#     """
#     Extract financial data from the PDF text.
    
#     Args:
#         text (str): PDF text content
        
#     Returns:
#         dict: Dictionary containing financial data
#     """
#     try:
#         # Define patterns for each financial metric
#         patterns = {
#             'net_cash_operating': r'(?:8\.1|Item\s+8\.1).*?Net cash from\s*/\s*\(used in\)\s*operating activities.*?([-]?\d+[,\d]*)',
#             'cash_equivalents': r'(?:8\.2|Item\s+8\.2).*?Cash and cash equivalents at quarter end.*?([-]?\d+[,\d]*)',
#             'unused_finance': r'(?:8\.3|Item\s+8\.3).*?Unused finance facilities available at quarter end.*?([-]?\d+[,\d]*)',
#             'total_funding': r'(?:8\.4|Item\s+8\.4).*?Total available funding.*?([-]?\d+[,\d]*)',
#             'quarters_funding': r'(?:8\.5|Item\s+8\.5).*?Estimated quarters of funding available.*?([-]?\d+[,\d]*)'
#         }
        
#         financial_data = {}
        
#         for key, pattern in patterns.items():
#             match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
#             if match:
#                 value = match.group(1).replace(',', '')
#                 try:
#                     financial_data[key] = float(value)
#                 except ValueError:
#                     financial_data[key] = None
#             else:
#                 financial_data[key] = None
                
#         print(f"Extracted data: {financial_data}")
#         return financial_data
        
#     except Exception as e:
#         print(f"Error extracting financial data: {str(e)}")
#         return None
