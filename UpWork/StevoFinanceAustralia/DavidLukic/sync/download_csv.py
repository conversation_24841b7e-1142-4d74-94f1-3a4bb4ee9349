from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time
import os

def setup_download_directory():
    # Create downloads directory if it doesn't exist
    download_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'downloads')
    os.makedirs(download_dir, exist_ok=True)
    return download_dir

def setup_chrome_options(download_dir):
    chrome_options = Options()
    chrome_options.add_argument('--disable-features=VizDisplayCompositor')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-infobars')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-notifications')
    
    # Set download directory
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    return chrome_options

def download_asx_directory():
    # Setup download directory
    download_dir = setup_download_directory()
    
    # Setup Chrome options
    chrome_options = setup_chrome_options(download_dir)
    
    # Initialize the Chrome driver with webdriver-manager (with path fix)
    driver_path = ChromeDriverManager().install()

    # Fix webdriver-manager bug that points to wrong file
    if 'THIRD_PARTY_NOTICES' in driver_path:
        import os
        driver_dir = os.path.dirname(driver_path)
        actual_driver = os.path.join(driver_dir, 'chromedriver')
        if os.path.exists(actual_driver):
            driver_path = actual_driver
            os.chmod(actual_driver, 0o755)  # Make sure it's executable

    service = Service(driver_path)
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Navigate to the ASX directory page
        url = 'https://www.asx.com.au/markets/trade-our-cash-market/directory'
        driver.get(url)
        
        # Wait for and handle cookie consent if present
        try:
            cookie_button = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "onetrust-accept-btn-handler"))
            )
            cookie_button.click()
            time.sleep(2)
        except:
            print("No cookie consent banner found or already accepted")
        
        # Wait for and click the CSV download link
        csv_link = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'a[aria-label="All ASX listed companies (CSV download)"]'))
        )
        csv_link.click()
        
        # Wait for download to complete
        time.sleep(5)
        
        print(f"CSV file should be downloaded to: {download_dir}")
        return download_dir
        
    except Exception as e:
        print(f"An error occurred: {str(e)}")
    
    finally:
        driver.quit()

# if __name__ == "__main__":
#     download_asx_directory()
