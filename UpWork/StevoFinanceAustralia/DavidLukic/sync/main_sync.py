from download_csv import download_asx_directory
import pandas as pd
import os
import shutil
from announcement_scraper import scrape_announcements
from datetime import datetime

start_time = datetime.now()

## TODO 1. download CSV
csv_location = download_asx_directory()
print(csv_location)

## TODO 2. Get ASX codes and company information
csv_file = [f for f in os.listdir(csv_location) if f.endswith('.csv')][0]
csv_file_path = os.path.join(csv_location, csv_file)

# Load full CSV with company information
df_companies = pd.read_csv(csv_file_path)
print(f"Found {len(df_companies)} companies in ASX directory")

# Create a lookup dictionary for company information
company_lookup = df_companies.set_index('ASX code').to_dict('index')

# Get ASX codes for scraping
asx_codes = df_companies['ASX code'].to_list()
print(f"Found {len(asx_codes)} ASX codes")

def format_currency(value):
    """Format financial values as currency in thousands"""
    if value == '-' or value == 'N/A' or value == 'Not found':
        return value
    try:
        # Remove any existing formatting and convert to float
        if isinstance(value, str):
            clean_value = value.replace(',', '').replace('$', '').replace('(', '').replace(')', '')
            if clean_value == '-' or clean_value.lower() == 'n/a':
                return value
            num_value = float(clean_value)
        else:
            num_value = float(value)

        # Convert to thousands and format with commas
        thousands_value = int(num_value)
        return f"${thousands_value:,},000"
    except (ValueError, TypeError):
        return value

def add_company_info(results, company_lookup):
    """Add company information to results"""
    enhanced_results = []
    for result in results:
        asx_code = result['asx_code']
        if asx_code in company_lookup:
            company_info = company_lookup[asx_code]
            enhanced_result = {
                'asx_code': asx_code,
                'company_name': company_info['Company name'],
                'gics_industry_group': company_info['GICs industry group'],
                'listing_date': company_info['Listing date'],
                'market_cap': company_info['Market Cap'],
                **{k: v for k, v in result.items() if k != 'asx_code'}
            }
            enhanced_results.append(enhanced_result)
        else:
            # If company info not found, add empty fields
            enhanced_result = {
                'asx_code': asx_code,
                'company_name': 'Not found',
                'gics_industry_group': 'Not found',
                'listing_date': 'Not found',
                'market_cap': 'Not found',
                **{k: v for k, v in result.items() if k != 'asx_code'}
            }
            enhanced_results.append(enhanced_result)
    return enhanced_results

def format_financial_values_4c(results):
    """Format 4C financial values (8.1-8.4 as currency, 8.5 as-is)"""
    formatted_results = []
    for result in results.copy():
        result['net_cash_operating_activities'] = format_currency(result.get('net_cash_operating_activities', 'Not found'))
        result['cash_and_cash_equivalents'] = format_currency(result.get('cash_and_cash_equivalents', 'Not found'))
        result['unused_finance_facilities'] = format_currency(result.get('unused_finance_facilities', 'Not found'))
        result['total_available_funding'] = format_currency(result.get('total_available_funding', 'Not found'))
        # Leave 8.5 (estimated_quarters_funding) as-is
        formatted_results.append(result)
    return formatted_results

def format_financial_values_5b(results):
    """Format 5B financial values (8.1-8.6 as currency, 8.7 as-is)"""
    formatted_results = []
    for result in results.copy():
        result['net_cash_operating_activities'] = format_currency(result.get('net_cash_operating_activities', 'Not found'))
        result['payments_exploration_evaluation'] = format_currency(result.get('payments_exploration_evaluation', 'Not found'))
        result['total_relevant_outgoings'] = format_currency(result.get('total_relevant_outgoings', 'Not found'))
        result['cash_and_cash_equivalents'] = format_currency(result.get('cash_and_cash_equivalents', 'Not found'))
        result['unused_finance_facilities'] = format_currency(result.get('unused_finance_facilities', 'Not found'))
        result['total_available_funding'] = format_currency(result.get('total_available_funding', 'Not found'))
        # Leave 8.7 (estimated_quarters_funding) as-is
        formatted_results.append(result)
    return formatted_results

## TODO 3. Scrape announcements and extract financial data
results_4c, results_5b, results2 = scrape_announcements(asx_codes)

# Display results summary
print(f"\nScraping Results Summary:")
print(f"- 4C reports found: {len(results_4c)}")
print(f"- 5B reports found: {len(results_5b)}")
print(f"- Unquoted option reports found: {len(results2)}")

# Display sample data if available
if results_4c:
    print(f"\nSample 4C reports:")
    for i, result in enumerate(results_4c[:3]):  # Show first 3
        print(f"  {i+1}. {result['asx_code']}: {result['title'][:60]}...")

if results_5b:
    print(f"\nSample 5B reports:")
    for i, result in enumerate(results_5b[:3]):  # Show first 3
        print(f"  {i+1}. {result['asx_code']}: {result['title'][:60]}...")

if results2:
    print(f"\nSample unquoted option reports:")
    for i, result in enumerate(results2[:3]):  # Show first 3
        print(f"  {i+1}. {result['asx_code']}: {result['title'][:60]}...")

# Enhance results with company information and format financial values
print(f"\nEnhancing results with company information...")

if results_4c:
    results_4c = add_company_info(results_4c, company_lookup)
    results_4c = format_financial_values_4c(results_4c)

if results_5b:
    results_5b = add_company_info(results_5b, company_lookup)
    results_5b = format_financial_values_5b(results_5b)

if results2:
    results2 = add_company_info(results2, company_lookup)

# Create results folder with timestamp
timestamp = start_time.strftime("%H_%M-%d-%m-%Y")
results_folder = f"results_{timestamp}"
os.makedirs(results_folder, exist_ok=True)
print(f"Created results folder: {results_folder}")

# Generate CSV files
print(f"\nGenerating CSV files...")

# Save 4C reports
if results_4c:
    df_4c = pd.DataFrame(results_4c)
    columns_4c = ['asx_code', 'company_name', 'gics_industry_group', 'listing_date', 'market_cap',
                  'date', 'title', 'url', 'report_type',
                  'net_cash_operating_activities', 'cash_and_cash_equivalents',
                  'unused_finance_facilities', 'total_available_funding',
                  'estimated_quarters_funding']
    df_4c_filtered = df_4c[columns_4c]
    output_4c = os.path.join(results_folder, 'financial_data_4C.csv')
    df_4c_filtered.to_csv(output_4c, index=False)
    print(f"Saved {len(df_4c)} Appendix 4C records to {output_4c}")

# Save 5B reports
if results_5b:
    df_5b = pd.DataFrame(results_5b)
    columns_5b = ['asx_code', 'company_name', 'gics_industry_group', 'listing_date', 'market_cap',
                  'date', 'title', 'url', 'report_type',
                  'net_cash_operating_activities', 'payments_exploration_evaluation',
                  'total_relevant_outgoings', 'cash_and_cash_equivalents',
                  'unused_finance_facilities', 'total_available_funding',
                  'estimated_quarters_funding']
    df_5b_filtered = df_5b[columns_5b]
    output_5b = os.path.join(results_folder, 'financial_data_5B.csv')
    df_5b_filtered.to_csv(output_5b, index=False)
    print(f"Saved {len(df_5b)} Appendix 5B records to {output_5b}")

# Save unquoted option data
if results2:
    df2 = pd.DataFrame(results2)
    output_2 = os.path.join(results_folder, 'unquoted_option_data.csv')
    df2.to_csv(output_2, index=False)
    print(f"Saved {len(results2)} unquoted option records to {output_2}")

# Copy the downloaded CSV to results folder
downloaded_csv_dest = os.path.join(results_folder, f'asx_directory_{timestamp}.csv')
shutil.copy2(csv_file_path, downloaded_csv_dest)
print(f"Copied ASX directory CSV to {downloaded_csv_dest}")

end_time = datetime.now()
execution_time = end_time - start_time
execution_time_formatted = f"{execution_time.days} days, {execution_time.seconds // 3600} hours, {(execution_time.seconds // 60) % 60} minutes"
print(f"\nExecution time: {execution_time_formatted}")
print(f"All results saved to folder: {results_folder}")

