import requests
import PyPDF2
import io
import re

needed_text = """
So let's use the highlighted unquoted example on my attached pic, the outputs would look like
Code: 14DAH
Expiry: 21 Aug 25
Strike/exercise price: $0.10
Options on issue: 35,663,063

"""

def download_pdf(url):
    try:
        response = requests.get(url)
        response.raise_for_status()
        return io.BytesIO(response.content)
    except requests.exceptions.RequestException as e:
        print(f"Error downloading PDF: {e}")
        return None

def extract_text_from_pdf(pdf_file):
    try:
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text() + "\n"
        print(text)
        return text
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return None

def parse_unquoted_option_data(text, code=None):
    # Search the entire text for option-like lines
    option_pattern = re.compile(
        r"(?P<code>[A-Z0-9]{4,6})\s*:\s*OPTION EXPIRING (?P<expiry>\d{2}-[A-Z]{3}-\d{4}) EX \$(?P<strike>[\d\.]+)\s+(?P<on_issue>[\d,]+)",
        re.IGNORECASE
    )
    results = []
    for match in option_pattern.finditer(text):
        print("MATCHED LINE:", match.group(0))
        d = match.groupdict()
        # Format expiry as '21 Aug 25' from '21-AUG-2025'
        try:
            from datetime import datetime
            expiry_dt = datetime.strptime(d['expiry'], '%d-%b-%Y')
            d['expiry'] = expiry_dt.strftime('%d %b %y')
        except Exception:
            pass
        d['strike'] = f"${d['strike']}"
        # Remove any non-digit/comma after the number
        d['on_issue'] = re.match(r"[\d,]+", d['on_issue']).group(0)
        results.append({
            'Code': d['code'],
            'Expiry': d['expiry'],
            'Strike/exercise price': d['strike'],
            'Options on issue': d['on_issue']
        })
    # If a specific code is requested, filter for it
    if code:
        for r in results:
            if r['Code'] == code:
                return r
        return None
    return results[0] if results else None

def extract_unquoted_option_data(url, code=None):
    pdf_file = download_pdf(url)
    if not pdf_file:
        return None
    text = extract_text_from_pdf(pdf_file)
    if not text:
        return None
    return parse_unquoted_option_data(text, code=code)

# Example usage:
# if __name__ == "__main__":
#     url = "https://cdn-api.markitdigital.com/apiman-gateway/ASX/asx-research/1.0/file/2924-02884518-2A1563750&v=04711220c3a57065317ba4efca4a3459a4e46882"
#     result = extract_unquoted_option_data(url)
#     print(result)
#     pass

