#!/usr/bin/env python3
"""
Test script to verify ASX directory download functionality.
"""

import os
import sys
from scraper_functions import safe_download_asx_directory

def test_asx_download():
    """Test ASX directory download"""
    print("🧪 Testing ASX Directory Download")
    print("=" * 40)
    
    try:
        print("📥 Attempting to download ASX directory...")
        download_dir = safe_download_asx_directory()
        
        if download_dir:
            print(f"✅ Download successful!")
            print(f"📁 Download directory: {download_dir}")
            
            # Check if files were downloaded
            if os.path.exists(download_dir):
                files = os.listdir(download_dir)
                csv_files = [f for f in files if f.endswith('.csv')]
                
                print(f"📊 Files in download directory:")
                for file in files:
                    print(f"   • {file}")
                
                if csv_files:
                    print(f"✅ Found {len(csv_files)} CSV file(s)")
                    
                    # Check the first CSV file
                    first_csv = os.path.join(download_dir, csv_files[0])
                    try:
                        import pandas as pd
                        df = pd.read_csv(first_csv)
                        print(f"📈 CSV file has {len(df)} rows and {len(df.columns)} columns")
                        print(f"📋 Columns: {list(df.columns)}")
                        
                        if len(df) > 0:
                            print("✅ ASX directory download test PASSED!")
                            return True
                        else:
                            print("⚠️  CSV file is empty")
                            return False
                            
                    except Exception as e:
                        print(f"❌ Error reading CSV file: {e}")
                        return False
                else:
                    print("⚠️  No CSV files found in download directory")
                    return False
            else:
                print("❌ Download directory does not exist")
                return False
        else:
            print("❌ Download failed - no directory returned")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Main function"""
    print("🎯 ASX Download Test")
    print("=" * 30)
    
    success = test_asx_download()
    
    if success:
        print("\n🎉 All tests passed! ASX download functionality is working.")
        print("\n💡 You can now use the scraper in the Streamlit app:")
        print("   1. Go to the Data Scraper page")
        print("   2. Select a few ASX codes (e.g., CBA, BHP)")
        print("   3. Choose report types")
        print("   4. Click 'Start Scraping'")
    else:
        print("\n❌ Tests failed. Please check the error messages above.")
        print("\n🔧 Troubleshooting steps:")
        print("   1. Ensure Chrome browser is installed")
        print("   2. Check internet connection")
        print("   3. Run: python test_chromedriver.py")
        print("   4. Try: pip install --upgrade webdriver-manager")

if __name__ == "__main__":
    main()
