#!/usr/bin/env python3
"""
Test script to verify Chrome driver setup and fix any issues.
"""

import os
import sys
import glob
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

def test_chromedriver():
    """Test Chrome driver setup"""
    print("🔧 Testing Chrome Driver Setup")
    print("=" * 40)

    try:
        # Import the fixed chromedriver function
        sys.path.append(os.path.dirname(__file__))
        from scraper_functions import get_correct_chromedriver_path

        # Get the corrected driver path
        print("📥 Getting corrected Chrome driver path...")
        driver_path = get_correct_chromedriver_path()

        if not driver_path:
            print("❌ Could not get Chrome driver path")
            return False

        print(f"🎯 Using driver path: {driver_path}")

        # Test the driver
        print("\n🧪 Testing Chrome driver...")

        chrome_options = Options()
        chrome_options.add_argument('--headless=new')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')

        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)

        try:
            # Test navigation
            print("   Navigating to Google...")
            driver.get("https://www.google.com")
            title = driver.title
            print(f"   Page title: {title}")

            if "Google" in title:
                print("✅ Chrome driver test successful!")
                return True
            else:
                print("❌ Unexpected page title")
                return False

        finally:
            driver.quit()

    except Exception as e:
        print(f"❌ Chrome driver test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_chromedriver_permissions():
    """Fix Chrome driver permissions"""
    print("\n🔧 Fixing Chrome driver permissions...")
    
    try:
        # Find all chromedriver files
        home_dir = os.path.expanduser("~")
        wdm_dir = os.path.join(home_dir, ".wdm")
        
        if os.path.exists(wdm_dir):
            chromedriver_files = glob.glob(os.path.join(wdm_dir, "**/chromedriver"), recursive=True)
            
            for driver_file in chromedriver_files:
                print(f"   Making executable: {driver_file}")
                os.chmod(driver_file, 0o755)
            
            print(f"✅ Fixed permissions for {len(chromedriver_files)} chromedriver files")
        else:
            print("⚠️  WebDriver Manager directory not found")
            
    except Exception as e:
        print(f"❌ Error fixing permissions: {e}")

def clean_chromedriver_cache():
    """Clean Chrome driver cache and re-download"""
    print("\n🧹 Cleaning Chrome driver cache...")
    
    try:
        import shutil
        
        home_dir = os.path.expanduser("~")
        wdm_dir = os.path.join(home_dir, ".wdm")
        
        if os.path.exists(wdm_dir):
            print(f"   Removing: {wdm_dir}")
            shutil.rmtree(wdm_dir)
            print("✅ Chrome driver cache cleaned")
            
            # Re-download
            print("📥 Re-downloading Chrome driver...")
            driver_path = ChromeDriverManager().install()
            print(f"✅ New driver downloaded: {driver_path}")
            
            return driver_path
        else:
            print("⚠️  No cache to clean")
            return None
            
    except Exception as e:
        print(f"❌ Error cleaning cache: {e}")
        return None

def main():
    """Main function"""
    print("🚗 Chrome Driver Diagnostic Tool")
    print("=" * 50)
    
    # Test current setup
    if test_chromedriver():
        print("\n🎉 Chrome driver is working correctly!")
        return
    
    print("\n🔧 Chrome driver test failed. Attempting fixes...")
    
    # Try fixing permissions
    fix_chromedriver_permissions()
    
    # Test again
    if test_chromedriver():
        print("\n🎉 Chrome driver fixed with permission changes!")
        return
    
    # Try cleaning cache and re-downloading
    print("\n🧹 Trying cache cleanup and re-download...")
    clean_chromedriver_cache()
    
    # Test final time
    if test_chromedriver():
        print("\n🎉 Chrome driver fixed with cache cleanup!")
    else:
        print("\n❌ Chrome driver still not working. Manual intervention may be required.")
        print("\n💡 Suggestions:")
        print("   1. Install Chrome browser: sudo apt install google-chrome-stable")
        print("   2. Update Chrome: google-chrome --version")
        print("   3. Clear all webdriver cache: rm -rf ~/.wdm")
        print("   4. Try running: pip install --upgrade webdriver-manager")

if __name__ == "__main__":
    main()
