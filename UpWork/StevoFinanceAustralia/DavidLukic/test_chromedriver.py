#!/usr/bin/env python3
"""
Test script to verify Chrome driver setup and fix any issues.
"""

import os
import sys
import glob
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

def test_chromedriver():
    """Test Chrome driver setup"""
    print("🔧 Testing Chrome Driver Setup")
    print("=" * 40)
    
    try:
        # Get the driver path from webdriver-manager
        print("📥 Getting Chrome driver path...")
        driver_path = ChromeDriverManager().install()
        print(f"   Initial path: {driver_path}")
        
        # Check if it's pointing to the wrong file
        if 'THIRD_PARTY_NOTICES' in driver_path:
            print("⚠️  Driver path points to THIRD_PARTY_NOTICES file")
            
            # Find the actual chromedriver executable
            driver_dir = os.path.dirname(driver_path)
            actual_driver = os.path.join(driver_dir, 'chromedriver')
            
            print(f"   Looking for actual driver in: {driver_dir}")
            
            if os.path.exists(actual_driver):
                print(f"✅ Found actual driver: {actual_driver}")
                
                # Check if it's executable
                if os.access(actual_driver, os.X_OK):
                    print("✅ Driver is executable")
                    driver_path = actual_driver
                else:
                    print("❌ Driver is not executable")
                    # Make it executable
                    os.chmod(actual_driver, 0o755)
                    print("✅ Made driver executable")
                    driver_path = actual_driver
            else:
                print("❌ Actual driver not found in expected location")
                
                # Search for chromedriver in parent directories
                parent_dir = os.path.dirname(driver_dir)
                print(f"   Searching in parent directory: {parent_dir}")
                
                possible_drivers = glob.glob(os.path.join(parent_dir, '**/chromedriver'), recursive=True)
                print(f"   Found possible drivers: {possible_drivers}")
                
                for possible_driver in possible_drivers:
                    if os.access(possible_driver, os.X_OK):
                        print(f"✅ Using executable driver: {possible_driver}")
                        driver_path = possible_driver
                        break
                else:
                    print("❌ No executable chromedriver found")
                    return False
        else:
            print("✅ Driver path looks correct")
        
        print(f"🎯 Final driver path: {driver_path}")
        
        # Test the driver
        print("\n🧪 Testing Chrome driver...")
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        try:
            # Test navigation
            print("   Navigating to Google...")
            driver.get("https://www.google.com")
            title = driver.title
            print(f"   Page title: {title}")
            
            if "Google" in title:
                print("✅ Chrome driver test successful!")
                return True
            else:
                print("❌ Unexpected page title")
                return False
                
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"❌ Chrome driver test failed: {e}")
        return False

def fix_chromedriver_permissions():
    """Fix Chrome driver permissions"""
    print("\n🔧 Fixing Chrome driver permissions...")
    
    try:
        # Find all chromedriver files
        home_dir = os.path.expanduser("~")
        wdm_dir = os.path.join(home_dir, ".wdm")
        
        if os.path.exists(wdm_dir):
            chromedriver_files = glob.glob(os.path.join(wdm_dir, "**/chromedriver"), recursive=True)
            
            for driver_file in chromedriver_files:
                print(f"   Making executable: {driver_file}")
                os.chmod(driver_file, 0o755)
            
            print(f"✅ Fixed permissions for {len(chromedriver_files)} chromedriver files")
        else:
            print("⚠️  WebDriver Manager directory not found")
            
    except Exception as e:
        print(f"❌ Error fixing permissions: {e}")

def clean_chromedriver_cache():
    """Clean Chrome driver cache and re-download"""
    print("\n🧹 Cleaning Chrome driver cache...")
    
    try:
        import shutil
        
        home_dir = os.path.expanduser("~")
        wdm_dir = os.path.join(home_dir, ".wdm")
        
        if os.path.exists(wdm_dir):
            print(f"   Removing: {wdm_dir}")
            shutil.rmtree(wdm_dir)
            print("✅ Chrome driver cache cleaned")
            
            # Re-download
            print("📥 Re-downloading Chrome driver...")
            driver_path = ChromeDriverManager().install()
            print(f"✅ New driver downloaded: {driver_path}")
            
            return driver_path
        else:
            print("⚠️  No cache to clean")
            return None
            
    except Exception as e:
        print(f"❌ Error cleaning cache: {e}")
        return None

def main():
    """Main function"""
    print("🚗 Chrome Driver Diagnostic Tool")
    print("=" * 50)
    
    # Test current setup
    if test_chromedriver():
        print("\n🎉 Chrome driver is working correctly!")
        return
    
    print("\n🔧 Chrome driver test failed. Attempting fixes...")
    
    # Try fixing permissions
    fix_chromedriver_permissions()
    
    # Test again
    if test_chromedriver():
        print("\n🎉 Chrome driver fixed with permission changes!")
        return
    
    # Try cleaning cache and re-downloading
    print("\n🧹 Trying cache cleanup and re-download...")
    clean_chromedriver_cache()
    
    # Test final time
    if test_chromedriver():
        print("\n🎉 Chrome driver fixed with cache cleanup!")
    else:
        print("\n❌ Chrome driver still not working. Manual intervention may be required.")
        print("\n💡 Suggestions:")
        print("   1. Install Chrome browser: sudo apt install google-chrome-stable")
        print("   2. Update Chrome: google-chrome --version")
        print("   3. Clear all webdriver cache: rm -rf ~/.wdm")
        print("   4. Try running: pip install --upgrade webdriver-manager")

if __name__ == "__main__":
    main()
