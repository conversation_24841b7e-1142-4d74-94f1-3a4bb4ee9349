# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/FileUploader.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import LabelVisibilityMessage_pb2 as streamlit_dot_proto_dot_LabelVisibilityMessage__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"streamlit/proto/FileUploader.proto\x1a,streamlit/proto/LabelVisibilityMessage.proto\"\xd5\x01\n\x0c\x46ileUploader\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05label\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x03(\t\x12\x1a\n\x12max_upload_size_mb\x18\x04 \x01(\x05\x12\x16\n\x0emultiple_files\x18\x06 \x01(\x08\x12\x0c\n\x04help\x18\x07 \x01(\t\x12\x0f\n\x07\x66orm_id\x18\x08 \x01(\t\x12\x10\n\x08\x64isabled\x18\t \x01(\x08\x12\x31\n\x10label_visibility\x18\n \x01(\x0b\x32\x17.LabelVisibilityMessageJ\x04\x08\x05\x10\x06\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.FileUploader_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _FILEUPLOADER._serialized_start=85
  _FILEUPLOADER._serialized_end=298
# @@protoc_insertion_point(module_scope)
