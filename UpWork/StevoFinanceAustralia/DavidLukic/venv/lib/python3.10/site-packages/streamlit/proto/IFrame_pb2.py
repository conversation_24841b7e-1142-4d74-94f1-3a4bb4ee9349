# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/IFrame.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cstreamlit/proto/IFrame.proto\"v\n\x06IFrame\x12\r\n\x03src\x18\x01 \x01(\tH\x00\x12\x10\n\x06srcdoc\x18\x02 \x01(\tH\x00\x12\r\n\x05width\x18\x03 \x01(\x02\x12\x11\n\thas_width\x18\x04 \x01(\x08\x12\x0e\n\x06height\x18\x05 \x01(\x02\x12\x11\n\tscrolling\x18\x07 \x01(\x08\x42\x06\n\x04typeb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.IFrame_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _IFRAME._serialized_start=32
  _IFRAME._serialized_end=150
# @@protoc_insertion_point(module_scope)
