# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/LabelVisibilityMessage.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,streamlit/proto/LabelVisibilityMessage.proto\"\x99\x01\n\x16LabelVisibilityMessage\x12=\n\x05value\x18\x01 \x01(\x0e\x32..LabelVisibilityMessage.LabelVisibilityOptions\"@\n\x16LabelVisibilityOptions\x12\x0b\n\x07VISIBLE\x10\x00\x12\n\n\x06HIDDEN\x10\x01\x12\r\n\tCOLLAPSED\x10\x02\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.LabelVisibilityMessage_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _LABELVISIBILITYMESSAGE._serialized_start=49
  _LABELVISIBILITYMESSAGE._serialized_end=202
  _LABELVISIBILITYMESSAGE_LABELVISIBILITYOPTIONS._serialized_start=138
  _LABELVISIBILITYMESSAGE_LABELVISIBILITYOPTIONS._serialized_end=202
# @@protoc_insertion_point(module_scope)
