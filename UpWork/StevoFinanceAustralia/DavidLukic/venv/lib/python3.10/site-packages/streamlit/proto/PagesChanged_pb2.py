# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/PagesChanged.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import AppPage_pb2 as streamlit_dot_proto_dot_AppPage__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"streamlit/proto/PagesChanged.proto\x1a\x1dstreamlit/proto/AppPage.proto\"+\n\x0cPagesChanged\x12\x1b\n\tapp_pages\x18\x01 \x03(\x0b\x32\x08.AppPageb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.PagesChanged_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PAGESCHANGED._serialized_start=69
  _PAGESCHANGED._serialized_end=112
# @@protoc_insertion_point(module_scope)
