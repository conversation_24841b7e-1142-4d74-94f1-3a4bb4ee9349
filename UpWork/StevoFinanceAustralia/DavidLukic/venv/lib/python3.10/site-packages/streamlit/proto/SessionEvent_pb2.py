# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/SessionEvent.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import Exception_pb2 as streamlit_dot_proto_dot_Exception__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"streamlit/proto/SessionEvent.proto\x1a\x1fstreamlit/proto/Exception.proto\"\x93\x01\n\x0cSessionEvent\x12 \n\x16script_changed_on_disk\x18\x01 \x01(\x08H\x00\x12%\n\x1bscript_was_manually_stopped\x18\x02 \x01(\x08H\x00\x12\x32\n\x1cscript_compilation_exception\x18\x03 \x01(\x0b\x32\n.ExceptionH\x00\x42\x06\n\x04typeb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.SessionEvent_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _SESSIONEVENT._serialized_start=72
  _SESSIONEVENT._serialized_end=219
# @@protoc_insertion_point(module_scope)
