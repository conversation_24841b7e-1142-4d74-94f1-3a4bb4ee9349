"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[7175],{79986:function(e,t,n){n.d(t,{Z:function(){return m}});n(66845);var r,i=n(50641),o=n(86659),a=n(50189),l=n(50669),s=n(1515),u=(0,n(7865).F4)(r||(r=(0,l.Z)(["\n  50% {\n    color: rgba(0, 0, 0, 0);\n  }\n"]))),d=(0,s.Z)("span",{target:"edlqvik0"})((function(e){var t=e.includeDot,n=e.shouldBlink,r=e.theme;return(0,a.Z)((0,a.Z)({},t?{"&::before":{opacity:1,content:'"\u2022"',animation:"none",color:r.colors.gray,margin:"0 5px"}}:{}),n?{color:r.colors.red,animationName:"".concat(u),animationDuration:"0.5s",animationIterationCount:5}:{})}),""),c=n(40864),m=function(e){var t=e.dirty,n=e.value,r=e.maxLength,a=e.className,l=e.type,s=void 0===l?"single":l,u=e.inForm,m=[],p=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];m.push((0,c.jsx)(d,{includeDot:m.length>0,shouldBlink:t,children:e},m.length))};if(t){var h=u?"submit form":"apply";if("multiline"===s){var f=(0,i.Ge)()?"\u2318":"Ctrl";p("Press ".concat(f,"+Enter to ").concat(h))}else"single"===s&&p("Press Enter to ".concat(h))}return r&&("chat"!==s||t)&&p("".concat(n.length,"/").concat(r),t&&n.length>=r),(0,c.jsx)(o.X7,{"data-testid":"InputInstructions",className:a,children:m})}},87814:function(e,t,n){n.d(t,{K:function(){return a}});var r=n(22951),i=n(91976),o=n(50641),a=function(){function e(){(0,r.Z)(this,e),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}return(0,i.Z)(e,[{key:"manageFormClearListener",value:function(e,t,n){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,o.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,n),this.lastWidgetMgr=e,this.lastFormId=t))}},{key:"disconnect",value:function(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}]),e}()},67175:function(e,t,n){n.r(t),n.d(t,{default:function(){return W}});var r=n(22951),i=n(91976),o=n(67591),a=n(94337),l=n(66845),s=n(20607),u=n(89997),d=n(25621),c=n(52347),m=n(87814),p=n(23849),h=n(16295),f=n(48266),g=n(8879),v=n(68411),b=n(46927),y=n(82534),x=n(79986),k=n(98478),C=n(86659),V=n(50641),I=n(1515);var w=(0,I.Z)("div",{target:"e116k4er3"})((function(e){var t=e.theme;return{display:"flex",flexDirection:"row",flexWrap:"nowrap",alignItems:"center",borderWidth:"1px",borderStyle:"solid",borderColor:t.colors.widgetBorderColor||t.colors.widgetBackgroundColor||t.colors.bgColor,transitionDuration:"200ms",transitionProperty:"border",transitionTimingFunction:"cubic-bezier(0.2, 0.8, 0.4, 1)",borderRadius:t.radii.lg,overflow:"hidden","&.focused":{borderColor:t.colors.primary},input:{MozAppearance:"textfield","&::-webkit-inner-spin-button, &::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:t.spacing.none}}}}),""),F=(0,I.Z)("div",{target:"e116k4er2"})({name:"76z9jo",styles:"display:flex;flex-direction:row;align-self:stretch"}),D=(0,I.Z)("button",{target:"e116k4er1"})((function(e){var t=e.theme;return{margin:t.spacing.none,border:"none",height:t.sizes.full,display:"flex",alignItems:"center",width:"".concat(32,"px"),justifyContent:"center",color:t.colors.bodyText,transition:"color 300ms, backgroundColor 300ms",backgroundColor:t.colors.widgetBackgroundColor||t.colors.secondaryBg,"&:hover:enabled, &:focus:enabled":{color:t.colors.white,backgroundColor:t.colors.primary,transition:"none",outline:"none"},"&:active":{outline:"none",border:"none"},"&:last-of-type":{borderTopRightRadius:t.radii.lg,borderBottomRightRadius:t.radii.lg},"&:disabled":{cursor:"not-allowed",color:t.colors.fadedText40}}}),""),M=(0,I.Z)("div",{target:"e116k4er0"})((function(e){var t=e.theme,n=e.clearable;return{position:"absolute",marginRight:t.spacing.twoXS,left:0,right:"".concat(64+(n?12:0),"px")}}),""),R=n(40864),S=function(e){(0,o.Z)(n,e);var t=(0,a.Z)(n);function n(e){var i;return(0,r.Z)(this,n),(i=t.call(this,e)).formClearHelper=new m.K,i.inputRef=l.createRef(),i.formatValue=function(e){if((0,V.le)(e))return null;var t=function(e){return null==e||""===e?void 0:e}(i.props.element.format);if(null==t)return e.toString();try{return(0,c.sprintf)(t,e)}catch(n){return(0,p.KE)("Error in sprintf(".concat(t,", ").concat(e,"): ").concat(n)),String(e)}},i.isIntData=function(){return i.props.element.dataType===h.Y2.DataType.INT},i.getMin=function(){return i.props.element.hasMin?i.props.element.min:-1/0},i.getMax=function(){return i.props.element.hasMax?i.props.element.max:1/0},i.getStep=function(){var e=i.props.element.step;return e||(i.isIntData()?1:.01)},i.commitWidgetValue=function(e){var t=i.state.value,n=i.props,r=n.element,o=n.widgetMgr,a=i.props.element,l=i.getMin(),s=i.getMax();if((0,V.bb)(t)&&(l>t||t>s)){var u=i.inputRef.current;u&&u.reportValidity()}else{var d,c=null!==(d=null!==t&&void 0!==t?t:a.default)&&void 0!==d?d:null;i.isIntData()?o.setIntValue(r,c,e):o.setDoubleValue(r,c,e),i.setState({dirty:!1,value:c,formattedValue:i.formatValue(c)})}},i.onFormCleared=function(){i.setState((function(e,t){var n;return{value:null!==(n=t.element.default)&&void 0!==n?n:null}}),(function(){return i.commitWidgetValue({fromUi:!0})}))},i.onBlur=function(){i.state.dirty&&i.commitWidgetValue({fromUi:!0}),i.setState({isFocused:!1})},i.onFocus=function(){i.setState({isFocused:!0})},i.onChange=function(e){var t,n=e.target.value;""===n?i.setState({dirty:!0,value:null,formattedValue:null}):(t=i.isIntData()?parseInt(n,10):parseFloat(n),i.setState({dirty:!0,value:t,formattedValue:n}))},i.onKeyDown=function(e){switch(e.key){case"ArrowUp":e.preventDefault(),i.modifyValueUsingStep("increment")();break;case"ArrowDown":e.preventDefault(),i.modifyValueUsingStep("decrement")()}},i.onKeyPress=function(e){"Enter"===e.key&&(i.state.dirty&&i.commitWidgetValue({fromUi:!0}),(0,V.$b)(i.props.element)&&i.props.widgetMgr.submitForm(i.props.element.formId))},i.modifyValueUsingStep=function(e){return function(){var t=i.state.value,n=i.getStep();switch(e){case"increment":i.canIncrement&&i.setState({dirty:!0,value:(null!==t&&void 0!==t?t:i.getMin())+n},(function(){i.commitWidgetValue({fromUi:!0})}));break;case"decrement":i.canDecrement&&i.setState({dirty:!0,value:(null!==t&&void 0!==t?t:i.getMax())-n},(function(){i.commitWidgetValue({fromUi:!0})}))}}},i.state={dirty:!1,value:i.initialValue,formattedValue:i.formatValue(i.initialValue),isFocused:!1},i}return(0,i.Z)(n,[{key:"initialValue",get:function(){var e,t=this.isIntData()?this.props.widgetMgr.getIntValue(this.props.element):this.props.widgetMgr.getDoubleValue(this.props.element);return null!==(e=null!==t&&void 0!==t?t:this.props.element.default)&&void 0!==e?e:null}},{key:"componentDidMount",value:function(){this.props.element.setValue?this.updateFromProtobuf():this.commitWidgetValue({fromUi:!1})}},{key:"componentDidUpdate",value:function(){this.maybeUpdateFromProtobuf()}},{key:"componentWillUnmount",value:function(){this.formClearHelper.disconnect()}},{key:"maybeUpdateFromProtobuf",value:function(){this.props.element.setValue&&this.updateFromProtobuf()}},{key:"updateFromProtobuf",value:function(){var e=this,t=this.props.element.value;this.props.element.setValue=!1,this.setState({value:null!==t&&void 0!==t?t:null,formattedValue:this.formatValue(null!==t&&void 0!==t?t:null)},(function(){e.commitWidgetValue({fromUi:!1})}))}},{key:"canDecrement",get:function(){return!(0,V.le)(this.state.value)&&this.state.value-this.getStep()>=this.getMin()}},{key:"canIncrement",get:function(){return!(0,V.le)(this.state.value)&&this.state.value+this.getStep()<=this.getMax()}},{key:"render",value:function(){var e,t=this.props,n=t.element,r=t.width,i=t.disabled,o=t.widgetMgr,a=t.theme,l=this.state,d=l.formattedValue,c=l.dirty,m=l.isFocused,p={width:r},h=!this.canDecrement||i,I=!this.canIncrement||i,S=(0,V.le)(n.default)&&!i;return this.formClearHelper.manageFormClearListener(o,n.formId,this.onFormCleared),(0,R.jsxs)("div",{className:"stNumberInput",style:p,"data-testid":"stNumberInput",children:[(0,R.jsx)(k.O,{label:n.label,disabled:i,labelVisibility:(0,V.iF)(null===(e=n.labelVisibility)||void 0===e?void 0:e.value),children:n.help&&(0,R.jsx)(C.dT,{children:(0,R.jsx)(g.Z,{content:n.help,placement:v.u.TOP_RIGHT})})}),(0,R.jsxs)(w,{className:m?"focused":"",children:[(0,R.jsx)(y.Z,{type:"number",inputRef:this.inputRef,value:null!==d&&void 0!==d?d:"",placeholder:n.placeholder,onBlur:this.onBlur,onFocus:this.onFocus,onChange:this.onChange,onKeyPress:this.onKeyPress,onKeyDown:this.onKeyDown,clearable:S,clearOnEscape:S,disabled:i,"aria-label":n.label,overrides:{ClearIcon:{props:{overrides:{Svg:{style:{color:a.colors.darkGray,transform:"scale(1.4)",width:a.spacing.twoXL,marginRight:"-1.25em",":hover":{fill:a.colors.bodyText}}}}}},Input:{props:{step:this.getStep(),min:this.getMin(),max:this.getMax()},style:{lineHeight:"1.4",paddingRight:".5rem",paddingLeft:".5rem",paddingBottom:".5rem",paddingTop:".5rem"}},InputContainer:{style:function(){return{borderTopRightRadius:0,borderBottomRightRadius:0}}},Root:{style:function(){return{borderTopRightRadius:0,borderBottomRightRadius:0,borderLeftWidth:0,borderRightWidth:0,borderTopWidth:0,borderBottomWidth:0}}}}}),r>f.A.hideNumberInputControls&&(0,R.jsxs)(F,{children:[(0,R.jsx)(D,{className:"step-down",onClick:this.modifyValueUsingStep("decrement"),disabled:h,tabIndex:-1,children:(0,R.jsx)(b.Z,{content:s.W,size:"xs",color:this.canDecrement?"inherit":"disabled"})}),(0,R.jsx)(D,{className:"step-up",onClick:this.modifyValueUsingStep("increment"),disabled:I,tabIndex:-1,children:(0,R.jsx)(b.Z,{content:u.v,size:"xs",color:this.canIncrement?"inherit":"disabled"})})]})]}),r>f.A.hideWidgetDetails&&(0,R.jsx)(M,{clearable:S,children:(0,R.jsx)(x.Z,{dirty:c,value:null!==d&&void 0!==d?d:"",className:"input-instructions",inForm:(0,V.$b)({formId:n.formId})})})]})}}]),n}(l.PureComponent);var W=(0,d.b)(S)}}]);