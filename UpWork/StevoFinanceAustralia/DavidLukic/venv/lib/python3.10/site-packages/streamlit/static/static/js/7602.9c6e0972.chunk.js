"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[7602],{90186:function(e,t,n){n.d(t,{$:function(){return i}});var i,r=n(66845),o=n(25621),s=n(66694),a=n(27466),l=n(38570),c=n(80318),u=n(40864);!function(e){e.EXTRASMALL="xs",e.SMALL="sm",e.MEDIUM="md",e.LARGE="lg",e.EXTRALARGE="xl"}(i||(i={})),t.Z=function(e){var t=e.value,n=e.width,d=e.size,g=void 0===d?i.SMALL:d,f=e.overrides,p=(0,o.u)(),h={xs:p.spacing.twoXS,sm:p.spacing.sm,md:p.spacing.lg,lg:p.spacing.xl,xl:p.spacing.twoXL},m=r.useContext(s.E).activeTheme,S=!(0,a.MJ)(m),v={BarContainer:{style:{marginTop:p.spacing.none,marginBottom:p.spacing.none,marginRight:p.spacing.none,marginLeft:p.spacing.none}},Bar:{style:function(e){var t=e.$theme;return{width:n?n.toString():void 0,marginTop:p.spacing.none,marginBottom:p.spacing.none,marginRight:p.spacing.none,marginLeft:p.spacing.none,height:h[g],backgroundColor:t.colors.progressbarTrackFill,borderTopLeftRadius:p.spacing.twoXS,borderTopRightRadius:p.spacing.twoXS,borderBottomLeftRadius:p.spacing.twoXS,borderBottomRightRadius:p.spacing.twoXS}}},BarProgress:{style:function(){return{backgroundColor:S?p.colors.primary:p.colors.blue70,borderTopLeftRadius:p.spacing.twoXS,borderTopRightRadius:p.spacing.twoXS,borderBottomLeftRadius:p.spacing.twoXS,borderBottomRightRadius:p.spacing.twoXS}}}};return(0,u.jsx)(l.Z,{value:t,overrides:(0,c.aO)(v,f)})}},47602:function(e,t,n){n.r(t),n.d(t,{default:function(){return ne}});var i,r=n(649),o=n(22951),s=n(91976),a=n(67591),l=n(94337),c=n(47869),u=n(45960),d=n.n(u),g=n(72706),f=n.n(g),p=n(66845),h=n(16295),m=n(46927),S=n(68411),v=n(8879),R=n(98478),C=n(86659),w=n(87814),x=n(23849),b=n(50641),y=n(77367),F=n(90186),I=n(50189),M=n(1515),E=n(35704);!function(e){e.XSMALL="xsmall",e.SMALL="small",e.MEDIUM="medium",e.LARGE="large"}(i||(i={}));var k=(0,M.Z)("div",{target:"etz5kuj9"})((function(){return{position:"relative",overflow:"hidden",width:"100%",objectFit:"contain"}}),""),T=(0,M.Z)("div",{target:"etz5kuj8"})((function(e){var t=e.theme,n=e.width;return{backgroundColor:t.colors.secondaryBg,borderRadius:"".concat(t.radii.lg," ").concat(t.radii.lg," 0 0"),width:"100%",height:9*n/16,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"}}),""),j=(0,M.Z)("p",{target:"etz5kuj7"})((function(e){return{marginTop:e.theme.spacing.sm,textAlign:"center"}}),""),L=(0,M.Z)("img",{target:"etz5kuj6"})((function(e){var t=e.theme,n=e.opacity;return{borderRadius:"".concat(t.radii.lg," ").concat(t.radii.lg," 0 0"),objectFit:"contain",opacity:n}}),""),U=(0,M.Z)("a",{target:"etz5kuj5"})((function(e){return{color:e.theme.colors.primary,display:"block",textDecoration:"none"}}),""),P=(0,M.Z)("span",{target:"etz5kuj4"})((function(){return{display:"flex",alignItems:"center"}}),""),Z=(0,M.Z)("div",{target:"etz5kuj3"})((function(e){var t=e.theme;return{position:"absolute",top:t.spacing.lg,right:t.spacing.lg,zIndex:1,color:t.colors.fadedText40,mixBlendMode:"difference",opacity:.6}}),""),z=(0,M.Z)("div",{target:"etz5kuj1"})((function(){return{height:"fit-content",width:"100%",position:"absolute",bottom:0}}),""),D=(0,M.Z)("button",{target:"etz5kuj0"})((function(e){var t=e.theme;return(0,I.Z)({position:"relative",display:"inline-flex",flexDirection:"column",alignItems:"center",justifyContent:"center",backgroundColor:t.colors.lightenedBg05,border:"1px solid ".concat(t.colors.fadedText10),borderRadius:"0 0 ".concat(t.radii.lg," ").concat(t.radii.lg),"&:hover":{borderColor:t.colors.primary,color:t.colors.primary},"&:active":{color:t.colors.white,borderColor:t.colors.primary,backgroundColor:t.colors.primary},"&:focus:not(:active)":{borderColor:t.colors.primary,color:t.colors.primary},"&:disabled, &:disabled:hover, &:disabled:active":{color:t.colors.fadedText40},fontWeight:t.fontWeights.normal,padding:"".concat(t.spacing.xs," ").concat(t.spacing.md),margin:0,lineHeight:t.lineHeights.base,color:"inherit",width:"100%",userSelect:"none","&:focus":{outline:"none"},"&:focus-visible":{boxShadow:"0 0 0 0.2rem ".concat((0,E.DZ)(t.colors.primary,.5))}},function(e,t){switch(e){case i.XSMALL:return{padding:"".concat(t.spacing.twoXS," ").concat(t.spacing.sm),fontSize:t.fontSizes.sm};case i.SMALL:return{padding:"".concat(t.spacing.twoXS," ").concat(t.spacing.md)};case i.LARGE:return{padding:"".concat(t.spacing.md," ").concat(t.spacing.md)};default:return{padding:"".concat(t.spacing.xs," ").concat(t.spacing.md)}}}(i.MEDIUM,t))}),""),X=n(40864);var _,O=function(e){var t=e.disabled,n=e.onClick,i=e.children,r=e.progress;return(0,X.jsxs)(D,{disabled:t||!1,onClick:n||function(){},progress:r||null,"data-testid":"stCameraInputButton",children:[i,r&&(0,X.jsx)(z,{children:(0,X.jsx)(F.Z,{value:r,size:F.$.EXTRASMALL,overrides:{Bar:{style:{borderTopLeftRadius:"0px",borderTopRightRadius:"0px"}},BarProgress:{style:{borderTopLeftRadius:"0px",borderTopRightRadius:"0px"}},BarContainer:{style:{borderTopLeftRadius:"0px",borderTopRightRadius:"0px"}}}})})]})},A=n(77979),B=n(9003),V=n(81354),W=n(11765);!function(e){e.USER="user",e.ENVIRONMENT="environment"}(_||(_={}));var G,N=function(e){var t=e.switchFacingMode;return(0,X.jsx)(Z,{children:(0,X.jsx)(S.Z,{content:"Switch camera",placement:S.u.TOP_RIGHT,children:(0,X.jsx)(B.ZP,{kind:V.nW.MINIMAL,onClick:t,children:(0,X.jsx)(m.Z,{content:A.Z,size:"twoXL",color:W.Z.white})})})})},H=n(11026),$=n(3084),q=n(25621),K=n(91706),J=n(48051),Q=n.n(J),Y=n(84192);!function(e){e.PENDING="pending",e.SUCCESS="success",e.ERROR="error"}(G||(G={}));var ee=function(e){var t=e.width;return(0,X.jsxs)(T,{width:t,children:[(0,X.jsx)(m.Z,{size:"threeXL",color:W.Z.gray60,content:$.n}),(0,X.jsxs)(j,{children:["This app would like to use your camera.",(0,X.jsx)(U,{href:Y.U3,rel:"noopener noreferrer",target:"_blank",children:"Learn how to allow access."})]})]})},te=function(e){var t=e.handleCapture,n=e.width,i=e.disabled,r=e.clearPhotoInProgress,o=e.setClearPhotoInProgress,s=e.facingMode,a=e.setFacingMode,l=(0,p.useState)(G.PENDING),c=(0,H.Z)(l,2),u=c[0],d=c[1],g=(0,p.useRef)(null),f=(0,p.useState)(n),h=(0,H.Z)(f,2),m=h[0],S=h[1],v=(0,p.useCallback)((0,b.Ds)(1e3,S),[]);(0,p.useEffect)((function(){v(n)}),[n,v]);var R=(0,q.u)();return(0,X.jsxs)(k,{width:m,children:[u===G.SUCCESS||i||r?K.tq&&(0,X.jsx)(N,{switchFacingMode:a}):(0,X.jsx)(ee,{width:m}),(0,X.jsx)(T,{hidden:u!==G.SUCCESS&&!i&&!r,width:m,children:!i&&(0,X.jsx)(Q(),{audio:!1,ref:g,screenshotFormat:"image/jpeg",screenshotQuality:1,width:m,height:9*m/16,style:{borderRadius:"".concat(R.radii.lg," ").concat(R.radii.lg," 0 0")},onUserMediaError:function(){d(G.ERROR)},onUserMedia:function(){d(G.SUCCESS),o(!1)},videoConstraints:{width:{ideal:m},facingMode:s}})}),(0,X.jsx)(O,{onClick:function(){if(null!==g.current){var e=g.current.getScreenshot();t(e)}},disabled:u!==G.SUCCESS||i||r,children:"Take Photo"})]})};var ne=function(e){(0,a.Z)(n,e);var t=(0,l.Z)(n);function n(e){var i;return(0,o.Z)(this,n),(i=t.call(this,e)).localFileIdCounter=1,i.RESTORED_FROM_WIDGET_STRING="RESTORED_FROM_WIDGET",i.formClearHelper=new w.K,i.getProgress=function(){if(i.state.files.length>0&&"uploading"===i.state.files[i.state.files.length-1].status.type)return i.state.files[i.state.files.length-1].status.progress},i.setClearPhotoInProgress=function(e){i.setState({clearPhotoInProgress:e})},i.setFacingMode=function(){i.setState((function(e){return{facingMode:e.facingMode===_.USER?_.ENVIRONMENT:_.USER}}))},i.handleCapture=function(e){if(null===e)return Promise.resolve();i.setState({imgSrc:e,shutter:!0,minShutterEffectPassed:!1});var t,n;return(t=e,n="camera-input-".concat((new Date).toISOString().replace(/:/g,"_"),".jpg"),fetch(t).then((function(e){return e.arrayBuffer()})).then((function(e){return new File([e],n,{type:"image/jpeg"})}))).then((function(e){return i.props.uploadClient.fetchFileURLs([e]).then((function(t){return{file:e,fileUrls:t[0]}}))})).then((function(e){var t=e.file,n=e.fileUrls;return i.uploadFile(n,t)})).then((function(){return e=150,new Promise((function(t){return setTimeout(t,e)}));var e})).then((function(){i.setState((function(t,n){return{imgSrc:e,shutter:t.shutter,minShutterEffectPassed:!0}}))})).catch((function(e){(0,x.H)(e)}))},i.removeCapture=function(){0!==i.state.files.length&&(i.state.files.forEach((function(e){return i.deleteFile(e.id)})),i.setState({imgSrc:null,clearPhotoInProgress:!0}))},i.componentDidUpdate=function(){if("ready"===i.status){var e=i.createWidgetValue(),t=i.props,n=t.element,r=t.widgetMgr,o=r.getFileUploaderStateValue(n);f().isEqual(e,o)||r.setFileUploaderStateValue(n,e,{fromUi:!0})}},i.onFormCleared=function(){i.setState({files:[]},(function(){var e=i.createWidgetValue();null!=e&&(i.setState({imgSrc:null}),i.props.widgetMgr.setFileUploaderStateValue(i.props.element,e,{fromUi:!0}))}))},i.deleteFile=function(e){var t=i.getFile(e);null!=t&&("uploading"===t.status.type&&t.status.cancelToken.cancel(),"uploaded"===t.status.type&&t.status.fileUrls.deleteUrl&&i.props.uploadClient.deleteFile(t.status.fileUrls.deleteUrl),i.removeFile(e))},i.addFile=function(e){i.setState((function(t){return{files:[].concat((0,r.Z)(t.files),[e])}}))},i.removeFile=function(e){i.setState((function(t){return{files:t.files.filter((function(t){return t.id!==e}))}}))},i.getFile=function(e){return i.state.files.find((function(t){return t.id===e}))},i.updateFile=function(e,t){i.setState((function(n){return{files:n.files.map((function(n){return n.id===e?t:n}))}}))},i.onUploadComplete=function(e,t){i.setState((function(){return{shutter:!1}}));var n=i.getFile(e);null!=n&&"uploading"===n.status.type&&i.updateFile(n.id,n.setStatus({type:"uploaded",fileId:t.fileId,fileUrls:t}))},i.onUploadProgress=function(e,t){var n=i.getFile(t);if(null!=n&&"uploading"===n.status.type){var r=Math.round(100*e.loaded/e.total);n.status.progress!==r&&i.updateFile(t,n.setStatus({type:"uploading",cancelToken:n.status.cancelToken,progress:r}))}},i.reset=function(){i.setState({files:[],imgSrc:null})},i.uploadFile=function(e,t){var n=d().CancelToken.source(),r=new y.R(t.name,t.size,i.nextLocalFileId(),{type:"uploading",cancelToken:n,progress:1});i.addFile(r),i.props.uploadClient.uploadFile(i.props.element,e.uploadUrl,t,(function(e){return i.onUploadProgress(e,r.id)}),n.token).then((function(){return i.onUploadComplete(r.id,e)})).catch((function(e){d().isCancel(e)||i.updateFile(r.id,r.setStatus({type:"error",errorMessage:e?e.toString():"Unknown error"}))}))},i.state=i.initialValue,i}return(0,s.Z)(n,[{key:"initialValue",get:function(){var e=this,t={files:[],imgSrc:null,shutter:!1,minShutterEffectPassed:!0,clearPhotoInProgress:!1,facingMode:_.USER},n=this.props,i=n.widgetMgr,r=n.element,o=i.getFileUploaderStateValue(r);if(null==o)return t;var s=o.uploadedFileInfo;return null==s||0===s.length?t:{files:s.map((function(t){var n=t.name,i=t.size,r=t.fileId,o=t.fileUrls;return new y.R(n,i,e.nextLocalFileId(),{type:"uploaded",fileId:r,fileUrls:o})})),imgSrc:0===s.length?"":this.RESTORED_FROM_WIDGET_STRING,shutter:!1,minShutterEffectPassed:!1,clearPhotoInProgress:!1,facingMode:_.USER}}},{key:"componentWillUnmount",value:function(){this.formClearHelper.disconnect()}},{key:"status",get:function(){return this.state.files.some((function(e){return"uploading"===e.status.type}))?"updating":"ready"}},{key:"componentDidMount",value:function(){var e=this.createWidgetValue(),t=this.props,n=t.element,i=t.widgetMgr;void 0===i.getFileUploaderStateValue(n)&&i.setFileUploaderStateValue(n,e,{fromUi:!1})}},{key:"createWidgetValue",value:function(){var e=this.state.files.filter((function(e){return"uploaded"===e.status.type})).map((function(e){var t=e.name,n=e.size,i=e.status;return new h.jM({fileId:i.fileId,fileUrls:i.fileUrls,name:t,size:n})}));return new h.xO({uploadedFileInfo:e})}},{key:"render",value:function(){var e,t=this.props,n=t.element,i=t.widgetMgr,r=t.disabled,o=t.width;return this.formClearHelper.manageFormClearListener(i,n.formId,this.onFormCleared),(0,X.jsxs)(k,{width:o,className:"row-widget","data-testid":"stCameraInput",children:[(0,X.jsx)(R.O,{label:n.label,disabled:r,labelVisibility:(0,b.iF)(null===(e=n.labelVisibility)||void 0===e?void 0:e.value),children:n.help&&(0,X.jsx)(C.dT,{children:(0,X.jsx)(v.Z,{content:n.help,placement:S.u.TOP_RIGHT})})}),this.state.imgSrc?(0,X.jsxs)(X.Fragment,{children:[(0,X.jsx)(T,{width:o,children:this.state.imgSrc!==this.RESTORED_FROM_WIDGET_STRING&&(0,X.jsx)(L,{src:this.state.imgSrc,alt:"Snapshot",opacity:this.state.shutter||!this.state.minShutterEffectPassed?"50%":"100%",width:o,height:9*o/16})}),(0,X.jsx)(O,{onClick:this.removeCapture,progress:this.getProgress(),disabled:!!this.getProgress()||r,children:this.getProgress()?"Uploading...":(0,X.jsxs)(P,{children:[(0,X.jsx)(m.Z,{content:c.X,margin:"0 xs 0 0",size:"sm"})," Clear photo"]})})]}):(0,X.jsx)(te,{handleCapture:this.handleCapture,width:o,disabled:r,clearPhotoInProgress:this.state.clearPhotoInProgress,setClearPhotoInProgress:this.setClearPhotoInProgress,facingMode:this.state.facingMode,setFacingMode:this.setFacingMode})]})}},{key:"nextLocalFileId",value:function(){return this.localFileIdCounter++}}]),n}(p.PureComponent)},77367:function(e,t,n){n.d(t,{R:function(){return o}});var i=n(22951),r=n(91976),o=function(){function e(t,n,r,o){(0,i.Z)(this,e),this.name=void 0,this.size=void 0,this.status=void 0,this.id=void 0,this.name=t,this.size=n,this.id=r,this.status=o}return(0,r.Z)(e,[{key:"setStatus",value:function(t){return new e(this.name,this.size,this.id,t)}}]),e}()},87814:function(e,t,n){n.d(t,{K:function(){return s}});var i=n(22951),r=n(91976),o=n(50641),s=function(){function e(){(0,i.Z)(this,e),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}return(0,r.Z)(e,[{key:"manageFormClearListener",value:function(e,t,n){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,o.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,n),this.lastWidgetMgr=e,this.lastFormId=t))}},{key:"disconnect",value:function(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}]),e}()}}]);