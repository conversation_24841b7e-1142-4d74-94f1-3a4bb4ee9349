/*! For license information please see 7673.e016f6f7.chunk.js.LICENSE.txt */
(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[7673],{97943:function(e,t,n){"use strict";n.d(t,{m:function(){return a}});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"}))}));a.displayName="Add"},57463:function(e,t,n){"use strict";n.d(t,{H:function(){return a}});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M16 9v10H8V9h8m-1.5-6h-5l-1 1H5v2h14V4h-3.5l-1-1zM18 7H6v12c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7z"}))}));a.displayName="Delete"},41342:function(e,t,n){"use strict";n.d(t,{k:function(){return a}});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("rect",{width:24,height:24,fill:"none"}),i.createElement("path",{d:"M18 15v3H6v-3H4v3c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-3h-2zm-1-4l-1.41-1.41L13 12.17V4h-2v8.17L8.41 9.59 7 11l5 5 5-5z"}))}));a.displayName="FileDownload"},34367:function(e,t,n){"use strict";n.d(t,{i:function(){return a}});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"}))}));a.displayName="Fullscreen"},31011:function(e,t,n){"use strict";n.d(t,{m:function(){return a}});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"}))}));a.displayName="FullscreenExit"},17875:function(e,t,n){"use strict";n.d(t,{o:function(){return a}});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}))}));a.displayName="Search"},29916:function(e){e.exports=function(e,t,n,r){for(var i=-1,o=null==e?0:e.length;++i<o;){var a=e[i];t(r,a,n(a),e)}return r}},10636:function(e,t,n){var r=n(74791);e.exports=function(e,t){return!!(null==e?0:e.length)&&r(e,t,0)>-1}},19774:function(e){e.exports=function(e,t,n){for(var r=-1,i=null==e?0:e.length;++r<i;)if(n(t,e[r]))return!0;return!1}},70199:function(e,t,n){var r=n(64811);e.exports=function(e,t,n,i){return r(e,(function(e,r,o){t(i,e,n(e),o)})),i}},18471:function(e){e.exports=function(e,t,n){return e===e&&(void 0!==n&&(e=e<=n?e:n),void 0!==t&&(e=e>=t?e:t)),e}},18218:function(e){e.exports=function(e,t,n,r){for(var i=e.length,o=n+(r?1:-1);r?o--:++o<i;)if(t(e[o],o,e))return o;return-1}},29080:function(e,t,n){var r=n(73211),i=n(48258);e.exports=function e(t,n,o,a,l){var u=-1,s=t.length;for(o||(o=i),l||(l=[]);++u<s;){var c=t[u];n>0&&o(c)?n>1?e(c,n-1,o,a,l):r(l,c):a||(l[l.length]=c)}return l}},48003:function(e){var t=Object.prototype.hasOwnProperty;e.exports=function(e,n){return null!=e&&t.call(e,n)}},74791:function(e,t,n){var r=n(18218),i=n(18238),o=n(68603);e.exports=function(e,t,n){return t===t?o(e,t,n):r(e,i,n)}},18238:function(e){e.exports=function(e){return e!==e}},61054:function(e){var t=Math.ceil,n=Math.max;e.exports=function(e,r,i,o){for(var a=-1,l=n(t((r-e)/(i||1)),0),u=Array(l);l--;)u[o?l:++a]=e,e+=i;return u}},13811:function(e,t,n){var r=n(51030),i=/^\s+/;e.exports=function(e){return e?e.slice(0,r(e)+1).replace(i,""):e}},34768:function(e,t,n){var r=n(89632),i=n(10636),o=n(19774),a=n(7086),l=n(58938),u=n(21278);e.exports=function(e,t,n){var s=-1,c=i,d=e.length,f=!0,h=[],p=h;if(n)f=!1,c=o;else if(d>=200){var v=t?null:l(e);if(v)return u(v);f=!1,c=a,p=new r}else p=t?[]:h;e:for(;++s<d;){var g=e[s],m=t?t(g):g;if(g=n||0!==g?g:0,f&&m===m){for(var y=p.length;y--;)if(p[y]===m)continue e;t&&p.push(m),h.push(g)}else c(p,m,n)||(p!==h&&p.push(m),h.push(g))}return h}},98718:function(e,t,n){var r=n(29916),i=n(70199),o=n(48603),a=n(12697);e.exports=function(e,t){return function(n,l){var u=a(n)?r:i,s=t?t():{};return u(n,e,o(l,2),s)}}},10583:function(e,t,n){var r=n(61054),i=n(18621),o=n(78054);e.exports=function(e){return function(t,n,a){return a&&"number"!=typeof a&&i(t,n,a)&&(n=a=void 0),t=o(t),void 0===n?(n=t,t=0):n=o(n),a=void 0===a?t<n?1:-1:o(a),r(t,n,a,e)}}},58938:function(e,t,n){var r=n(64626),i=n(55304),o=n(21278),a=r&&1/o(new r([,-0]))[1]==1/0?function(e){return new r(e)}:i;e.exports=a},48258:function(e,t,n){var r=n(27530),i=n(22846),o=n(12697),a=r?r.isConcatSpreadable:void 0;e.exports=function(e){return o(e)||i(e)||!!(a&&e&&e[a])}},68603:function(e){e.exports=function(e,t,n){for(var r=n-1,i=e.length;++r<i;)if(e[r]===t)return r;return-1}},51030:function(e){var t=/\s/;e.exports=function(e){for(var n=e.length;n--&&t.test(e.charAt(n)););return n}},51586:function(e,t,n){var r=n(18471),i=n(90773);e.exports=function(e,t,n){return void 0===n&&(n=t,t=void 0),void 0!==n&&(n=(n=i(n))===n?n:0),void 0!==t&&(t=(t=i(t))===t?t:0),r(i(e),t,n)}},56797:function(e,t,n){var r=n(60506),i=n(3338),o=n(90773),a=Math.max,l=Math.min;e.exports=function(e,t,n){var u,s,c,d,f,h,p=0,v=!1,g=!1,m=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function y(t){var n=u,r=s;return u=s=void 0,p=t,d=e.apply(r,n)}function b(e){var n=e-h;return void 0===h||n>=t||n<0||g&&e-p>=c}function w(){var e=i();if(b(e))return x(e);f=setTimeout(w,function(e){var n=t-(e-h);return g?l(n,c-(e-p)):n}(e))}function x(e){return f=void 0,m&&u?y(e):(u=s=void 0,d)}function k(){var e=i(),n=b(e);if(u=arguments,s=this,h=e,n){if(void 0===f)return function(e){return p=e,f=setTimeout(w,t),v?y(e):d}(h);if(g)return clearTimeout(f),f=setTimeout(w,t),y(h)}return void 0===f&&(f=setTimeout(w,t)),d}return t=o(t)||0,r(n)&&(v=!!n.leading,c=(g="maxWait"in n)?a(o(n.maxWait)||0,t):c,m="trailing"in n?!!n.trailing:m),k.cancel=function(){void 0!==f&&clearTimeout(f),p=0,u=h=s=f=void 0},k.flush=function(){return void 0===f?d:x(i())},k}},86995:function(e,t,n){var r=n(29080);e.exports=function(e){return(null==e?0:e.length)?r(e,1):[]}},76236:function(e,t,n){var r=n(45742),i=n(98718),o=Object.prototype.hasOwnProperty,a=i((function(e,t,n){o.call(e,n)?e[n].push(t):r(e,n,[t])}));e.exports=a},82781:function(e,t,n){var r=n(48003),i=n(98869);e.exports=function(e,t){return null!=e&&i(e,t,r)}},55304:function(e){e.exports=function(){}},3338:function(e,t,n){var r=n(19661);e.exports=function(){return r.Date.now()}},7974:function(e,t,n){var r=n(10583)();e.exports=r},19266:function(e,t,n){var r=n(56797),i=n(60506);e.exports=function(e,t,n){var o=!0,a=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return i(n)&&(o="leading"in n?!!n.leading:o,a="trailing"in n?!!n.trailing:a),r(e,t,{leading:o,maxWait:t,trailing:a})}},78054:function(e,t,n){var r=n(90773),i=1/0;e.exports=function(e){return e?(e=r(e))===i||e===-1/0?17976931348623157e292*(e<0?-1:1):e===e?e:0:0===e?e:0}},90773:function(e,t,n){var r=n(13811),i=n(60506),o=n(3490),a=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,u=/^0o[0-7]+$/i,s=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(o(e))return NaN;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=r(e);var n=l.test(e);return n||u.test(e)?s(e.slice(2),n?2:8):a.test(e)?NaN:+e}},17015:function(e,t,n){var r=n(34768);e.exports=function(e){return e&&e.length?r(e):[]}},87717:function(e,t,n){var r,i,o,a,l;a=this,l=function(e){var t=!1,n=!1,r=!1,i=!1,o="escape years months weeks days hours minutes seconds milliseconds general".split(" "),a=[{type:"seconds",targets:[{type:"minutes",value:60},{type:"hours",value:3600},{type:"days",value:86400},{type:"weeks",value:604800},{type:"months",value:2678400},{type:"years",value:31536e3}]},{type:"minutes",targets:[{type:"hours",value:60},{type:"days",value:1440},{type:"weeks",value:10080},{type:"months",value:44640},{type:"years",value:525600}]},{type:"hours",targets:[{type:"days",value:24},{type:"weeks",value:168},{type:"months",value:744},{type:"years",value:8760}]},{type:"days",targets:[{type:"weeks",value:7},{type:"months",value:31},{type:"years",value:365}]},{type:"months",targets:[{type:"years",value:12}]}];function l(e,t){return!(t.length>e.length)&&-1!==e.indexOf(t)}function u(e){for(var t="";e;)t+="0",e-=1;return t}function s(e,t){var n=e+"+"+m(C(t).sort(),(function(e){return e+":"+t[e]})).join(",");return s.cache[n]||(s.cache[n]=Intl.NumberFormat(e,t)),s.cache[n]}function c(e,t,o){var a,l,d,f=t.useToLocaleString,h=t.useGrouping,p=h&&t.grouping.slice(),v=t.maximumSignificantDigits,g=t.minimumIntegerDigits||1,m=t.fractionDigits||0,y=t.groupingSeparator,b=t.decimalSeparator;if(f&&o){var w,x={minimumIntegerDigits:g,useGrouping:h};return m&&(x.maximumFractionDigits=m,x.minimumFractionDigits=m),v&&e>0&&(x.maximumSignificantDigits=v),r?(i||((w=D({},t)).useGrouping=!1,w.decimalSeparator=".",e=parseFloat(c(e,w),10)),s(o,x).format(e)):(n||((w=D({},t)).useGrouping=!1,w.decimalSeparator=".",e=parseFloat(c(e,w),10)),e.toLocaleString(o,x))}var k=(v?e.toPrecision(v+1):e.toFixed(m+1)).split("e");d=k[1]||"",l=(k=k[0].split("."))[1]||"";var C=(a=k[0]||"").length,S=l.length,E=C+S,F=a+l;(v&&E===v+1||!v&&S===m+1)&&((F=function(e){for(var t=e.split("").reverse(),n=0,r=!0;r&&n<t.length;)n?"9"===t[n]?t[n]="0":(t[n]=(parseInt(t[n],10)+1).toString(),r=!1):(parseInt(t[n],10)<5&&(r=!1),t[n]="0"),n+=1;return r&&t.push("1"),t.reverse().join("")}(F)).length===E+1&&(C+=1),S&&(F=F.slice(0,-1)),a=F.slice(0,C),l=F.slice(C)),v&&(l=l.replace(/0*$/,""));var M=parseInt(d,10);M>0?l.length<=M?(a+=l+=u(M-l.length),l=""):(a+=l.slice(0,M),l=l.slice(M)):M<0&&(l=u(Math.abs(M)-a.length)+a+l,a="0"),v||((l=l.slice(0,m)).length<m&&(l+=u(m-l.length)),a.length<g&&(a=u(g-a.length)+a));var Z,A="";if(h)for(k=a;k.length;)p.length&&(Z=p.shift()),A&&(A=y+A),A=k.slice(-Z)+A,k=k.slice(0,-Z);else A=a;return l&&(A=A+b+l),A}function d(e,t){return e.label.length>t.label.length?-1:e.label.length<t.label.length?1:0}s.cache={};var f={durationLabelsStandard:{S:"millisecond",SS:"milliseconds",s:"second",ss:"seconds",m:"minute",mm:"minutes",h:"hour",hh:"hours",d:"day",dd:"days",w:"week",ww:"weeks",M:"month",MM:"months",y:"year",yy:"years"},durationLabelsShort:{S:"msec",SS:"msecs",s:"sec",ss:"secs",m:"min",mm:"mins",h:"hr",hh:"hrs",d:"dy",dd:"dys",w:"wk",ww:"wks",M:"mo",MM:"mos",y:"yr",yy:"yrs"},durationTimeTemplates:{HMS:"h:mm:ss",HM:"h:mm",MS:"m:ss"},durationLabelTypes:[{type:"standard",string:"__"},{type:"short",string:"_"}],durationPluralKey:function(e,t,n){return 1===t&&null===n?e:e+e}};function h(e){return"[object Array]"===Object.prototype.toString.call(e)}function p(e){return"[object Object]"===Object.prototype.toString.call(e)}function v(e,t){var n,r=0,i=e&&e.length||0;for("function"!==typeof t&&(n=t,t=function(e){return e===n});r<i;){if(t(e[r]))return e[r];r+=1}}function g(e,t){var n=0,r=e.length;if(e&&r)for(;n<r;){if(!1===t(e[n],n))return;n+=1}}function m(e,t){var n=0,r=e.length,i=[];if(!e||!r)return i;for(;n<r;)i[n]=t(e[n],n),n+=1;return i}function y(e,t){return m(e,(function(e){return e[t]}))}function b(e){var t=[];return g(e,(function(e){e&&t.push(e)})),t}function w(e){var t=[];return g(e,(function(e){v(t,e)||t.push(e)})),t}function x(e,t){var n=[];return g(e,(function(e){g(t,(function(t){e===t&&n.push(e)}))})),w(n)}function k(e,t){var n=[];return g(e,(function(r,i){if(!t(r))return n=e.slice(i),!1})),n}function D(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}function C(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t}function S(e,t){var n=0,r=e.length;if(!e||!r)return!1;for(;n<r;){if(!0===t(e[n],n))return!0;n+=1}return!1}function E(e){return"3.6"===e(3.55,"en",{useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:1,maximumFractionDigits:1})}function F(e){var t=!0;return!!(t=(t=(t=t&&"1"===e(1,"en",{minimumIntegerDigits:1}))&&"01"===e(1,"en",{minimumIntegerDigits:2}))&&"001"===e(1,"en",{minimumIntegerDigits:3}))&&!!(t=(t=(t=(t=t&&"100"===e(99.99,"en",{maximumFractionDigits:0,minimumFractionDigits:0}))&&"100.0"===e(99.99,"en",{maximumFractionDigits:1,minimumFractionDigits:1}))&&"99.99"===e(99.99,"en",{maximumFractionDigits:2,minimumFractionDigits:2}))&&"99.990"===e(99.99,"en",{maximumFractionDigits:3,minimumFractionDigits:3}))&&!!(t=(t=(t=(t=(t=t&&"100"===e(99.99,"en",{maximumSignificantDigits:1}))&&"100"===e(99.99,"en",{maximumSignificantDigits:2}))&&"100"===e(99.99,"en",{maximumSignificantDigits:3}))&&"99.99"===e(99.99,"en",{maximumSignificantDigits:4}))&&"99.99"===e(99.99,"en",{maximumSignificantDigits:5}))&&!!(t=(t=t&&"1,000"===e(1e3,"en",{useGrouping:!0}))&&"1000"===e(1e3,"en",{useGrouping:!1}))}function M(){var e,t=[].slice.call(arguments),n={};if(g(t,(function(t,r){if(!r){if(!h(t))throw"Expected array as the first argument to durationsFormat.";e=t}"string"!==typeof t&&"function"!==typeof t?"number"!==typeof t?p(t)&&D(n,t):n.precision=t:n.template=t})),!e||!e.length)return[];n.returnMomentTypes=!0;var r=m(e,(function(e){return e.format(n)})),i=x(o,w(y(function(e){var t=[];return g(e,(function(e){t=t.concat(e)})),t}(r),"type"))),a=n.largest;return a&&(i=i.slice(0,a)),n.returnMomentTypes=!1,n.outputTypes=i,m(e,(function(e){return e.format(n)}))}function Z(){var n=[].slice.call(arguments),i=D({},this.format.defaults),u=this.asMilliseconds(),s=this.asMonths();"function"===typeof this.isValid&&!1===this.isValid()&&(u=0,s=0);var E=u<0,F=e.duration(Math.abs(u),"milliseconds"),M=e.duration(Math.abs(s),"months");g(n,(function(e){"string"!==typeof e&&"function"!==typeof e?"number"!==typeof e?p(e)&&D(i,e):i.precision=e:i.template=e}));var Z={years:"y",months:"M",weeks:"w",days:"d",hours:"h",minutes:"m",seconds:"s",milliseconds:"S"},A={escape:/\[(.+?)\]/,years:/\*?[Yy]+/,months:/\*?M+/,weeks:/\*?[Ww]+/,days:/\*?[Dd]+/,hours:/\*?[Hh]+/,minutes:/\*?m+/,seconds:/\*?s+/,milliseconds:/\*?S+/,general:/.+?/};i.types=o;var R=function(e){return v(o,(function(t){return A[t].test(e)}))},I=new RegExp(m(o,(function(e){return A[e].source})).join("|"),"g");i.duration=this;var T="function"===typeof i.template?i.template.apply(i):i.template,O=i.outputTypes,P=i.returnMomentTypes,H=i.largest,L=[];O||(h(i.stopTrim)&&(i.stopTrim=i.stopTrim.join("")),i.stopTrim&&g(i.stopTrim.match(I),(function(e){var t=R(e);"escape"!==t&&"general"!==t&&L.push(t)})));var z=e.localeData();z||(z={}),g(C(f),(function(e){"function"!==typeof f[e]?z["_"+e]||(z["_"+e]=f[e]):z[e]||(z[e]=f[e])})),g(C(z._durationTimeTemplates),(function(e){T=T.replace("_"+e+"_",z._durationTimeTemplates[e])}));var B=i.userLocale||e.locale(),_=i.useLeftUnits,V=i.usePlural,N=i.precision,W=i.forceLength,j=i.useGrouping,U=i.trunc,X=i.useSignificantDigits&&N>0,Y=X?i.precision:0,K=Y,$=i.minValue,G=!1,q=i.maxValue,Q=!1,J=i.useToLocaleString,ee=i.groupingSeparator,te=i.decimalSeparator,ne=i.grouping;J=J&&(t||r);var re=i.trim;h(re)&&(re=re.join(" ")),null===re&&(H||q||X)&&(re="all"),null!==re&&!0!==re&&"left"!==re&&"right"!==re||(re="large"),!1===re&&(re="");var ie=function(e){return e.test(re)},oe=/both/,ae=/^all|[^sm]all/,le=H>0||S([/large/,oe,ae],ie),ue=S([/small/,oe,ae],ie),se=S([/mid/,ae],ie),ce=S([/final/,ae],ie),de=m(T.match(I),(function(e,t){var n=R(e);return"*"===e.slice(0,1)&&(e=e.slice(1),"escape"!==n&&"general"!==n&&L.push(n)),{index:t,length:e.length,text:"",token:"escape"===n?e.replace(A.escape,"$1"):e,type:"escape"===n||"general"===n?null:n}})),fe={index:0,length:0,token:"",text:"",type:null},he=[];_&&de.reverse(),g(de,(function(e){if(e.type)return(fe.type||fe.text)&&he.push(fe),void(fe=e);_?fe.text=e.token+fe.text:fe.text+=e.token})),(fe.type||fe.text)&&he.push(fe),_&&he.reverse();var pe=x(o,w(b(y(he,"type"))));if(!pe.length)return y(he,"text").join("");pe=m(pe,(function(e,t){var n,r=t+1===pe.length,o=!t;n="years"===e||"months"===e?M.as(e):F.as(e);var a=Math.floor(n),l=n-a,u=v(he,(function(t){return e===t.type}));return o&&q&&n>q&&(Q=!0),r&&$&&Math.abs(i.duration.as(e))<$&&(G=!0),o&&null===W&&u.length>1&&(W=!0),F.subtract(a,e),M.subtract(a,e),{rawValue:n,wholeValue:a,decimalValue:r?l:0,isSmallest:r,isLargest:o,type:e,tokenLength:u.length}}));var ve,ge=U?Math.floor:Math.round,me=function(e,t){var n=Math.pow(10,t);return ge(e*n)/n},ye=!1,be=!1,we=function(e,t){var n={useGrouping:j,groupingSeparator:ee,decimalSeparator:te,grouping:ne,useToLocaleString:J};return X&&(Y<=0?(e.rawValue=0,e.wholeValue=0,e.decimalValue=0):(n.maximumSignificantDigits=Y,e.significantDigits=Y)),Q&&!be&&(e.isLargest?(e.wholeValue=q,e.decimalValue=0):(e.wholeValue=0,e.decimalValue=0)),G&&!be&&(e.isSmallest?(e.wholeValue=$,e.decimalValue=0):(e.wholeValue=0,e.decimalValue=0)),e.isSmallest||e.significantDigits&&e.significantDigits-e.wholeValue.toString().length<=0?N<0?e.value=me(e.wholeValue,N):0===N?e.value=ge(e.wholeValue+e.decimalValue):X?(e.value=U?me(e.rawValue,Y-e.wholeValue.toString().length):e.rawValue,e.wholeValue&&(Y-=e.wholeValue.toString().length)):(n.fractionDigits=N,e.value=U?e.wholeValue+me(e.decimalValue,N):e.wholeValue+e.decimalValue):X&&e.wholeValue?(e.value=Math.round(me(e.wholeValue,e.significantDigits-e.wholeValue.toString().length)),Y-=e.wholeValue.toString().length):e.value=e.wholeValue,e.tokenLength>1&&(W||ye)&&(n.minimumIntegerDigits=e.tokenLength,be&&n.maximumSignificantDigits<e.tokenLength&&delete n.maximumSignificantDigits),!ye&&(e.value>0||""===re||v(L,e.type)||v(O,e.type))&&(ye=!0),e.formattedValue=c(e.value,n,B),n.useGrouping=!1,n.decimalSeparator=".",e.formattedValueEn=c(e.value,n,"en"),2===e.tokenLength&&"milliseconds"===e.type&&(e.formattedValueMS=c(e.value,{minimumIntegerDigits:3,useGrouping:!1},"en").slice(0,2)),e};if((pe=b(pe=m(pe,we))).length>1){var xe=function(e){return v(pe,(function(t){return t.type===e}))};g(a,(function(e){var t=xe(e.type);t&&g(e.targets,(function(e){var n=xe(e.type);n&&parseInt(t.formattedValueEn,10)===e.value&&(t.rawValue=0,t.wholeValue=0,t.decimalValue=0,n.rawValue+=1,n.wholeValue+=1,n.decimalValue=0,n.formattedValueEn=n.wholeValue.toString(),be=!0)}))}))}return be&&(ye=!1,Y=K,pe=b(pe=m(pe,we))),!O||Q&&!i.trim?(le&&(pe=k(pe,(function(e){return!e.isSmallest&&!e.wholeValue&&!v(L,e.type)}))),H&&pe.length&&(pe=pe.slice(0,H)),ue&&pe.length>1&&(ve=function(e){return!e.wholeValue&&!v(L,e.type)&&!e.isLargest},pe=k(pe.slice().reverse(),ve).reverse()),se&&(pe=b(pe=m(pe,(function(e,t){return t>0&&t<pe.length-1&&!e.wholeValue?null:e})))),!ce||1!==pe.length||pe[0].wholeValue||!U&&pe[0].isSmallest&&pe[0].rawValue<$||(pe=[])):pe=b(pe=m(pe,(function(e){return v(O,(function(t){return e.type===t}))?e:null}))),P?pe:(g(he,(function(e){var t=Z[e.type],n=v(pe,(function(t){return t.type===e.type}));if(t&&n){var r=n.formattedValueEn.split(".");r[0]=parseInt(r[0],10),r[1]?r[1]=parseFloat("0."+r[1],10):r[1]=null;var i=z.durationPluralKey(t,r[0],r[1]),o=function(e,t){var n=[];return g(C(t),(function(r){if("_durationLabels"===r.slice(0,15)){var i=r.slice(15).toLowerCase();g(C(t[r]),(function(o){o.slice(0,1)===e&&n.push({type:i,key:o,label:t[r][o]})}))}})),n}(t,z),a=!1,u={};g(z._durationLabelTypes,(function(t){var n=v(o,(function(e){return e.type===t.type&&e.key===i}));n&&(u[n.type]=n.label,l(e.text,t.string)&&(e.text=e.text.replace(t.string,n.label),a=!0))})),V&&!a&&(o.sort(d),g(o,(function(t){return u[t.type]===t.label?!l(e.text,t.label)&&void 0:l(e.text,t.label)?(e.text=e.text.replace(t.label,u[t.type]),!1):void 0})))}})),(he=m(he,(function(e){if(!e.type)return e.text;var t=v(pe,(function(t){return t.type===e.type}));if(!t)return"";var n="";return _&&(n+=e.text),(E&&Q||!E&&G)&&(n+="< ",Q=!1,G=!1),(E&&G||!E&&Q)&&(n+="> ",Q=!1,G=!1),E&&(t.value>0||""===re||v(L,t.type)||v(O,t.type))&&(n+="-",E=!1),"milliseconds"===e.type&&t.formattedValueMS?n+=t.formattedValueMS:n+=t.formattedValue,_||(n+=e.text),n}))).join("").replace(/(,| |:|\.)*$/,"").replace(/^(,| |:|\.)*/,""))}function A(){var e=this.duration,t=function(t){return e._data[t]},n=v(this.types,t),r=function(e,t){for(var n=e.length;n-=1;)if(t(e[n]))return e[n]}(this.types,t);switch(n){case"milliseconds":return"S __";case"seconds":case"minutes":return"*_MS_";case"hours":return"_HMS_";case"days":if(n===r)return"d __";case"weeks":return n===r?"w __":(null===this.trim&&(this.trim="both"),"w __, d __, h __");case"months":if(n===r)return"M __";case"years":return n===r?"y __":(null===this.trim&&(this.trim="both"),"y __, M __, d __");default:return null===this.trim&&(this.trim="both"),"y __, d __, h __, m __, s __"}}function R(e){if(!e)throw"Moment Duration Format init cannot find moment instance.";e.duration.format=M,e.duration.fn.format=Z,e.duration.fn.format.defaults={trim:null,stopTrim:null,largest:null,maxValue:null,minValue:null,precision:0,trunc:!1,forceLength:null,userLocale:null,usePlural:!0,useLeftUnits:!1,useGrouping:!0,useSignificantDigits:!1,template:A,useToLocaleString:!0,groupingSeparator:",",decimalSeparator:".",grouping:[3]},e.updateLocale("en",f)}var I=function(e,t,n){return e.toLocaleString(t,n)};t=function(){try{(0).toLocaleString("i")}catch(e){return"RangeError"===e.name}return!1}()&&F(I),n=t&&E(I);var T=function(e,t,n){if("undefined"!==typeof window&&window&&window.Intl&&window.Intl.NumberFormat)return window.Intl.NumberFormat(t,n).format(e)};return r=F(T),i=r&&E(T),R(e),R},i=[n(53608)],void 0===(o="function"===typeof(r=l)?r.apply(t,i):r)||(e.exports=o),a&&(a.momentDurationFormatSetup=a.moment?l(a.moment):l)},15650:function(e,t,n){var r,i,o;i=[t,n(73074)],r=function(e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(t);function r(e){return e&&e.__esModule?e:{default:e}}e.default=n.default},void 0===(o="function"===typeof r?r.apply(t,i):r)||(e.exports=o)},73074:function(e,t,n){var r,i,o;i=[t,n(66845),n(8984)],r=function(e,t,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHasSupportToCaptureOption=h;var r=o(t),i=o(n);function o(e){return e&&e.__esModule?e:{default:e}}var a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};function l(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}function d(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var f=!1;function h(e){f=e}try{addEventListener("test",null,Object.defineProperty({},"capture",{get:function(){h(!0)}}))}catch(m){}function p(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{capture:!0};return f?e:e.capture}function v(e){if("touches"in e){var t=e.touches[0];return{x:t.pageX,y:t.pageY}}return{x:e.screenX,y:e.screenY}}var g=function(e){function t(){var e;u(this,t);for(var n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];var o=c(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(r)));return o._handleSwipeStart=o._handleSwipeStart.bind(o),o._handleSwipeMove=o._handleSwipeMove.bind(o),o._handleSwipeEnd=o._handleSwipeEnd.bind(o),o._onMouseDown=o._onMouseDown.bind(o),o._onMouseMove=o._onMouseMove.bind(o),o._onMouseUp=o._onMouseUp.bind(o),o._setSwiperRef=o._setSwiperRef.bind(o),o}return d(t,e),s(t,[{key:"componentDidMount",value:function(){this.swiper&&this.swiper.addEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"componentWillUnmount",value:function(){this.swiper&&this.swiper.removeEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"_onMouseDown",value:function(e){this.props.allowMouseEvents&&(this.mouseDown=!0,document.addEventListener("mouseup",this._onMouseUp),document.addEventListener("mousemove",this._onMouseMove),this._handleSwipeStart(e))}},{key:"_onMouseMove",value:function(e){this.mouseDown&&this._handleSwipeMove(e)}},{key:"_onMouseUp",value:function(e){this.mouseDown=!1,document.removeEventListener("mouseup",this._onMouseUp),document.removeEventListener("mousemove",this._onMouseMove),this._handleSwipeEnd(e)}},{key:"_handleSwipeStart",value:function(e){var t=v(e),n=t.x,r=t.y;this.moveStart={x:n,y:r},this.props.onSwipeStart(e)}},{key:"_handleSwipeMove",value:function(e){if(this.moveStart){var t=v(e),n=t.x,r=t.y,i=n-this.moveStart.x,o=r-this.moveStart.y;this.moving=!0,this.props.onSwipeMove({x:i,y:o},e)&&e.cancelable&&e.preventDefault(),this.movePosition={deltaX:i,deltaY:o}}}},{key:"_handleSwipeEnd",value:function(e){this.props.onSwipeEnd(e);var t=this.props.tolerance;this.moving&&this.movePosition&&(this.movePosition.deltaX<-t?this.props.onSwipeLeft(1,e):this.movePosition.deltaX>t&&this.props.onSwipeRight(1,e),this.movePosition.deltaY<-t?this.props.onSwipeUp(1,e):this.movePosition.deltaY>t&&this.props.onSwipeDown(1,e)),this.moveStart=null,this.moving=!1,this.movePosition=null}},{key:"_setSwiperRef",value:function(e){this.swiper=e,this.props.innerRef(e)}},{key:"render",value:function(){var e=this.props,t=(e.tagName,e.className),n=e.style,i=e.children,o=(e.allowMouseEvents,e.onSwipeUp,e.onSwipeDown,e.onSwipeLeft,e.onSwipeRight,e.onSwipeStart,e.onSwipeMove,e.onSwipeEnd,e.innerRef,e.tolerance,l(e,["tagName","className","style","children","allowMouseEvents","onSwipeUp","onSwipeDown","onSwipeLeft","onSwipeRight","onSwipeStart","onSwipeMove","onSwipeEnd","innerRef","tolerance"]));return r.default.createElement(this.props.tagName,a({ref:this._setSwiperRef,onMouseDown:this._onMouseDown,onTouchStart:this._handleSwipeStart,onTouchEnd:this._handleSwipeEnd,className:t,style:n},o),i)}}]),t}(t.Component);g.displayName="ReactSwipe",g.propTypes={tagName:i.default.string,className:i.default.string,style:i.default.object,children:i.default.node,allowMouseEvents:i.default.bool,onSwipeUp:i.default.func,onSwipeDown:i.default.func,onSwipeLeft:i.default.func,onSwipeRight:i.default.func,onSwipeStart:i.default.func,onSwipeMove:i.default.func,onSwipeEnd:i.default.func,innerRef:i.default.func,tolerance:i.default.number.isRequired},g.defaultProps={tagName:"div",allowMouseEvents:!1,onSwipeUp:function(){},onSwipeDown:function(){},onSwipeLeft:function(){},onSwipeRight:function(){},onSwipeStart:function(){},onSwipeMove:function(){},onSwipeEnd:function(){},innerRef:function(){},tolerance:0},e.default=g},void 0===(o="function"===typeof r?r.apply(t,i):r)||(e.exports=o)},30480:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(e,t,n){var r=0===e?e:e+t;return"translate3d"+("("+("horizontal"===n?[r,0,0]:[0,r,0]).join(",")+")")}},98806:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fadeAnimationHandler=t.slideStopSwipingHandler=t.slideSwipeAnimationHandler=t.slideAnimationHandler=void 0;var r,i=n(66845),o=(r=n(30480))&&r.__esModule?r:{default:r},a=n(34705);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}t.slideAnimationHandler=function(e,t){var n={},r=t.selectedItem,l=r,s=i.Children.count(e.children)-1;if(e.infiniteLoop&&(r<0||r>s))return l<0?e.centerMode&&e.centerSlidePercentage&&"horizontal"===e.axis?n.itemListStyle=(0,a.setPosition)(-(s+2)*e.centerSlidePercentage-(100-e.centerSlidePercentage)/2,e.axis):n.itemListStyle=(0,a.setPosition)(100*-(s+2),e.axis):l>s&&(n.itemListStyle=(0,a.setPosition)(0,e.axis)),n;var c=(0,a.getPosition)(r,e),d=(0,o.default)(c,"%",e.axis),f=e.transitionTime+"ms";return n.itemListStyle={WebkitTransform:d,msTransform:d,OTransform:d,transform:d},t.swiping||(n.itemListStyle=u(u({},n.itemListStyle),{},{WebkitTransitionDuration:f,MozTransitionDuration:f,OTransitionDuration:f,transitionDuration:f,msTransitionDuration:f})),n};t.slideSwipeAnimationHandler=function(e,t,n,r){var o={},l="horizontal"===t.axis,u=i.Children.count(t.children),s=(0,a.getPosition)(n.selectedItem,t),c=t.infiniteLoop?(0,a.getPosition)(u-1,t)-100:(0,a.getPosition)(u-1,t),d=l?e.x:e.y,f=d;0===s&&d>0&&(f=0),s===c&&d<0&&(f=0);var h=s+100/(n.itemSize/f),p=Math.abs(d)>t.swipeScrollTolerance;return t.infiniteLoop&&p&&(0===n.selectedItem&&h>-100?h-=100*u:n.selectedItem===u-1&&h<100*-u&&(h+=100*u)),(!t.preventMovementUntilSwipeScrollTolerance||p||n.swipeMovementStarted)&&(n.swipeMovementStarted||r({swipeMovementStarted:!0}),o.itemListStyle=(0,a.setPosition)(h,t.axis)),p&&!n.cancelClick&&r({cancelClick:!0}),o};t.slideStopSwipingHandler=function(e,t){var n=(0,a.getPosition)(t.selectedItem,e);return{itemListStyle:(0,a.setPosition)(n,e.axis)}};t.fadeAnimationHandler=function(e,t){var n=e.transitionTime+"ms",r="ease-in-out",i={position:"absolute",display:"block",zIndex:-2,minHeight:"100%",opacity:0,top:0,right:0,left:0,bottom:0,transitionTimingFunction:r,msTransitionTimingFunction:r,MozTransitionTimingFunction:r,WebkitTransitionTimingFunction:r,OTransitionTimingFunction:r};return t.swiping||(i=u(u({},i),{},{WebkitTransitionDuration:n,MozTransitionDuration:n,OTransitionDuration:n,transitionDuration:n,msTransitionDuration:n})),{slideStyle:i,selectedStyle:u(u({},i),{},{opacity:1,position:"relative"}),prevStyle:u({},i)}}},42852:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==h(e)&&"function"!==typeof e)return{default:e};var t=f();if(t&&t.has(e))return t.get(e);var n={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var o=r?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(n,i,o):n[i]=e[i]}n.default=e,t&&t.set(e,n);return n}(n(66845)),i=d(n(15650)),o=d(n(75385)),a=d(n(91935)),l=d(n(39896)),u=d(n(22124)),s=n(34705),c=n(98806);function d(e){return e&&e.__esModule?e:{default:e}}function f(){if("function"!==typeof WeakMap)return null;var e=new WeakMap;return f=function(){return e},e}function h(e){return h="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function p(){return p=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},p.apply(this,arguments)}function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function y(e,t){return y=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},y(e,t)}function b(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=x(e);if(t){var i=x(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return function(e,t){if(t&&("object"===h(t)||"function"===typeof t))return t;return w(e)}(this,n)}}function w(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function x(e){return x=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},x(e)}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var D=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&y(e,t)}(h,e);var t,n,d,f=b(h);function h(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h),k(w(t=f.call(this,e)),"thumbsRef",void 0),k(w(t),"carouselWrapperRef",void 0),k(w(t),"listRef",void 0),k(w(t),"itemsRef",void 0),k(w(t),"timer",void 0),k(w(t),"animationHandler",void 0),k(w(t),"setThumbsRef",(function(e){t.thumbsRef=e})),k(w(t),"setCarouselWrapperRef",(function(e){t.carouselWrapperRef=e})),k(w(t),"setListRef",(function(e){t.listRef=e})),k(w(t),"setItemsRef",(function(e,n){t.itemsRef||(t.itemsRef=[]),t.itemsRef[n]=e})),k(w(t),"autoPlay",(function(){r.Children.count(t.props.children)<=1||(t.clearAutoPlay(),t.props.autoPlay&&(t.timer=setTimeout((function(){t.increment()}),t.props.interval)))})),k(w(t),"clearAutoPlay",(function(){t.timer&&clearTimeout(t.timer)})),k(w(t),"resetAutoPlay",(function(){t.clearAutoPlay(),t.autoPlay()})),k(w(t),"stopOnHover",(function(){t.setState({isMouseEntered:!0},t.clearAutoPlay)})),k(w(t),"startOnLeave",(function(){t.setState({isMouseEntered:!1},t.autoPlay)})),k(w(t),"isFocusWithinTheCarousel",(function(){return!!t.carouselWrapperRef&&!((0,l.default)().activeElement!==t.carouselWrapperRef&&!t.carouselWrapperRef.contains((0,l.default)().activeElement))})),k(w(t),"navigateWithKeyboard",(function(e){if(t.isFocusWithinTheCarousel()){var n="horizontal"===t.props.axis,r=n?37:38;(n?39:40)===e.keyCode?t.increment():r===e.keyCode&&t.decrement()}})),k(w(t),"updateSizes",(function(){if(t.state.initialized&&t.itemsRef&&0!==t.itemsRef.length){var e="horizontal"===t.props.axis,n=t.itemsRef[0];if(n){var r=e?n.clientWidth:n.clientHeight;t.setState({itemSize:r}),t.thumbsRef&&t.thumbsRef.updateSizes()}}})),k(w(t),"setMountState",(function(){t.setState({hasMount:!0}),t.updateSizes()})),k(w(t),"handleClickItem",(function(e,n){0!==r.Children.count(t.props.children)&&(t.state.cancelClick?t.setState({cancelClick:!1}):(t.props.onClickItem(e,n),e!==t.state.selectedItem&&t.setState({selectedItem:e})))})),k(w(t),"handleOnChange",(function(e,n){r.Children.count(t.props.children)<=1||t.props.onChange(e,n)})),k(w(t),"handleClickThumb",(function(e,n){t.props.onClickThumb(e,n),t.moveTo(e)})),k(w(t),"onSwipeStart",(function(e){t.setState({swiping:!0}),t.props.onSwipeStart(e)})),k(w(t),"onSwipeEnd",(function(e){t.setState({swiping:!1,cancelClick:!1,swipeMovementStarted:!1}),t.props.onSwipeEnd(e),t.clearAutoPlay(),t.state.autoPlay&&t.autoPlay()})),k(w(t),"onSwipeMove",(function(e,n){t.props.onSwipeMove(n);var r=t.props.swipeAnimationHandler(e,t.props,t.state,t.setState.bind(w(t)));return t.setState(g({},r)),!!Object.keys(r).length})),k(w(t),"decrement",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;t.moveTo(t.state.selectedItem-("number"===typeof e?e:1))})),k(w(t),"increment",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;t.moveTo(t.state.selectedItem+("number"===typeof e?e:1))})),k(w(t),"moveTo",(function(e){if("number"===typeof e){var n=r.Children.count(t.props.children)-1;e<0&&(e=t.props.infiniteLoop?n:0),e>n&&(e=t.props.infiniteLoop?0:n),t.selectItem({selectedItem:e}),t.state.autoPlay&&!1===t.state.isMouseEntered&&t.resetAutoPlay()}})),k(w(t),"onClickNext",(function(){t.increment(1)})),k(w(t),"onClickPrev",(function(){t.decrement(1)})),k(w(t),"onSwipeForward",(function(){t.increment(1),t.props.emulateTouch&&t.setState({cancelClick:!0})})),k(w(t),"onSwipeBackwards",(function(){t.decrement(1),t.props.emulateTouch&&t.setState({cancelClick:!0})})),k(w(t),"changeItem",(function(e){return function(n){(0,s.isKeyboardEvent)(n)&&"Enter"!==n.key||t.moveTo(e)}})),k(w(t),"selectItem",(function(e){t.setState(g({previousItem:t.state.selectedItem},e),(function(){t.setState(t.animationHandler(t.props,t.state))})),t.handleOnChange(e.selectedItem,r.Children.toArray(t.props.children)[e.selectedItem])})),k(w(t),"getInitialImage",(function(){var e=t.props.selectedItem,n=t.itemsRef&&t.itemsRef[e];return(n&&n.getElementsByTagName("img")||[])[0]})),k(w(t),"getVariableItemHeight",(function(e){var n=t.itemsRef&&t.itemsRef[e];if(t.state.hasMount&&n&&n.children.length){var r=n.children[0].getElementsByTagName("img")||[];if(r.length>0){var i=r[0];if(!i.complete){i.addEventListener("load",(function e(){t.forceUpdate(),i.removeEventListener("load",e)}))}}var o=(r[0]||n.children[0]).clientHeight;return o>0?o:null}return null}));var n={initialized:!1,previousItem:e.selectedItem,selectedItem:e.selectedItem,hasMount:!1,isMouseEntered:!1,autoPlay:e.autoPlay,swiping:!1,swipeMovementStarted:!1,cancelClick:!1,itemSize:1,itemListStyle:{},slideStyle:{},selectedStyle:{},prevStyle:{}};return t.animationHandler="function"===typeof e.animationHandler&&e.animationHandler||"fade"===e.animationHandler&&c.fadeAnimationHandler||c.slideAnimationHandler,t.state=g(g({},n),t.animationHandler(e,n)),t}return t=h,(n=[{key:"componentDidMount",value:function(){this.props.children&&this.setupCarousel()}},{key:"componentDidUpdate",value:function(e,t){e.children||!this.props.children||this.state.initialized||this.setupCarousel(),!e.autoFocus&&this.props.autoFocus&&this.forceFocus(),t.swiping&&!this.state.swiping&&this.setState(g({},this.props.stopSwipingHandler(this.props,this.state))),e.selectedItem===this.props.selectedItem&&e.centerMode===this.props.centerMode||(this.updateSizes(),this.moveTo(this.props.selectedItem)),e.autoPlay!==this.props.autoPlay&&(this.props.autoPlay?this.setupAutoPlay():this.destroyAutoPlay(),this.setState({autoPlay:this.props.autoPlay}))}},{key:"componentWillUnmount",value:function(){this.destroyCarousel()}},{key:"setupCarousel",value:function(){var e=this;this.bindEvents(),this.state.autoPlay&&r.Children.count(this.props.children)>1&&this.setupAutoPlay(),this.props.autoFocus&&this.forceFocus(),this.setState({initialized:!0},(function(){var t=e.getInitialImage();t&&!t.complete?t.addEventListener("load",e.setMountState):e.setMountState()}))}},{key:"destroyCarousel",value:function(){this.state.initialized&&(this.unbindEvents(),this.destroyAutoPlay())}},{key:"setupAutoPlay",value:function(){this.autoPlay();var e=this.carouselWrapperRef;this.props.stopOnHover&&e&&(e.addEventListener("mouseenter",this.stopOnHover),e.addEventListener("mouseleave",this.startOnLeave))}},{key:"destroyAutoPlay",value:function(){this.clearAutoPlay();var e=this.carouselWrapperRef;this.props.stopOnHover&&e&&(e.removeEventListener("mouseenter",this.stopOnHover),e.removeEventListener("mouseleave",this.startOnLeave))}},{key:"bindEvents",value:function(){(0,u.default)().addEventListener("resize",this.updateSizes),(0,u.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.props.useKeyboardArrows&&(0,l.default)().addEventListener("keydown",this.navigateWithKeyboard)}},{key:"unbindEvents",value:function(){(0,u.default)().removeEventListener("resize",this.updateSizes),(0,u.default)().removeEventListener("DOMContentLoaded",this.updateSizes);var e=this.getInitialImage();e&&e.removeEventListener("load",this.setMountState),this.props.useKeyboardArrows&&(0,l.default)().removeEventListener("keydown",this.navigateWithKeyboard)}},{key:"forceFocus",value:function(){var e;null===(e=this.carouselWrapperRef)||void 0===e||e.focus()}},{key:"renderItems",value:function(e){var t=this;return this.props.children?r.Children.map(this.props.children,(function(n,i){var a=i===t.state.selectedItem,l=i===t.state.previousItem,u=a&&t.state.selectedStyle||l&&t.state.prevStyle||t.state.slideStyle||{};t.props.centerMode&&"horizontal"===t.props.axis&&(u=g(g({},u),{},{minWidth:t.props.centerSlidePercentage+"%"})),t.state.swiping&&t.state.swipeMovementStarted&&(u=g(g({},u),{},{pointerEvents:"none"}));var s={ref:function(e){return t.setItemsRef(e,i)},key:"itemKey"+i+(e?"clone":""),className:o.default.ITEM(!0,i===t.state.selectedItem,i===t.state.previousItem),onClick:t.handleClickItem.bind(t,i,n),style:u};return r.default.createElement("li",s,t.props.renderItem(n,{isSelected:i===t.state.selectedItem,isPrevious:i===t.state.previousItem}))})):[]}},{key:"renderControls",value:function(){var e=this,t=this.props,n=t.showIndicators,i=t.labels,o=t.renderIndicator,a=t.children;return n?r.default.createElement("ul",{className:"control-dots"},r.Children.map(a,(function(t,n){return o&&o(e.changeItem(n),n===e.state.selectedItem,n,i.item)}))):null}},{key:"renderStatus",value:function(){return this.props.showStatus?r.default.createElement("p",{className:"carousel-status"},this.props.statusFormatter(this.state.selectedItem+1,r.Children.count(this.props.children))):null}},{key:"renderThumbs",value:function(){return this.props.showThumbs&&this.props.children&&0!==r.Children.count(this.props.children)?r.default.createElement(a.default,{ref:this.setThumbsRef,onSelectItem:this.handleClickThumb,selectedItem:this.state.selectedItem,transitionTime:this.props.transitionTime,thumbWidth:this.props.thumbWidth,labels:this.props.labels,emulateTouch:this.props.emulateTouch},this.props.renderThumbs(this.props.children)):null}},{key:"render",value:function(){var e=this;if(!this.props.children||0===r.Children.count(this.props.children))return null;var t=this.props.swipeable&&r.Children.count(this.props.children)>1,n="horizontal"===this.props.axis,a=this.props.showArrows&&r.Children.count(this.props.children)>1,l=a&&(this.state.selectedItem>0||this.props.infiniteLoop)||!1,u=a&&(this.state.selectedItem<r.Children.count(this.props.children)-1||this.props.infiniteLoop)||!1,s=this.renderItems(!0),c=s.shift(),d=s.pop(),f={className:o.default.SLIDER(!0,this.state.swiping),onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:this.state.itemListStyle,tolerance:this.props.swipeScrollTolerance},h={};if(n){if(f.onSwipeLeft=this.onSwipeForward,f.onSwipeRight=this.onSwipeBackwards,this.props.dynamicHeight){var v=this.getVariableItemHeight(this.state.selectedItem);h.height=v||"auto"}}else f.onSwipeUp="natural"===this.props.verticalSwipe?this.onSwipeBackwards:this.onSwipeForward,f.onSwipeDown="natural"===this.props.verticalSwipe?this.onSwipeForward:this.onSwipeBackwards,f.style=g(g({},f.style),{},{height:this.state.itemSize}),h.height=this.state.itemSize;return r.default.createElement("div",{"aria-label":this.props.ariaLabel,className:o.default.ROOT(this.props.className),ref:this.setCarouselWrapperRef,tabIndex:this.props.useKeyboardArrows?0:void 0},r.default.createElement("div",{className:o.default.CAROUSEL(!0),style:{width:this.props.width}},this.renderControls(),this.props.renderArrowPrev(this.onClickPrev,l,this.props.labels.leftArrow),r.default.createElement("div",{className:o.default.WRAPPER(!0,this.props.axis),style:h},t?r.default.createElement(i.default,p({tagName:"ul",innerRef:this.setListRef},f,{allowMouseEvents:this.props.emulateTouch}),this.props.infiniteLoop&&d,this.renderItems(),this.props.infiniteLoop&&c):r.default.createElement("ul",{className:o.default.SLIDER(!0,this.state.swiping),ref:function(t){return e.setListRef(t)},style:this.state.itemListStyle||{}},this.props.infiniteLoop&&d,this.renderItems(),this.props.infiniteLoop&&c)),this.props.renderArrowNext(this.onClickNext,u,this.props.labels.rightArrow),this.renderStatus()),this.renderThumbs())}}])&&m(t.prototype,n),d&&m(t,d),h}(r.default.Component);t.default=D,k(D,"displayName","Carousel"),k(D,"defaultProps",{ariaLabel:void 0,axis:"horizontal",centerSlidePercentage:80,interval:3e3,labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},onClickItem:s.noop,onClickThumb:s.noop,onChange:s.noop,onSwipeStart:function(){},onSwipeEnd:function(){},onSwipeMove:function(){return!1},preventMovementUntilSwipeScrollTolerance:!1,renderArrowPrev:function(e,t,n){return r.default.createElement("button",{type:"button","aria-label":n,className:o.default.ARROW_PREV(!t),onClick:e})},renderArrowNext:function(e,t,n){return r.default.createElement("button",{type:"button","aria-label":n,className:o.default.ARROW_NEXT(!t),onClick:e})},renderIndicator:function(e,t,n,i){return r.default.createElement("li",{className:o.default.DOT(t),onClick:e,onKeyDown:e,value:n,key:n,role:"button",tabIndex:0,"aria-label":"".concat(i," ").concat(n+1)})},renderItem:function(e){return e},renderThumbs:function(e){var t=r.Children.map(e,(function(e){var t=e;if("img"!==e.type&&(t=r.Children.toArray(e.props.children).find((function(e){return"img"===e.type}))),t)return t}));return 0===t.filter((function(e){return e})).length?(console.warn("No images found! Can't build the thumb list without images. If you don't need thumbs, set showThumbs={false} in the Carousel. Note that it's not possible to get images rendered inside custom components. More info at https://github.com/leandrowd/react-responsive-carousel/blob/master/TROUBLESHOOTING.md"),[]):t},statusFormatter:s.defaultStatusFormatter,selectedItem:0,showArrows:!0,showIndicators:!0,showStatus:!0,showThumbs:!0,stopOnHover:!0,swipeScrollTolerance:5,swipeable:!0,transitionTime:350,verticalSwipe:"standard",width:"100%",animationHandler:"slide",swipeAnimationHandler:c.slideSwipeAnimationHandler,stopSwipingHandler:c.slideStopSwipingHandler})},96239:function(){},34705:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setPosition=t.getPosition=t.isKeyboardEvent=t.defaultStatusFormatter=t.noop=void 0;var r,i=n(66845),o=(r=n(30480))&&r.__esModule?r:{default:r};t.noop=function(){};t.defaultStatusFormatter=function(e,t){return"".concat(e," of ").concat(t)};t.isKeyboardEvent=function(e){return!!e&&e.hasOwnProperty("key")};t.getPosition=function(e,t){if(t.infiniteLoop&&++e,0===e)return 0;var n=i.Children.count(t.children);if(t.centerMode&&"horizontal"===t.axis){var r=-e*t.centerSlidePercentage,o=n-1;return e&&(e!==o||t.infiniteLoop)?r+=(100-t.centerSlidePercentage)/2:e===o&&(r+=100-t.centerSlidePercentage),r}return 100*-e};t.setPosition=function(e,t){var n={};return["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach((function(r){n[r]=(0,o.default)(e,"%",t)})),n}},91935:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==d(e)&&"function"!==typeof e)return{default:e};var t=c();if(t&&t.has(e))return t.get(e);var n={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var o=r?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(n,i,o):n[i]=e[i]}n.default=e,t&&t.set(e,n);return n}(n(66845)),i=s(n(75385)),o=n(24202),a=s(n(30480)),l=s(n(15650)),u=s(n(22124));function s(e){return e&&e.__esModule?e:{default:e}}function c(){if("function"!==typeof WeakMap)return null;var e=new WeakMap;return c=function(){return e},e}function d(e){return d="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function f(){return f=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},f.apply(this,arguments)}function h(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function p(e,t){return p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},p(e,t)}function v(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=m(e);if(t){var i=m(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return function(e,t){if(t&&("object"===d(t)||"function"===typeof t))return t;return g(e)}(this,n)}}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&p(e,t)}(d,e);var t,n,s,c=v(d);function d(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),y(g(t=c.call(this,e)),"itemsWrapperRef",void 0),y(g(t),"itemsListRef",void 0),y(g(t),"thumbsRef",void 0),y(g(t),"setItemsWrapperRef",(function(e){t.itemsWrapperRef=e})),y(g(t),"setItemsListRef",(function(e){t.itemsListRef=e})),y(g(t),"setThumbsRef",(function(e,n){t.thumbsRef||(t.thumbsRef=[]),t.thumbsRef[n]=e})),y(g(t),"updateSizes",(function(){if(t.props.children&&t.itemsWrapperRef&&t.thumbsRef){var e=r.Children.count(t.props.children),n=t.itemsWrapperRef.clientWidth,i=t.props.thumbWidth?t.props.thumbWidth:(0,o.outerWidth)(t.thumbsRef[0]),a=Math.floor(n/i),l=a<e,u=l?e-a:0;t.setState((function(e,n){return{itemSize:i,visibleItems:a,firstItem:l?t.getFirstItem(n.selectedItem):0,lastPosition:u,showArrows:l}}))}})),y(g(t),"handleClickItem",(function(e,n,r){if(!function(e){return e.hasOwnProperty("key")}(r)||"Enter"===r.key){var i=t.props.onSelectItem;"function"===typeof i&&i(e,n)}})),y(g(t),"onSwipeStart",(function(){t.setState({swiping:!0})})),y(g(t),"onSwipeEnd",(function(){t.setState({swiping:!1})})),y(g(t),"onSwipeMove",(function(e){var n=e.x;if(!t.state.itemSize||!t.itemsWrapperRef||!t.state.visibleItems)return!1;var i=r.Children.count(t.props.children),o=-100*t.state.firstItem/t.state.visibleItems;0===o&&n>0&&(n=0),o===100*-Math.max(i-t.state.visibleItems,0)/t.state.visibleItems&&n<0&&(n=0);var l=o+100/(t.itemsWrapperRef.clientWidth/n);return t.itemsListRef&&["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach((function(e){t.itemsListRef.style[e]=(0,a.default)(l,"%",t.props.axis)})),!0})),y(g(t),"slideRight",(function(e){t.moveTo(t.state.firstItem-("number"===typeof e?e:1))})),y(g(t),"slideLeft",(function(e){t.moveTo(t.state.firstItem+("number"===typeof e?e:1))})),y(g(t),"moveTo",(function(e){e=(e=e<0?0:e)>=t.state.lastPosition?t.state.lastPosition:e,t.setState({firstItem:e})})),t.state={selectedItem:e.selectedItem,swiping:!1,showArrows:!1,firstItem:0,visibleItems:0,lastPosition:0},t}return t=d,(n=[{key:"componentDidMount",value:function(){this.setupThumbs()}},{key:"componentDidUpdate",value:function(e){this.props.selectedItem!==this.state.selectedItem&&this.setState({selectedItem:this.props.selectedItem,firstItem:this.getFirstItem(this.props.selectedItem)}),this.props.children!==e.children&&this.updateSizes()}},{key:"componentWillUnmount",value:function(){this.destroyThumbs()}},{key:"setupThumbs",value:function(){(0,u.default)().addEventListener("resize",this.updateSizes),(0,u.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.updateSizes()}},{key:"destroyThumbs",value:function(){(0,u.default)().removeEventListener("resize",this.updateSizes),(0,u.default)().removeEventListener("DOMContentLoaded",this.updateSizes)}},{key:"getFirstItem",value:function(e){var t=e;return e>=this.state.lastPosition&&(t=this.state.lastPosition),e<this.state.firstItem+this.state.visibleItems&&(t=this.state.firstItem),e<this.state.firstItem&&(t=e),t}},{key:"renderItems",value:function(){var e=this;return this.props.children.map((function(t,n){var o=i.default.ITEM(!1,n===e.state.selectedItem),a={key:n,ref:function(t){return e.setThumbsRef(t,n)},className:o,onClick:e.handleClickItem.bind(e,n,e.props.children[n]),onKeyDown:e.handleClickItem.bind(e,n,e.props.children[n]),"aria-label":"".concat(e.props.labels.item," ").concat(n+1),style:{width:e.props.thumbWidth}};return r.default.createElement("li",f({},a,{role:"button",tabIndex:0}),t)}))}},{key:"render",value:function(){var e=this;if(!this.props.children)return null;var t,n=r.Children.count(this.props.children)>1,o=this.state.showArrows&&this.state.firstItem>0,u=this.state.showArrows&&this.state.firstItem<this.state.lastPosition,s=-this.state.firstItem*(this.state.itemSize||0),c=(0,a.default)(s,"px",this.props.axis),d=this.props.transitionTime+"ms";return t={WebkitTransform:c,MozTransform:c,MsTransform:c,OTransform:c,transform:c,msTransform:c,WebkitTransitionDuration:d,MozTransitionDuration:d,MsTransitionDuration:d,OTransitionDuration:d,transitionDuration:d,msTransitionDuration:d},r.default.createElement("div",{className:i.default.CAROUSEL(!1)},r.default.createElement("div",{className:i.default.WRAPPER(!1),ref:this.setItemsWrapperRef},r.default.createElement("button",{type:"button",className:i.default.ARROW_PREV(!o),onClick:function(){return e.slideRight()},"aria-label":this.props.labels.leftArrow}),n?r.default.createElement(l.default,{tagName:"ul",className:i.default.SLIDER(!1,this.state.swiping),onSwipeLeft:this.slideLeft,onSwipeRight:this.slideRight,onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:t,innerRef:this.setItemsListRef,allowMouseEvents:this.props.emulateTouch},this.renderItems()):r.default.createElement("ul",{className:i.default.SLIDER(!1,this.state.swiping),ref:function(t){return e.setItemsListRef(t)},style:t},this.renderItems()),r.default.createElement("button",{type:"button",className:i.default.ARROW_NEXT(!u),onClick:function(){return e.slideLeft()},"aria-label":this.props.labels.rightArrow})))}}])&&h(t.prototype,n),s&&h(t,s),d}(r.Component);t.default=b,y(b,"displayName","Thumbs"),y(b,"defaultProps",{axis:"horizontal",labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},selectedItem:0,thumbWidth:80,transitionTime:350})},75385:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,i=(r=n(23175))&&r.__esModule?r:{default:r};var o={ROOT:function(e){return(0,i.default)(function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({"carousel-root":!0},e||"",!!e))},CAROUSEL:function(e){return(0,i.default)({carousel:!0,"carousel-slider":e})},WRAPPER:function(e,t){return(0,i.default)({"thumbs-wrapper":!e,"slider-wrapper":e,"axis-horizontal":"horizontal"===t,"axis-vertical":"horizontal"!==t})},SLIDER:function(e,t){return(0,i.default)({thumbs:!e,slider:e,animated:!t})},ITEM:function(e,t,n){return(0,i.default)({thumb:!e,slide:e,selected:t,previous:n})},ARROW_PREV:function(e){return(0,i.default)({"control-arrow control-prev":!0,"control-disabled":e})},ARROW_NEXT:function(e){return(0,i.default)({"control-arrow control-next":!0,"control-disabled":e})},DOT:function(e){return(0,i.default)({dot:!0,selected:e})}};t.default=o},24202:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.outerWidth=void 0;t.outerWidth=function(e){var t=e.offsetWidth,n=getComputedStyle(e);return t+=parseInt(n.marginLeft)+parseInt(n.marginRight)}},44303:function(e,t,n){"use strict";Object.defineProperty(t,"lr",{enumerable:!0,get:function(){return r.default}});var r=a(n(42852)),i=n(96239),o=a(n(91935));function a(e){return e&&e.__esModule?e:{default:e}}},39896:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(){return document}},22124:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(){return window}},52347:function(e,t,n){var r;!function(){"use strict";var i={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function o(e){return function(e,t){var n,r,a,l,u,s,c,d,f,h=1,p=e.length,v="";for(r=0;r<p;r++)if("string"===typeof e[r])v+=e[r];else if("object"===typeof e[r]){if((l=e[r]).keys)for(n=t[h],a=0;a<l.keys.length;a++){if(void 0==n)throw new Error(o('[sprintf] Cannot access property "%s" of undefined value "%s"',l.keys[a],l.keys[a-1]));n=n[l.keys[a]]}else n=l.param_no?t[l.param_no]:t[h++];if(i.not_type.test(l.type)&&i.not_primitive.test(l.type)&&n instanceof Function&&(n=n()),i.numeric_arg.test(l.type)&&"number"!==typeof n&&isNaN(n))throw new TypeError(o("[sprintf] expecting number but found %T",n));switch(i.number.test(l.type)&&(d=n>=0),l.type){case"b":n=parseInt(n,10).toString(2);break;case"c":n=String.fromCharCode(parseInt(n,10));break;case"d":case"i":n=parseInt(n,10);break;case"j":n=JSON.stringify(n,null,l.width?parseInt(l.width):0);break;case"e":n=l.precision?parseFloat(n).toExponential(l.precision):parseFloat(n).toExponential();break;case"f":n=l.precision?parseFloat(n).toFixed(l.precision):parseFloat(n);break;case"g":n=l.precision?String(Number(n.toPrecision(l.precision))):parseFloat(n);break;case"o":n=(parseInt(n,10)>>>0).toString(8);break;case"s":n=String(n),n=l.precision?n.substring(0,l.precision):n;break;case"t":n=String(!!n),n=l.precision?n.substring(0,l.precision):n;break;case"T":n=Object.prototype.toString.call(n).slice(8,-1).toLowerCase(),n=l.precision?n.substring(0,l.precision):n;break;case"u":n=parseInt(n,10)>>>0;break;case"v":n=n.valueOf(),n=l.precision?n.substring(0,l.precision):n;break;case"x":n=(parseInt(n,10)>>>0).toString(16);break;case"X":n=(parseInt(n,10)>>>0).toString(16).toUpperCase()}i.json.test(l.type)?v+=n:(!i.number.test(l.type)||d&&!l.sign?f="":(f=d?"+":"-",n=n.toString().replace(i.sign,"")),s=l.pad_char?"0"===l.pad_char?"0":l.pad_char.charAt(1):" ",c=l.width-(f+n).length,u=l.width&&c>0?s.repeat(c):"",v+=l.align?f+n+u:"0"===s?f+u+n:u+f+n)}return v}(function(e){if(l[e])return l[e];var t,n=e,r=[],o=0;for(;n;){if(null!==(t=i.text.exec(n)))r.push(t[0]);else if(null!==(t=i.modulo.exec(n)))r.push("%");else{if(null===(t=i.placeholder.exec(n)))throw new SyntaxError("[sprintf] unexpected placeholder");if(t[2]){o|=1;var a=[],u=t[2],s=[];if(null===(s=i.key.exec(u)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(a.push(s[1]);""!==(u=u.substring(s[0].length));)if(null!==(s=i.key_access.exec(u)))a.push(s[1]);else{if(null===(s=i.index_access.exec(u)))throw new SyntaxError("[sprintf] failed to parse named argument key");a.push(s[1])}t[2]=a}else o|=2;if(3===o)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");r.push({placeholder:t[0],param_no:t[1],keys:t[2],sign:t[3],pad_char:t[4],align:t[5],width:t[6],precision:t[7],type:t[8]})}n=n.substring(t[0].length)}return l[e]=r}(e),arguments)}function a(e,t){return o.apply(null,[e].concat(t||[]))}var l=Object.create(null);t.sprintf=o,t.vsprintf=a,"undefined"!==typeof window&&(window.sprintf=o,window.vsprintf=a,void 0===(r=function(){return{sprintf:o,vsprintf:a}}.call(t,n,t,e))||(e.exports=r))}()},24665:function(){},2739:function(){},66709:function(e,t,n){"use strict";function r(e,t){this.v=e,this.k=t}n.d(t,{Z:function(){return r}})},187:function(e,t,n){"use strict";function r(e){var t,n,r,o=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);o--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new i(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function i(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return i=function(e){this.s=e,this.n=e.next},i.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new i(e)}n.d(t,{Z:function(){return r}})},38692:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(66709);function i(e){return new r.Z(e,0)}},22265:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var r=n(66709);function i(e){var t,n;function i(t,n){try{var a=e[t](n),l=a.value,u=l instanceof r.Z;Promise.resolve(u?l.v:l).then((function(n){if(u){var r="return"===t?"return":"next";if(!l.k||n.done)return i(r,n);n=e[r](n).value}o(a.done?"return":"normal",n)}),(function(e){i("throw",e)}))}catch(s){o("throw",s)}}function o(e,r){switch(e){case"return":t.resolve({value:r,done:!0});break;case"throw":t.reject(r);break;default:t.resolve({value:r,done:!1})}(t=t.next)?i(t.key,t.arg):n=null}this._invoke=function(e,r){return new Promise((function(o,a){var l={key:e,arg:r,resolve:o,reject:a,next:null};n?n=n.next=l:(t=n=l,i(e,r))}))},"function"!=typeof e.return&&(this.return=void 0)}function o(e){return function(){return new i(e.apply(this,arguments))}}i.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},i.prototype.next=function(e){return this._invoke("next",e)},i.prototype.throw=function(e){return this._invoke("throw",e)},i.prototype.return=function(e){return this._invoke("return",e)}},67487:function(e,t,n){"use strict";n.d(t,{d:function(){return d}});var r=n(11026),i=n(66845);var o=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|inert|itemProp|itemScope|itemType|itemID|itemRef|on|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,a=function(e){var t={};return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}((function(e){return o.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),l=function(){var e=Array.prototype.slice.call(arguments).filter(Boolean),t={},n=[];e.forEach((function(e){(e?e.split(" "):[]).forEach((function(e){if(e.startsWith("atm_")){var i=e.split("_"),o=(0,r.Z)(i,2)[1];t[o]=e}else n.push(e)}))}));var i=[];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&i.push(t[o]);return i.push.apply(i,n),i.join(" ")},u=function(e){return e.toUpperCase()===e},s=function(e,t){var n={};return Object.keys(e).filter(function(e){return function(t){return-1===e.indexOf(t)}}(t)).forEach((function(t){n[t]=e[t]})),n};var c=function(e,t){if(!("string"===typeof e||"number"===typeof e&&isFinite(e))){var n="object"===typeof e?JSON.stringify(e):String(e);console.warn("An interpolation evaluated to '".concat(n,"' in the component '").concat(t,"', which is probably a mistake. You should explicitly cast or transform the value to a string."))}};var d=new Proxy((function(e){return function(t){if(Array.isArray(t))throw new Error('Using the "styled" tag in runtime is not supported. Make sure you have set up the Babel plugin correctly. See https://github.com/callstack/linaria#setup');var n=function(n,r){var o=n.as,d=void 0===o?e:o,f=n.class,h=function(e,t,n){var r=s(t,n);return"string"!==typeof e||-1!==e.indexOf("-")||u(e[0])||Object.keys(r).forEach((function(e){a(e)||delete r[e]})),r}(d,n,["as","class"]);h.ref=r,h.className=t.atomic?l(t.class,h.className||f):l(h.className||f,t.class);var p=t.vars;if(p){var v={};for(var g in p){var m=p[g],y=m[0],b=m[1]||"",w="function"===typeof y?y(n):y;c(w,t.name),v["--".concat(g)]="".concat(w).concat(b)}var x=h.style||{},k=Object.keys(x);k.length>0&&k.forEach((function(e){v[e]=x[e]})),h.style=v}return e.__linaria&&e!==d?(h.as=d,i.createElement(e,h)):i.createElement(d,h)},r=i.forwardRef?i.forwardRef(n):function(e){var t=s(e,["innerRef"]);return n(t,e.innerRef)};return r.displayName=t.name,r.__linaria={className:t.class,extends:e},r}}),{get:function(e,t){return e(t)}})},94379:function(e,t,n){"use strict";n.d(t,{Bn:function(){return nn}});var r=n(11092),i=n(27791),o=n(53782),a=n(649),l=n(60726),u=n(11026),s=n(50189),c=n(67487),d=n(35396),f=n(66845),h=n(25773),p=n(7865),v=n(50669),g=n(33940),m=n(22951),y=n(91976),b=n(67591),w=n(64649),x=n(17664);function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function D(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function C(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?D(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function S(e){return S=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},S(e)}function E(e,t){return!t||"object"!==typeof t&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function F(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=S(e);if(t){var i=S(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return E(this,n)}}var M=["className","clearValue","cx","getStyles","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],Z=function(){};function A(e,t){return t?"-"===t[0]?e+t:e+"__"+t:e}function R(e,t,n){var r=[n];if(t&&e)for(var i in t)t.hasOwnProperty(i)&&t[i]&&r.push("".concat(A(e,i)));return r.filter((function(e){return e})).map((function(e){return String(e).trim()})).join(" ")}var I=function(e){return t=e,Array.isArray(t)?e.filter(Boolean):"object"===(0,g.Z)(e)&&null!==e?[e]:[];var t},T=function(e){return e.className,e.clearValue,e.cx,e.getStyles,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme,C({},(0,o.Z)(e,M))};function O(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function P(e){return O(e)?window.pageYOffset:e.scrollTop}function H(e,t){O(e)?window.scrollTo(0,t):e.scrollTop=t}function L(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Z,i=P(e),o=t-i,a=0;!function t(){var l,u=o*((l=(l=a+=10)/n-1)*l*l+1)+i;H(e,u),a<n?window.requestAnimationFrame(t):r(e)}()}function z(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}var B=!1,_={get passive(){return B=!0}},V="undefined"!==typeof window?window:{};V.addEventListener&&V.removeEventListener&&(V.addEventListener("p",Z,_),V.removeEventListener("p",Z,!1));var N=B;function W(e){return null!=e}function j(e,t,n){return e?t:n}function U(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,i=e.placement,o=e.shouldScroll,a=e.isFixedPosition,l=e.theme.spacing,u=function(e){var t=getComputedStyle(e),n="absolute"===t.position,r=/(auto|scroll)/;if("fixed"===t.position)return document.documentElement;for(var i=e;i=i.parentElement;)if(t=getComputedStyle(i),(!n||"static"!==t.position)&&r.test(t.overflow+t.overflowY+t.overflowX))return i;return document.documentElement}(n),s={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return s;var c,d=u.getBoundingClientRect().height,f=n.getBoundingClientRect(),h=f.bottom,p=f.height,v=f.top,g=n.offsetParent.getBoundingClientRect().top,m=a?window.innerHeight:O(c=u)?window.innerHeight:c.clientHeight,y=P(u),b=parseInt(getComputedStyle(n).marginBottom,10),w=parseInt(getComputedStyle(n).marginTop,10),x=g-w,k=m-v,D=x+y,C=d-y-v,S=h-m+y+b,E=y+v-w,F=160;switch(i){case"auto":case"bottom":if(k>=p)return{placement:"bottom",maxHeight:t};if(C>=p&&!a)return o&&L(u,S,F),{placement:"bottom",maxHeight:t};if(!a&&C>=r||a&&k>=r)return o&&L(u,S,F),{placement:"bottom",maxHeight:a?k-b:C-b};if("auto"===i||a){var M=t,Z=a?x:D;return Z>=r&&(M=Math.min(Z-b-l.controlHeight,t)),{placement:"top",maxHeight:M}}if("bottom"===i)return o&&H(u,S),{placement:"bottom",maxHeight:t};break;case"top":if(x>=p)return{placement:"top",maxHeight:t};if(D>=p&&!a)return o&&L(u,E,F),{placement:"top",maxHeight:t};if(!a&&D>=r||a&&x>=r){var A=t;return(!a&&D>=r||a&&x>=r)&&(A=a?x-w:D-w),o&&L(u,E,F),{placement:"top",maxHeight:A}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(i,'".'))}return s}var X=function(e){return"auto"===e?"bottom":e},Y=(0,f.createContext)({getPortalPlacement:null}),K=function(e){(0,b.Z)(n,e);var t=F(n);function n(){var e;(0,m.Z)(this,n);for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];return(e=t.call.apply(t,[this].concat(i))).state={maxHeight:e.props.maxMenuHeight,placement:null},e.context=void 0,e.getPlacement=function(t){var n=e.props,r=n.minMenuHeight,i=n.maxMenuHeight,o=n.menuPlacement,a=n.menuPosition,l=n.menuShouldScrollIntoView,u=n.theme;if(t){var s="fixed"===a,c=U({maxHeight:i,menuEl:t,minHeight:r,placement:o,shouldScroll:l&&!s,isFixedPosition:s,theme:u}),d=e.context.getPortalPlacement;d&&d(c),e.setState(c)}},e.getUpdatedProps=function(){var t=e.props.menuPlacement,n=e.state.placement||X(t);return C(C({},e.props),{},{placement:n,maxHeight:e.state.maxHeight})},e}return(0,y.Z)(n,[{key:"render",value:function(){return(0,this.props.children)({ref:this.getPlacement,placerProps:this.getUpdatedProps()})}}]),n}(f.Component);K.contextType=Y;var $=function(e){var t=e.theme,n=t.spacing.baseUnit;return{color:t.colors.neutral40,padding:"".concat(2*n,"px ").concat(3*n,"px"),textAlign:"center"}},G=$,q=$,Q=function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.innerProps;return(0,p.tZ)("div",(0,h.Z)({css:i("noOptionsMessage",e),className:r({"menu-notice":!0,"menu-notice--no-options":!0},n)},o),t)};Q.defaultProps={children:"No options"};var J=function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.innerProps;return(0,p.tZ)("div",(0,h.Z)({css:i("loadingMessage",e),className:r({"menu-notice":!0,"menu-notice--loading":!0},n)},o),t)};J.defaultProps={children:"Loading..."};var ee,te=function(e){(0,b.Z)(n,e);var t=F(n);function n(){var e;(0,m.Z)(this,n);for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];return(e=t.call.apply(t,[this].concat(i))).state={placement:null},e.getPortalPlacement=function(t){var n=t.placement;n!==X(e.props.menuPlacement)&&e.setState({placement:n})},e}return(0,y.Z)(n,[{key:"render",value:function(){var e=this.props,t=e.appendTo,n=e.children,r=e.className,i=e.controlElement,o=e.cx,a=e.innerProps,l=e.menuPlacement,u=e.menuPosition,s=e.getStyles,c="fixed"===u;if(!t&&!c||!i)return null;var d=this.state.placement||X(l),f=function(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}(i),v=c?0:window.pageYOffset,g={offset:f[d]+v,position:u,rect:f},m=(0,p.tZ)("div",(0,h.Z)({css:s("menuPortal",g),className:o({"menu-portal":!0},r)},a),n);return(0,p.tZ)(Y.Provider,{value:{getPortalPlacement:this.getPortalPlacement}},t?(0,x.createPortal)(m,t):m)}}]),n}(f.Component),ne=["size"];var re={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},ie=function(e){var t=e.size,n=(0,o.Z)(e,ne);return(0,p.tZ)("svg",(0,h.Z)({height:t,width:t,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:re},n))},oe=function(e){return(0,p.tZ)(ie,(0,h.Z)({size:20},e),(0,p.tZ)("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},ae=function(e){return(0,p.tZ)(ie,(0,h.Z)({size:20},e),(0,p.tZ)("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},le=function(e){var t=e.isFocused,n=e.theme,r=n.spacing.baseUnit,i=n.colors;return{label:"indicatorContainer",color:t?i.neutral60:i.neutral20,display:"flex",padding:2*r,transition:"color 150ms",":hover":{color:t?i.neutral80:i.neutral40}}},ue=le,se=le,ce=(0,p.F4)(ee||(ee=(0,v.Z)(["\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n"]))),de=function(e){var t=e.delay,n=e.offset;return(0,p.tZ)("span",{css:(0,p.iv)({animation:"".concat(ce," 1s ease-in-out ").concat(t,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:n?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","","","")})},fe=function(e){var t=e.className,n=e.cx,r=e.getStyles,i=e.innerProps,o=e.isRtl;return(0,p.tZ)("div",(0,h.Z)({css:r("loadingIndicator",e),className:n({indicator:!0,"loading-indicator":!0},t)},i),(0,p.tZ)(de,{delay:0,offset:o}),(0,p.tZ)(de,{delay:160,offset:!0}),(0,p.tZ)(de,{delay:320,offset:!o}))};fe.defaultProps={size:4};var he=["data"],pe=["innerRef","isDisabled","isHidden","inputClassName"],ve={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},ge={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":C({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},ve)},me=function(e){return C({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},ve)},ye=function(e){var t=e.children,n=e.innerProps;return(0,p.tZ)("div",n,t)};var be={ClearIndicator:function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.innerProps;return(0,p.tZ)("div",(0,h.Z)({css:i("clearIndicator",e),className:r({indicator:!0,"clear-indicator":!0},n)},o),t||(0,p.tZ)(oe,null))},Control:function(e){var t=e.children,n=e.cx,r=e.getStyles,i=e.className,o=e.isDisabled,a=e.isFocused,l=e.innerRef,u=e.innerProps,s=e.menuIsOpen;return(0,p.tZ)("div",(0,h.Z)({ref:l,css:r("control",e),className:n({control:!0,"control--is-disabled":o,"control--is-focused":a,"control--menu-is-open":s},i)},u),t)},DropdownIndicator:function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.innerProps;return(0,p.tZ)("div",(0,h.Z)({css:i("dropdownIndicator",e),className:r({indicator:!0,"dropdown-indicator":!0},n)},o),t||(0,p.tZ)(ae,null))},DownChevron:ae,CrossIcon:oe,Group:function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.Heading,a=e.headingProps,l=e.innerProps,u=e.label,s=e.theme,c=e.selectProps;return(0,p.tZ)("div",(0,h.Z)({css:i("group",e),className:r({group:!0},n)},l),(0,p.tZ)(o,(0,h.Z)({},a,{selectProps:c,theme:s,getStyles:i,cx:r}),u),(0,p.tZ)("div",null,t))},GroupHeading:function(e){var t=e.getStyles,n=e.cx,r=e.className,i=T(e);i.data;var a=(0,o.Z)(i,he);return(0,p.tZ)("div",(0,h.Z)({css:t("groupHeading",e),className:n({"group-heading":!0},r)},a))},IndicatorsContainer:function(e){var t=e.children,n=e.className,r=e.cx,i=e.innerProps,o=e.getStyles;return(0,p.tZ)("div",(0,h.Z)({css:o("indicatorsContainer",e),className:r({indicators:!0},n)},i),t)},IndicatorSeparator:function(e){var t=e.className,n=e.cx,r=e.getStyles,i=e.innerProps;return(0,p.tZ)("span",(0,h.Z)({},i,{css:r("indicatorSeparator",e),className:n({"indicator-separator":!0},t)}))},Input:function(e){var t=e.className,n=e.cx,r=e.getStyles,i=e.value,a=T(e),l=a.innerRef,u=a.isDisabled,s=a.isHidden,c=a.inputClassName,d=(0,o.Z)(a,pe);return(0,p.tZ)("div",{className:n({"input-container":!0},t),css:r("input",e),"data-value":i||""},(0,p.tZ)("input",(0,h.Z)({className:n({input:!0},c),ref:l,style:me(s),disabled:u},d)))},LoadingIndicator:fe,Menu:function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.innerRef,a=e.innerProps;return(0,p.tZ)("div",(0,h.Z)({css:i("menu",e),className:r({menu:!0},n),ref:o},a),t)},MenuList:function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.innerProps,a=e.innerRef,l=e.isMulti;return(0,p.tZ)("div",(0,h.Z)({css:i("menuList",e),className:r({"menu-list":!0,"menu-list--is-multi":l},n),ref:a},o),t)},MenuPortal:te,LoadingMessage:J,NoOptionsMessage:Q,MultiValue:function(e){var t=e.children,n=e.className,r=e.components,i=e.cx,o=e.data,a=e.getStyles,l=e.innerProps,u=e.isDisabled,s=e.removeProps,c=e.selectProps,d=r.Container,f=r.Label,h=r.Remove;return(0,p.tZ)(p.ms,null,(function(r){var v=r.css,g=r.cx;return(0,p.tZ)(d,{data:o,innerProps:C({className:g(v(a("multiValue",e)),i({"multi-value":!0,"multi-value--is-disabled":u},n))},l),selectProps:c},(0,p.tZ)(f,{data:o,innerProps:{className:g(v(a("multiValueLabel",e)),i({"multi-value__label":!0},n))},selectProps:c},t),(0,p.tZ)(h,{data:o,innerProps:C({className:g(v(a("multiValueRemove",e)),i({"multi-value__remove":!0},n)),"aria-label":"Remove ".concat(t||"option")},s),selectProps:c}))}))},MultiValueContainer:ye,MultiValueLabel:ye,MultiValueRemove:function(e){var t=e.children,n=e.innerProps;return(0,p.tZ)("div",(0,h.Z)({role:"button"},n),t||(0,p.tZ)(oe,{size:14}))},Option:function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.isDisabled,a=e.isFocused,l=e.isSelected,u=e.innerRef,s=e.innerProps;return(0,p.tZ)("div",(0,h.Z)({css:i("option",e),className:r({option:!0,"option--is-disabled":o,"option--is-focused":a,"option--is-selected":l},n),ref:u,"aria-disabled":o},s),t)},Placeholder:function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.innerProps;return(0,p.tZ)("div",(0,h.Z)({css:i("placeholder",e),className:r({placeholder:!0},n)},o),t)},SelectContainer:function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.innerProps,a=e.isDisabled,l=e.isRtl;return(0,p.tZ)("div",(0,h.Z)({css:i("container",e),className:r({"--is-disabled":a,"--is-rtl":l},n)},o),t)},SingleValue:function(e){var t=e.children,n=e.className,r=e.cx,i=e.getStyles,o=e.isDisabled,a=e.innerProps;return(0,p.tZ)("div",(0,h.Z)({css:i("singleValue",e),className:r({"single-value":!0,"single-value--is-disabled":o},n)},a),t)},ValueContainer:function(e){var t=e.children,n=e.className,r=e.cx,i=e.innerProps,o=e.isMulti,a=e.getStyles,l=e.hasValue;return(0,p.tZ)("div",(0,h.Z)({css:a("valueContainer",e),className:r({"value-container":!0,"value-container--is-multi":o,"value-container--has-value":l},n)},i),t)}},we=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];var xe=n(61687);for(var ke={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},De=function(e){return(0,p.tZ)("span",(0,h.Z)({css:ke},e))},Ce={guidance:function(e){var t=e.isSearchable,n=e.isMulti,r=e.isDisabled,i=e.tabSelectsValue;switch(e.context){case"menu":return"Use Up and Down to choose options".concat(r?"":", press Enter to select the currently focused option",", press Escape to exit the menu").concat(i?", press Tab to select the option and exit the menu":"",".");case"input":return"".concat(e["aria-label"]||"Select"," is focused ").concat(t?",type to refine list":"",", press Down to open the menu, ").concat(n?" press left to focus selected values":"");case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var t=e.action,n=e.label,r=void 0===n?"":n,i=e.labels,o=e.isDisabled;switch(t){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(r,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(i.length>1?"s":""," ").concat(i.join(","),", selected.");case"select-option":return"option ".concat(r,o?" is disabled. Select another option.":", selected.");default:return""}},onFocus:function(e){var t=e.context,n=e.focused,r=e.options,i=e.label,o=void 0===i?"":i,a=e.selectValue,l=e.isDisabled,u=e.isSelected,s=function(e,t){return e&&e.length?"".concat(e.indexOf(t)+1," of ").concat(e.length):""};if("value"===t&&a)return"value ".concat(o," focused, ").concat(s(a,n),".");if("menu"===t){var c=l?" disabled":"",d="".concat(u?"selected":"focused").concat(c);return"option ".concat(o," ").concat(d,", ").concat(s(r,n),".")}return""},onFilter:function(e){var t=e.inputValue,n=e.resultsMessage;return"".concat(n).concat(t?" for search term "+t:"",".")}},Se=function(e){var t=e.ariaSelection,n=e.focusedOption,r=e.focusedValue,i=e.focusableOptions,o=e.isFocused,a=e.selectValue,l=e.selectProps,u=e.id,s=l.ariaLiveMessages,c=l.getOptionLabel,d=l.inputValue,h=l.isMulti,v=l.isOptionDisabled,g=l.isSearchable,m=l.menuIsOpen,y=l.options,b=l.screenReaderStatus,w=l.tabSelectsValue,x=l["aria-label"],k=l["aria-live"],D=(0,f.useMemo)((function(){return C(C({},Ce),s||{})}),[s]),S=(0,f.useMemo)((function(){var e,n="";if(t&&D.onChange){var r=t.option,i=t.options,o=t.removedValue,l=t.removedValues,u=t.value,s=o||r||(e=u,Array.isArray(e)?null:e),d=s?c(s):"",f=i||l||void 0,h=f?f.map(c):[],p=C({isDisabled:s&&v(s,a),label:d,labels:h},t);n=D.onChange(p)}return n}),[t,D,v,a,c]),E=(0,f.useMemo)((function(){var e="",t=n||r,i=!!(n&&a&&a.includes(n));if(t&&D.onFocus){var o={focused:t,label:c(t),isDisabled:v(t,a),isSelected:i,options:y,context:t===n?"menu":"value",selectValue:a};e=D.onFocus(o)}return e}),[n,r,c,v,D,y,a]),F=(0,f.useMemo)((function(){var e="";if(m&&y.length&&D.onFilter){var t=b({count:i.length});e=D.onFilter({inputValue:d,resultsMessage:t})}return e}),[i,d,m,D,y,b]),M=(0,f.useMemo)((function(){var e="";if(D.guidance){var t=r?"value":m?"menu":"input";e=D.guidance({"aria-label":x,context:t,isDisabled:n&&v(n,a),isMulti:h,isSearchable:g,tabSelectsValue:w})}return e}),[x,n,r,h,v,g,m,D,a,w]),Z="".concat(E," ").concat(F," ").concat(M),A=(0,p.tZ)(f.Fragment,null,(0,p.tZ)("span",{id:"aria-selection"},S),(0,p.tZ)("span",{id:"aria-context"},Z)),R="initial-input-focus"===(null===t||void 0===t?void 0:t.action);return(0,p.tZ)(f.Fragment,null,(0,p.tZ)(De,{id:u},R&&A),(0,p.tZ)(De,{"aria-live":k,"aria-atomic":"false","aria-relevant":"additions text"},o&&!R&&A))},Ee=[{base:"A",letters:"A\u24b6\uff21\xc0\xc1\xc2\u1ea6\u1ea4\u1eaa\u1ea8\xc3\u0100\u0102\u1eb0\u1eae\u1eb4\u1eb2\u0226\u01e0\xc4\u01de\u1ea2\xc5\u01fa\u01cd\u0200\u0202\u1ea0\u1eac\u1eb6\u1e00\u0104\u023a\u2c6f"},{base:"AA",letters:"\ua732"},{base:"AE",letters:"\xc6\u01fc\u01e2"},{base:"AO",letters:"\ua734"},{base:"AU",letters:"\ua736"},{base:"AV",letters:"\ua738\ua73a"},{base:"AY",letters:"\ua73c"},{base:"B",letters:"B\u24b7\uff22\u1e02\u1e04\u1e06\u0243\u0182\u0181"},{base:"C",letters:"C\u24b8\uff23\u0106\u0108\u010a\u010c\xc7\u1e08\u0187\u023b\ua73e"},{base:"D",letters:"D\u24b9\uff24\u1e0a\u010e\u1e0c\u1e10\u1e12\u1e0e\u0110\u018b\u018a\u0189\ua779"},{base:"DZ",letters:"\u01f1\u01c4"},{base:"Dz",letters:"\u01f2\u01c5"},{base:"E",letters:"E\u24ba\uff25\xc8\xc9\xca\u1ec0\u1ebe\u1ec4\u1ec2\u1ebc\u0112\u1e14\u1e16\u0114\u0116\xcb\u1eba\u011a\u0204\u0206\u1eb8\u1ec6\u0228\u1e1c\u0118\u1e18\u1e1a\u0190\u018e"},{base:"F",letters:"F\u24bb\uff26\u1e1e\u0191\ua77b"},{base:"G",letters:"G\u24bc\uff27\u01f4\u011c\u1e20\u011e\u0120\u01e6\u0122\u01e4\u0193\ua7a0\ua77d\ua77e"},{base:"H",letters:"H\u24bd\uff28\u0124\u1e22\u1e26\u021e\u1e24\u1e28\u1e2a\u0126\u2c67\u2c75\ua78d"},{base:"I",letters:"I\u24be\uff29\xcc\xcd\xce\u0128\u012a\u012c\u0130\xcf\u1e2e\u1ec8\u01cf\u0208\u020a\u1eca\u012e\u1e2c\u0197"},{base:"J",letters:"J\u24bf\uff2a\u0134\u0248"},{base:"K",letters:"K\u24c0\uff2b\u1e30\u01e8\u1e32\u0136\u1e34\u0198\u2c69\ua740\ua742\ua744\ua7a2"},{base:"L",letters:"L\u24c1\uff2c\u013f\u0139\u013d\u1e36\u1e38\u013b\u1e3c\u1e3a\u0141\u023d\u2c62\u2c60\ua748\ua746\ua780"},{base:"LJ",letters:"\u01c7"},{base:"Lj",letters:"\u01c8"},{base:"M",letters:"M\u24c2\uff2d\u1e3e\u1e40\u1e42\u2c6e\u019c"},{base:"N",letters:"N\u24c3\uff2e\u01f8\u0143\xd1\u1e44\u0147\u1e46\u0145\u1e4a\u1e48\u0220\u019d\ua790\ua7a4"},{base:"NJ",letters:"\u01ca"},{base:"Nj",letters:"\u01cb"},{base:"O",letters:"O\u24c4\uff2f\xd2\xd3\xd4\u1ed2\u1ed0\u1ed6\u1ed4\xd5\u1e4c\u022c\u1e4e\u014c\u1e50\u1e52\u014e\u022e\u0230\xd6\u022a\u1ece\u0150\u01d1\u020c\u020e\u01a0\u1edc\u1eda\u1ee0\u1ede\u1ee2\u1ecc\u1ed8\u01ea\u01ec\xd8\u01fe\u0186\u019f\ua74a\ua74c"},{base:"OI",letters:"\u01a2"},{base:"OO",letters:"\ua74e"},{base:"OU",letters:"\u0222"},{base:"P",letters:"P\u24c5\uff30\u1e54\u1e56\u01a4\u2c63\ua750\ua752\ua754"},{base:"Q",letters:"Q\u24c6\uff31\ua756\ua758\u024a"},{base:"R",letters:"R\u24c7\uff32\u0154\u1e58\u0158\u0210\u0212\u1e5a\u1e5c\u0156\u1e5e\u024c\u2c64\ua75a\ua7a6\ua782"},{base:"S",letters:"S\u24c8\uff33\u1e9e\u015a\u1e64\u015c\u1e60\u0160\u1e66\u1e62\u1e68\u0218\u015e\u2c7e\ua7a8\ua784"},{base:"T",letters:"T\u24c9\uff34\u1e6a\u0164\u1e6c\u021a\u0162\u1e70\u1e6e\u0166\u01ac\u01ae\u023e\ua786"},{base:"TZ",letters:"\ua728"},{base:"U",letters:"U\u24ca\uff35\xd9\xda\xdb\u0168\u1e78\u016a\u1e7a\u016c\xdc\u01db\u01d7\u01d5\u01d9\u1ee6\u016e\u0170\u01d3\u0214\u0216\u01af\u1eea\u1ee8\u1eee\u1eec\u1ef0\u1ee4\u1e72\u0172\u1e76\u1e74\u0244"},{base:"V",letters:"V\u24cb\uff36\u1e7c\u1e7e\u01b2\ua75e\u0245"},{base:"VY",letters:"\ua760"},{base:"W",letters:"W\u24cc\uff37\u1e80\u1e82\u0174\u1e86\u1e84\u1e88\u2c72"},{base:"X",letters:"X\u24cd\uff38\u1e8a\u1e8c"},{base:"Y",letters:"Y\u24ce\uff39\u1ef2\xdd\u0176\u1ef8\u0232\u1e8e\u0178\u1ef6\u1ef4\u01b3\u024e\u1efe"},{base:"Z",letters:"Z\u24cf\uff3a\u0179\u1e90\u017b\u017d\u1e92\u1e94\u01b5\u0224\u2c7f\u2c6b\ua762"},{base:"a",letters:"a\u24d0\uff41\u1e9a\xe0\xe1\xe2\u1ea7\u1ea5\u1eab\u1ea9\xe3\u0101\u0103\u1eb1\u1eaf\u1eb5\u1eb3\u0227\u01e1\xe4\u01df\u1ea3\xe5\u01fb\u01ce\u0201\u0203\u1ea1\u1ead\u1eb7\u1e01\u0105\u2c65\u0250"},{base:"aa",letters:"\ua733"},{base:"ae",letters:"\xe6\u01fd\u01e3"},{base:"ao",letters:"\ua735"},{base:"au",letters:"\ua737"},{base:"av",letters:"\ua739\ua73b"},{base:"ay",letters:"\ua73d"},{base:"b",letters:"b\u24d1\uff42\u1e03\u1e05\u1e07\u0180\u0183\u0253"},{base:"c",letters:"c\u24d2\uff43\u0107\u0109\u010b\u010d\xe7\u1e09\u0188\u023c\ua73f\u2184"},{base:"d",letters:"d\u24d3\uff44\u1e0b\u010f\u1e0d\u1e11\u1e13\u1e0f\u0111\u018c\u0256\u0257\ua77a"},{base:"dz",letters:"\u01f3\u01c6"},{base:"e",letters:"e\u24d4\uff45\xe8\xe9\xea\u1ec1\u1ebf\u1ec5\u1ec3\u1ebd\u0113\u1e15\u1e17\u0115\u0117\xeb\u1ebb\u011b\u0205\u0207\u1eb9\u1ec7\u0229\u1e1d\u0119\u1e19\u1e1b\u0247\u025b\u01dd"},{base:"f",letters:"f\u24d5\uff46\u1e1f\u0192\ua77c"},{base:"g",letters:"g\u24d6\uff47\u01f5\u011d\u1e21\u011f\u0121\u01e7\u0123\u01e5\u0260\ua7a1\u1d79\ua77f"},{base:"h",letters:"h\u24d7\uff48\u0125\u1e23\u1e27\u021f\u1e25\u1e29\u1e2b\u1e96\u0127\u2c68\u2c76\u0265"},{base:"hv",letters:"\u0195"},{base:"i",letters:"i\u24d8\uff49\xec\xed\xee\u0129\u012b\u012d\xef\u1e2f\u1ec9\u01d0\u0209\u020b\u1ecb\u012f\u1e2d\u0268\u0131"},{base:"j",letters:"j\u24d9\uff4a\u0135\u01f0\u0249"},{base:"k",letters:"k\u24da\uff4b\u1e31\u01e9\u1e33\u0137\u1e35\u0199\u2c6a\ua741\ua743\ua745\ua7a3"},{base:"l",letters:"l\u24db\uff4c\u0140\u013a\u013e\u1e37\u1e39\u013c\u1e3d\u1e3b\u017f\u0142\u019a\u026b\u2c61\ua749\ua781\ua747"},{base:"lj",letters:"\u01c9"},{base:"m",letters:"m\u24dc\uff4d\u1e3f\u1e41\u1e43\u0271\u026f"},{base:"n",letters:"n\u24dd\uff4e\u01f9\u0144\xf1\u1e45\u0148\u1e47\u0146\u1e4b\u1e49\u019e\u0272\u0149\ua791\ua7a5"},{base:"nj",letters:"\u01cc"},{base:"o",letters:"o\u24de\uff4f\xf2\xf3\xf4\u1ed3\u1ed1\u1ed7\u1ed5\xf5\u1e4d\u022d\u1e4f\u014d\u1e51\u1e53\u014f\u022f\u0231\xf6\u022b\u1ecf\u0151\u01d2\u020d\u020f\u01a1\u1edd\u1edb\u1ee1\u1edf\u1ee3\u1ecd\u1ed9\u01eb\u01ed\xf8\u01ff\u0254\ua74b\ua74d\u0275"},{base:"oi",letters:"\u01a3"},{base:"ou",letters:"\u0223"},{base:"oo",letters:"\ua74f"},{base:"p",letters:"p\u24df\uff50\u1e55\u1e57\u01a5\u1d7d\ua751\ua753\ua755"},{base:"q",letters:"q\u24e0\uff51\u024b\ua757\ua759"},{base:"r",letters:"r\u24e1\uff52\u0155\u1e59\u0159\u0211\u0213\u1e5b\u1e5d\u0157\u1e5f\u024d\u027d\ua75b\ua7a7\ua783"},{base:"s",letters:"s\u24e2\uff53\xdf\u015b\u1e65\u015d\u1e61\u0161\u1e67\u1e63\u1e69\u0219\u015f\u023f\ua7a9\ua785\u1e9b"},{base:"t",letters:"t\u24e3\uff54\u1e6b\u1e97\u0165\u1e6d\u021b\u0163\u1e71\u1e6f\u0167\u01ad\u0288\u2c66\ua787"},{base:"tz",letters:"\ua729"},{base:"u",letters:"u\u24e4\uff55\xf9\xfa\xfb\u0169\u1e79\u016b\u1e7b\u016d\xfc\u01dc\u01d8\u01d6\u01da\u1ee7\u016f\u0171\u01d4\u0215\u0217\u01b0\u1eeb\u1ee9\u1eef\u1eed\u1ef1\u1ee5\u1e73\u0173\u1e77\u1e75\u0289"},{base:"v",letters:"v\u24e5\uff56\u1e7d\u1e7f\u028b\ua75f\u028c"},{base:"vy",letters:"\ua761"},{base:"w",letters:"w\u24e6\uff57\u1e81\u1e83\u0175\u1e87\u1e85\u1e98\u1e89\u2c73"},{base:"x",letters:"x\u24e7\uff58\u1e8b\u1e8d"},{base:"y",letters:"y\u24e8\uff59\u1ef3\xfd\u0177\u1ef9\u0233\u1e8f\xff\u1ef7\u1e99\u1ef5\u01b4\u024f\u1eff"},{base:"z",letters:"z\u24e9\uff5a\u017a\u1e91\u017c\u017e\u1e93\u1e95\u01b6\u0225\u0240\u2c6c\ua763"}],Fe=new RegExp("["+Ee.map((function(e){return e.letters})).join("")+"]","g"),Me={},Ze=0;Ze<Ee.length;Ze++)for(var Ae=Ee[Ze],Re=0;Re<Ae.letters.length;Re++)Me[Ae.letters[Re]]=Ae.base;var Ie=function(e){return e.replace(Fe,(function(e){return Me[e]}))},Te=(0,xe.Z)(Ie),Oe=function(e){return e.replace(/^\s+|\s+$/g,"")},Pe=function(e){return"".concat(e.label," ").concat(e.value)},He=["innerRef"];function Le(e){var t=e.innerRef,n=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i=Object.entries(e).filter((function(e){var t=(0,u.Z)(e,1)[0];return!n.includes(t)}));return i.reduce((function(e,t){var n=(0,u.Z)(t,2),r=n[0],i=n[1];return e[r]=i,e}),{})}((0,o.Z)(e,He),"onExited","in","enter","exit","appear");return(0,p.tZ)("input",(0,h.Z)({ref:t},n,{css:(0,p.iv)({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","","","")}))}var ze=function(e){e.preventDefault(),e.stopPropagation()};var Be=["boxSizing","height","overflow","paddingRight","position"],_e={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function Ve(e){e.preventDefault()}function Ne(e){e.stopPropagation()}function We(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;0===e?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function je(){return"ontouchstart"in window||navigator.maxTouchPoints}var Ue=!("undefined"===typeof window||!window.document||!window.document.createElement),Xe=0,Ye={capture:!1,passive:!1};var Ke=function(){return document.activeElement&&document.activeElement.blur()},$e={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function Ge(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,i=function(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,i=e.onTopArrive,o=e.onTopLeave,a=(0,f.useRef)(!1),l=(0,f.useRef)(!1),u=(0,f.useRef)(0),s=(0,f.useRef)(null),c=(0,f.useCallback)((function(e,t){if(null!==s.current){var u=s.current,c=u.scrollTop,d=u.scrollHeight,f=u.clientHeight,h=s.current,p=t>0,v=d-f-c,g=!1;v>t&&a.current&&(r&&r(e),a.current=!1),p&&l.current&&(o&&o(e),l.current=!1),p&&t>v?(n&&!a.current&&n(e),h.scrollTop=d,g=!0,a.current=!0):!p&&-t>c&&(i&&!l.current&&i(e),h.scrollTop=0,g=!0,l.current=!0),g&&ze(e)}}),[n,r,i,o]),d=(0,f.useCallback)((function(e){c(e,e.deltaY)}),[c]),h=(0,f.useCallback)((function(e){u.current=e.changedTouches[0].clientY}),[]),p=(0,f.useCallback)((function(e){var t=u.current-e.changedTouches[0].clientY;c(e,t)}),[c]),v=(0,f.useCallback)((function(e){if(e){var t=!!N&&{passive:!1};e.addEventListener("wheel",d,t),e.addEventListener("touchstart",h,t),e.addEventListener("touchmove",p,t)}}),[p,h,d]),g=(0,f.useCallback)((function(e){e&&(e.removeEventListener("wheel",d,!1),e.removeEventListener("touchstart",h,!1),e.removeEventListener("touchmove",p,!1))}),[p,h,d]);return(0,f.useEffect)((function(){if(t){var e=s.current;return v(e),function(){g(e)}}}),[t,v,g]),function(e){s.current=e}}({isEnabled:void 0===r||r,onBottomArrive:e.onBottomArrive,onBottomLeave:e.onBottomLeave,onTopArrive:e.onTopArrive,onTopLeave:e.onTopLeave}),o=function(e){var t=e.isEnabled,n=e.accountForScrollbars,r=void 0===n||n,i=(0,f.useRef)({}),o=(0,f.useRef)(null),a=(0,f.useCallback)((function(e){if(Ue){var t=document.body,n=t&&t.style;if(r&&Be.forEach((function(e){var t=n&&n[e];i.current[e]=t})),r&&Xe<1){var o=parseInt(i.current.paddingRight,10)||0,a=document.body?document.body.clientWidth:0,l=window.innerWidth-a+o||0;Object.keys(_e).forEach((function(e){var t=_e[e];n&&(n[e]=t)})),n&&(n.paddingRight="".concat(l,"px"))}t&&je()&&(t.addEventListener("touchmove",Ve,Ye),e&&(e.addEventListener("touchstart",We,Ye),e.addEventListener("touchmove",Ne,Ye))),Xe+=1}}),[r]),l=(0,f.useCallback)((function(e){if(Ue){var t=document.body,n=t&&t.style;Xe=Math.max(Xe-1,0),r&&Xe<1&&Be.forEach((function(e){var t=i.current[e];n&&(n[e]=t)})),t&&je()&&(t.removeEventListener("touchmove",Ve,Ye),e&&(e.removeEventListener("touchstart",We,Ye),e.removeEventListener("touchmove",Ne,Ye)))}}),[r]);return(0,f.useEffect)((function(){if(t){var e=o.current;return a(e),function(){l(e)}}}),[t,a,l]),function(e){o.current=e}}({isEnabled:n});return(0,p.tZ)(f.Fragment,null,n&&(0,p.tZ)("div",{onClick:Ke,css:$e}),t((function(e){i(e),o(e)})))}var qe={clearIndicator:se,container:function(e){var t=e.isDisabled;return{label:"container",direction:e.isRtl?"rtl":void 0,pointerEvents:t?"none":void 0,position:"relative"}},control:function(e){var t=e.isDisabled,n=e.isFocused,r=e.theme,i=r.colors,o=r.borderRadius,a=r.spacing;return{label:"control",alignItems:"center",backgroundColor:t?i.neutral5:i.neutral0,borderColor:t?i.neutral10:n?i.primary:i.neutral20,borderRadius:o,borderStyle:"solid",borderWidth:1,boxShadow:n?"0 0 0 1px ".concat(i.primary):void 0,cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:a.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms","&:hover":{borderColor:n?i.primary:i.neutral30}}},dropdownIndicator:ue,group:function(e){var t=e.theme.spacing;return{paddingBottom:2*t.baseUnit,paddingTop:2*t.baseUnit}},groupHeading:function(e){var t=e.theme.spacing;return{label:"group",color:"#999",cursor:"default",display:"block",fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:3*t.baseUnit,paddingRight:3*t.baseUnit,textTransform:"uppercase"}},indicatorsContainer:function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},indicatorSeparator:function(e){var t=e.isDisabled,n=e.theme,r=n.spacing.baseUnit,i=n.colors;return{label:"indicatorSeparator",alignSelf:"stretch",backgroundColor:t?i.neutral10:i.neutral20,marginBottom:2*r,marginTop:2*r,width:1}},input:function(e){var t=e.isDisabled,n=e.value,r=e.theme,i=r.spacing,o=r.colors;return C({margin:i.baseUnit/2,paddingBottom:i.baseUnit/2,paddingTop:i.baseUnit/2,visibility:t?"hidden":"visible",color:o.neutral80,transform:n?"translateZ(0)":""},ge)},loadingIndicator:function(e){var t=e.isFocused,n=e.size,r=e.theme,i=r.colors,o=r.spacing.baseUnit;return{label:"loadingIndicator",color:t?i.neutral60:i.neutral20,display:"flex",padding:2*o,transition:"color 150ms",alignSelf:"center",fontSize:n,lineHeight:1,marginRight:n,textAlign:"center",verticalAlign:"middle"}},loadingMessage:q,menu:function(e){var t,n=e.placement,r=e.theme,i=r.borderRadius,o=r.spacing,a=r.colors;return t={label:"menu"},(0,w.Z)(t,function(e){return e?{bottom:"top",top:"bottom"}[e]:"bottom"}(n),"100%"),(0,w.Z)(t,"backgroundColor",a.neutral0),(0,w.Z)(t,"borderRadius",i),(0,w.Z)(t,"boxShadow","0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)"),(0,w.Z)(t,"marginBottom",o.menuGutter),(0,w.Z)(t,"marginTop",o.menuGutter),(0,w.Z)(t,"position","absolute"),(0,w.Z)(t,"width","100%"),(0,w.Z)(t,"zIndex",1),t},menuList:function(e){var t=e.maxHeight,n=e.theme.spacing.baseUnit;return{maxHeight:t,overflowY:"auto",paddingBottom:n,paddingTop:n,position:"relative",WebkitOverflowScrolling:"touch"}},menuPortal:function(e){var t=e.rect,n=e.offset,r=e.position;return{left:t.left,position:r,top:n,width:t.width,zIndex:1}},multiValue:function(e){var t=e.theme,n=t.spacing,r=t.borderRadius;return{label:"multiValue",backgroundColor:t.colors.neutral10,borderRadius:r/2,display:"flex",margin:n.baseUnit/2,minWidth:0}},multiValueLabel:function(e){var t=e.theme,n=t.borderRadius,r=t.colors,i=e.cropWithEllipsis;return{borderRadius:n/2,color:r.neutral80,fontSize:"85%",overflow:"hidden",padding:3,paddingLeft:6,textOverflow:i||void 0===i?"ellipsis":void 0,whiteSpace:"nowrap"}},multiValueRemove:function(e){var t=e.theme,n=t.spacing,r=t.borderRadius,i=t.colors;return{alignItems:"center",borderRadius:r/2,backgroundColor:e.isFocused?i.dangerLight:void 0,display:"flex",paddingLeft:n.baseUnit,paddingRight:n.baseUnit,":hover":{backgroundColor:i.dangerLight,color:i.danger}}},noOptionsMessage:G,option:function(e){var t=e.isDisabled,n=e.isFocused,r=e.isSelected,i=e.theme,o=i.spacing,a=i.colors;return{label:"option",backgroundColor:r?a.primary:n?a.primary25:"transparent",color:t?a.neutral20:r?a.neutral0:"inherit",cursor:"default",display:"block",fontSize:"inherit",padding:"".concat(2*o.baseUnit,"px ").concat(3*o.baseUnit,"px"),width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",":active":{backgroundColor:t?void 0:r?a.primary:a.primary50}}},placeholder:function(e){var t=e.theme,n=t.spacing;return{label:"placeholder",color:t.colors.neutral50,gridArea:"1 / 1 / 2 / 3",marginLeft:n.baseUnit/2,marginRight:n.baseUnit/2}},singleValue:function(e){var t=e.isDisabled,n=e.theme,r=n.spacing,i=n.colors;return{label:"singleValue",color:t?i.neutral40:i.neutral80,gridArea:"1 / 1 / 2 / 3",marginLeft:r.baseUnit/2,marginRight:r.baseUnit/2,maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},valueContainer:function(e){var t=e.theme.spacing,n=e.isMulti,r=e.hasValue,i=e.selectProps.controlShouldRenderValue;return{alignItems:"center",display:n&&r&&i?"flex":"grid",flex:1,flexWrap:"wrap",padding:"".concat(t.baseUnit/2,"px ").concat(2*t.baseUnit,"px"),WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"}}};var Qe,Je={borderRadius:4,colors:{primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},spacing:{baseUnit:4,controlHeight:38,menuGutter:8}},et={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:z(),captureMenuScroll:!z(),closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:function(e,t){if(e.data.__isNew__)return!0;var n=C({ignoreCase:!0,ignoreAccents:!0,stringify:Pe,trim:!0,matchFrom:"any"},Qe),r=n.ignoreCase,i=n.ignoreAccents,o=n.stringify,a=n.trim,l=n.matchFrom,u=a?Oe(t):t,s=a?Oe(o(e)):o(e);return r&&(u=u.toLowerCase(),s=s.toLowerCase()),i&&(u=Te(u),s=Ie(s)),"start"===l?s.substr(0,u.length)===u:s.indexOf(u)>-1},formatGroupLabel:function(e){return e.label},getOptionLabel:function(e){return e.label},getOptionValue:function(e){return e.value},isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:function(e){return!!e.isDisabled},loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!function(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch(e){return!1}}(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var t=e.count;return"".concat(t," result").concat(1!==t?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0};function tt(e,t,n,r){return{type:"option",data:t,isDisabled:lt(e,t,n),isSelected:ut(e,t,n),label:ot(e,t),value:at(e,t),index:r}}function nt(e,t){return e.options.map((function(n,r){if("options"in n){var i=n.options.map((function(n,r){return tt(e,n,t,r)})).filter((function(t){return it(e,t)}));return i.length>0?{type:"group",data:n,options:i,index:r}:void 0}var o=tt(e,n,t,r);return it(e,o)?o:void 0})).filter(W)}function rt(e){return e.reduce((function(e,t){return"group"===t.type?e.push.apply(e,(0,a.Z)(t.options.map((function(e){return e.data})))):e.push(t.data),e}),[])}function it(e,t){var n=e.inputValue,r=void 0===n?"":n,i=t.data,o=t.isSelected,a=t.label,l=t.value;return(!ct(e)||!o)&&st(e,{label:a,value:l,data:i},r)}var ot=function(e,t){return e.getOptionLabel(t)},at=function(e,t){return e.getOptionValue(t)};function lt(e,t,n){return"function"===typeof e.isOptionDisabled&&e.isOptionDisabled(t,n)}function ut(e,t,n){if(n.indexOf(t)>-1)return!0;if("function"===typeof e.isOptionSelected)return e.isOptionSelected(t,n);var r=at(e,t);return n.some((function(t){return at(e,t)===r}))}function st(e,t,n){return!e.filterOption||e.filterOption(t,n)}var ct=function(e){var t=e.hideSelectedOptions,n=e.isMulti;return void 0===t?n:t},dt=1,ft=function(e){(0,b.Z)(n,e);var t=F(n);function n(e){var r;return(0,m.Z)(this,n),(r=t.call(this,e)).state={ariaSelection:null,focusedOption:null,focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0},r.blockOptionHover=!1,r.isComposing=!1,r.commonProps=void 0,r.initialTouchX=0,r.initialTouchY=0,r.instancePrefix="",r.openAfterFocus=!1,r.scrollToFocusedOptionOnUpdate=!1,r.userIsDragging=void 0,r.controlRef=null,r.getControlRef=function(e){r.controlRef=e},r.focusedOptionRef=null,r.getFocusedOptionRef=function(e){r.focusedOptionRef=e},r.menuListRef=null,r.getMenuListRef=function(e){r.menuListRef=e},r.inputRef=null,r.getInputRef=function(e){r.inputRef=e},r.focus=r.focusInput,r.blur=r.blurInput,r.onChange=function(e,t){var n=r.props,i=n.onChange,o=n.name;t.name=o,r.ariaOnChange(e,t),i(e,t)},r.setValue=function(e,t,n){var i=r.props,o=i.closeMenuOnSelect,a=i.isMulti,l=i.inputValue;r.onInputChange("",{action:"set-value",prevInputValue:l}),o&&(r.setState({inputIsHiddenAfterUpdate:!a}),r.onMenuClose()),r.setState({clearFocusValueOnUpdate:!0}),r.onChange(e,{action:t,option:n})},r.selectOption=function(e){var t=r.props,n=t.blurInputOnSelect,i=t.isMulti,o=t.name,l=r.state.selectValue,u=i&&r.isOptionSelected(e,l),s=r.isOptionDisabled(e,l);if(u){var c=r.getOptionValue(e);r.setValue(l.filter((function(e){return r.getOptionValue(e)!==c})),"deselect-option",e)}else{if(s)return void r.ariaOnChange(e,{action:"select-option",option:e,name:o});i?r.setValue([].concat((0,a.Z)(l),[e]),"select-option",e):r.setValue(e,"select-option")}n&&r.blurInput()},r.removeValue=function(e){var t=r.props.isMulti,n=r.state.selectValue,i=r.getOptionValue(e),o=n.filter((function(e){return r.getOptionValue(e)!==i})),a=j(t,o,o[0]||null);r.onChange(a,{action:"remove-value",removedValue:e}),r.focusInput()},r.clearValue=function(){var e=r.state.selectValue;r.onChange(j(r.props.isMulti,[],null),{action:"clear",removedValues:e})},r.popValue=function(){var e=r.props.isMulti,t=r.state.selectValue,n=t[t.length-1],i=t.slice(0,t.length-1),o=j(e,i,i[0]||null);r.onChange(o,{action:"pop-value",removedValue:n})},r.getValue=function(){return r.state.selectValue},r.cx=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return R.apply(void 0,[r.props.classNamePrefix].concat(t))},r.getOptionLabel=function(e){return ot(r.props,e)},r.getOptionValue=function(e){return at(r.props,e)},r.getStyles=function(e,t){var n=qe[e](t);n.boxSizing="border-box";var i=r.props.styles[e];return i?i(n,t):n},r.getElementId=function(e){return"".concat(r.instancePrefix,"-").concat(e)},r.getComponents=function(){return e=r.props,C(C({},be),e.components);var e},r.buildCategorizedOptions=function(){return nt(r.props,r.state.selectValue)},r.getCategorizedOptions=function(){return r.props.menuIsOpen?r.buildCategorizedOptions():[]},r.buildFocusableOptions=function(){return rt(r.buildCategorizedOptions())},r.getFocusableOptions=function(){return r.props.menuIsOpen?r.buildFocusableOptions():[]},r.ariaOnChange=function(e,t){r.setState({ariaSelection:C({value:e},t)})},r.onMenuMouseDown=function(e){0===e.button&&(e.stopPropagation(),e.preventDefault(),r.focusInput())},r.onMenuMouseMove=function(e){r.blockOptionHover=!1},r.onControlMouseDown=function(e){if(!e.defaultPrevented){var t=r.props.openMenuOnClick;r.state.isFocused?r.props.menuIsOpen?"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&r.onMenuClose():t&&r.openMenu("first"):(t&&(r.openAfterFocus=!0),r.focusInput()),"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&e.preventDefault()}},r.onDropdownIndicatorMouseDown=function(e){if((!e||"mousedown"!==e.type||0===e.button)&&!r.props.isDisabled){var t=r.props,n=t.isMulti,i=t.menuIsOpen;r.focusInput(),i?(r.setState({inputIsHiddenAfterUpdate:!n}),r.onMenuClose()):r.openMenu("first"),e.preventDefault()}},r.onClearIndicatorMouseDown=function(e){e&&"mousedown"===e.type&&0!==e.button||(r.clearValue(),e.preventDefault(),r.openAfterFocus=!1,"touchend"===e.type?r.focusInput():setTimeout((function(){return r.focusInput()})))},r.onScroll=function(e){"boolean"===typeof r.props.closeMenuOnScroll?e.target instanceof HTMLElement&&O(e.target)&&r.props.onMenuClose():"function"===typeof r.props.closeMenuOnScroll&&r.props.closeMenuOnScroll(e)&&r.props.onMenuClose()},r.onCompositionStart=function(){r.isComposing=!0},r.onCompositionEnd=function(){r.isComposing=!1},r.onTouchStart=function(e){var t=e.touches,n=t&&t.item(0);n&&(r.initialTouchX=n.clientX,r.initialTouchY=n.clientY,r.userIsDragging=!1)},r.onTouchMove=function(e){var t=e.touches,n=t&&t.item(0);if(n){var i=Math.abs(n.clientX-r.initialTouchX),o=Math.abs(n.clientY-r.initialTouchY);r.userIsDragging=i>5||o>5}},r.onTouchEnd=function(e){r.userIsDragging||(r.controlRef&&!r.controlRef.contains(e.target)&&r.menuListRef&&!r.menuListRef.contains(e.target)&&r.blurInput(),r.initialTouchX=0,r.initialTouchY=0)},r.onControlTouchEnd=function(e){r.userIsDragging||r.onControlMouseDown(e)},r.onClearIndicatorTouchEnd=function(e){r.userIsDragging||r.onClearIndicatorMouseDown(e)},r.onDropdownIndicatorTouchEnd=function(e){r.userIsDragging||r.onDropdownIndicatorMouseDown(e)},r.handleInputChange=function(e){var t=r.props.inputValue,n=e.currentTarget.value;r.setState({inputIsHiddenAfterUpdate:!1}),r.onInputChange(n,{action:"input-change",prevInputValue:t}),r.props.menuIsOpen||r.onMenuOpen()},r.onInputFocus=function(e){r.props.onFocus&&r.props.onFocus(e),r.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(r.openAfterFocus||r.props.openMenuOnFocus)&&r.openMenu("first"),r.openAfterFocus=!1},r.onInputBlur=function(e){var t=r.props.inputValue;r.menuListRef&&r.menuListRef.contains(document.activeElement)?r.inputRef.focus():(r.props.onBlur&&r.props.onBlur(e),r.onInputChange("",{action:"input-blur",prevInputValue:t}),r.onMenuClose(),r.setState({focusedValue:null,isFocused:!1}))},r.onOptionHover=function(e){r.blockOptionHover||r.state.focusedOption===e||r.setState({focusedOption:e})},r.shouldHideSelectedOptions=function(){return ct(r.props)},r.onKeyDown=function(e){var t=r.props,n=t.isMulti,i=t.backspaceRemovesValue,o=t.escapeClearsValue,a=t.inputValue,l=t.isClearable,u=t.isDisabled,s=t.menuIsOpen,c=t.onKeyDown,d=t.tabSelectsValue,f=t.openMenuOnFocus,h=r.state,p=h.focusedOption,v=h.focusedValue,g=h.selectValue;if(!u&&("function"!==typeof c||(c(e),!e.defaultPrevented))){switch(r.blockOptionHover=!0,e.key){case"ArrowLeft":if(!n||a)return;r.focusValue("previous");break;case"ArrowRight":if(!n||a)return;r.focusValue("next");break;case"Delete":case"Backspace":if(a)return;if(v)r.removeValue(v);else{if(!i)return;n?r.popValue():l&&r.clearValue()}break;case"Tab":if(r.isComposing)return;if(e.shiftKey||!s||!d||!p||f&&r.isOptionSelected(p,g))return;r.selectOption(p);break;case"Enter":if(229===e.keyCode)break;if(s){if(!p)return;if(r.isComposing)return;r.selectOption(p);break}return;case"Escape":s?(r.setState({inputIsHiddenAfterUpdate:!1}),r.onInputChange("",{action:"menu-close",prevInputValue:a}),r.onMenuClose()):l&&o&&r.clearValue();break;case" ":if(a)return;if(!s){r.openMenu("first");break}if(!p)return;r.selectOption(p);break;case"ArrowUp":s?r.focusOption("up"):r.openMenu("last");break;case"ArrowDown":s?r.focusOption("down"):r.openMenu("first");break;case"PageUp":if(!s)return;r.focusOption("pageup");break;case"PageDown":if(!s)return;r.focusOption("pagedown");break;case"Home":if(!s)return;r.focusOption("first");break;case"End":if(!s)return;r.focusOption("last");break;default:return}e.preventDefault()}},r.instancePrefix="react-select-"+(r.props.instanceId||++dt),r.state.selectValue=I(e.value),r}return(0,y.Z)(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput()}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isDisabled,r=t.menuIsOpen,i=this.state.isFocused;(i&&!n&&e.isDisabled||i&&r&&!e.menuIsOpen)&&this.focusInput(),i&&n&&!e.isDisabled&&this.setState({isFocused:!1},this.onMenuClose),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(!function(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),i=t.offsetHeight/3;r.bottom+i>n.bottom?H(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+i,e.scrollHeight)):r.top-i<n.top&&H(e,Math.max(t.offsetTop-i,0))}(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,t){this.props.onInputChange(e,t)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var t=this,n=this.state,r=n.selectValue,i=n.isFocused,o=this.buildFocusableOptions(),a="first"===e?0:o.length-1;if(!this.props.isMulti){var l=o.indexOf(r[0]);l>-1&&(a=l)}this.scrollToFocusedOptionOnUpdate=!(i&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:o[a]},(function(){return t.onMenuOpen()}))}},{key:"focusValue",value:function(e){var t=this.state,n=t.selectValue,r=t.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var i=n.indexOf(r);r||(i=-1);var o=n.length-1,a=-1;if(n.length){switch(e){case"previous":a=0===i?0:-1===i?o:i-1;break;case"next":i>-1&&i<o&&(a=i+1)}this.setState({inputIsHidden:-1!==a,focusedValue:n[a]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"first",t=this.props.pageSize,n=this.state.focusedOption,r=this.getFocusableOptions();if(r.length){var i=0,o=r.indexOf(n);n||(o=-1),"up"===e?i=o>0?o-1:r.length-1:"down"===e?i=(o+1)%r.length:"pageup"===e?(i=o-t)<0&&(i=0):"pagedown"===e?(i=o+t)>r.length-1&&(i=r.length-1):"last"===e&&(i=r.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:r[i],focusedValue:null})}}},{key:"getTheme",value:function(){return this.props.theme?"function"===typeof this.props.theme?this.props.theme(Je):C(C({},Je),this.props.theme):Je}},{key:"getCommonProps",value:function(){var e=this.clearValue,t=this.cx,n=this.getStyles,r=this.getValue,i=this.selectOption,o=this.setValue,a=this.props,l=a.isMulti,u=a.isRtl,s=a.options;return{clearValue:e,cx:t,getStyles:n,getValue:r,hasValue:this.hasValue(),isMulti:l,isRtl:u,options:s,selectOption:i,selectProps:a,setValue:o,theme:this.getTheme()}}},{key:"hasValue",value:function(){return this.state.selectValue.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,t=e.isClearable,n=e.isMulti;return void 0===t?n:t}},{key:"isOptionDisabled",value:function(e,t){return lt(this.props,e,t)}},{key:"isOptionSelected",value:function(e,t){return ut(this.props,e,t)}},{key:"filterOption",value:function(e,t){return st(this.props,e,t)}},{key:"formatOptionLabel",value:function(e,t){if("function"===typeof this.props.formatOptionLabel){var n=this.props.inputValue,r=this.state.selectValue;return this.props.formatOptionLabel(e,{context:t,inputValue:n,selectValue:r})}return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,t=e.isDisabled,n=e.isSearchable,r=e.inputId,i=e.inputValue,o=e.tabIndex,a=e.form,l=e.menuIsOpen,u=this.getComponents().Input,s=this.state,c=s.inputIsHidden,d=s.ariaSelection,p=this.commonProps,v=r||this.getElementId("input"),g=C(C(C({"aria-autocomplete":"list","aria-expanded":l,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],role:"combobox"},l&&{"aria-controls":this.getElementId("listbox"),"aria-owns":this.getElementId("listbox")}),!n&&{"aria-readonly":!0}),this.hasValue()?"initial-input-focus"===(null===d||void 0===d?void 0:d.action)&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return n?f.createElement(u,(0,h.Z)({},p,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:v,innerRef:this.getInputRef,isDisabled:t,isHidden:c,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:o,form:a,type:"text",value:i},g)):f.createElement(Le,(0,h.Z)({id:v,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:Z,onFocus:this.onInputFocus,disabled:t,tabIndex:o,inputMode:"none",form:a,value:""},g))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,t=this.getComponents(),n=t.MultiValue,r=t.MultiValueContainer,i=t.MultiValueLabel,o=t.MultiValueRemove,a=t.SingleValue,l=t.Placeholder,u=this.commonProps,s=this.props,c=s.controlShouldRenderValue,d=s.isDisabled,p=s.isMulti,v=s.inputValue,g=s.placeholder,m=this.state,y=m.selectValue,b=m.focusedValue,w=m.isFocused;if(!this.hasValue()||!c)return v?null:f.createElement(l,(0,h.Z)({},u,{key:"placeholder",isDisabled:d,isFocused:w,innerProps:{id:this.getElementId("placeholder")}}),g);if(p)return y.map((function(t,a){var l=t===b,s="".concat(e.getOptionLabel(t),"-").concat(e.getOptionValue(t));return f.createElement(n,(0,h.Z)({},u,{components:{Container:r,Label:i,Remove:o},isFocused:l,isDisabled:d,key:s,index:a,removeProps:{onClick:function(){return e.removeValue(t)},onTouchEnd:function(){return e.removeValue(t)},onMouseDown:function(e){e.preventDefault()}},data:t}),e.formatOptionLabel(t,"value"))}));if(v)return null;var x=y[0];return f.createElement(a,(0,h.Z)({},u,{data:x,isDisabled:d}),this.formatOptionLabel(x,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents().ClearIndicator,t=this.commonProps,n=this.props,r=n.isDisabled,i=n.isLoading,o=this.state.isFocused;if(!this.isClearable()||!e||r||!this.hasValue()||i)return null;var a={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(e,(0,h.Z)({},t,{innerProps:a,isFocused:o}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents().LoadingIndicator,t=this.commonProps,n=this.props,r=n.isDisabled,i=n.isLoading,o=this.state.isFocused;if(!e||!i)return null;return f.createElement(e,(0,h.Z)({},t,{innerProps:{"aria-hidden":"true"},isDisabled:r,isFocused:o}))}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),t=e.DropdownIndicator,n=e.IndicatorSeparator;if(!t||!n)return null;var r=this.commonProps,i=this.props.isDisabled,o=this.state.isFocused;return f.createElement(n,(0,h.Z)({},r,{isDisabled:i,isFocused:o}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents().DropdownIndicator;if(!e)return null;var t=this.commonProps,n=this.props.isDisabled,r=this.state.isFocused,i={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(e,(0,h.Z)({},t,{innerProps:i,isDisabled:n,isFocused:r}))}},{key:"renderMenu",value:function(){var e=this,t=this.getComponents(),n=t.Group,r=t.GroupHeading,i=t.Menu,o=t.MenuList,a=t.MenuPortal,l=t.LoadingMessage,u=t.NoOptionsMessage,s=t.Option,c=this.commonProps,d=this.state.focusedOption,p=this.props,v=p.captureMenuScroll,g=p.inputValue,m=p.isLoading,y=p.loadingMessage,b=p.minMenuHeight,w=p.maxMenuHeight,x=p.menuIsOpen,k=p.menuPlacement,D=p.menuPosition,C=p.menuPortalTarget,S=p.menuShouldBlockScroll,E=p.menuShouldScrollIntoView,F=p.noOptionsMessage,M=p.onMenuScrollToTop,Z=p.onMenuScrollToBottom;if(!x)return null;var A,R=function(t,n){var r=t.type,i=t.data,o=t.isDisabled,a=t.isSelected,l=t.label,u=t.value,p=d===i,v=o?void 0:function(){return e.onOptionHover(i)},g=o?void 0:function(){return e.selectOption(i)},m="".concat(e.getElementId("option"),"-").concat(n),y={id:m,onClick:g,onMouseMove:v,onMouseOver:v,tabIndex:-1};return f.createElement(s,(0,h.Z)({},c,{innerProps:y,data:i,isDisabled:o,isSelected:a,key:m,label:l,type:r,value:u,isFocused:p,innerRef:p?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(t.data,"menu"))};if(this.hasOptions())A=this.getCategorizedOptions().map((function(t){if("group"===t.type){var i=t.data,o=t.options,a=t.index,l="".concat(e.getElementId("group"),"-").concat(a),u="".concat(l,"-heading");return f.createElement(n,(0,h.Z)({},c,{key:l,data:i,options:o,Heading:r,headingProps:{id:u,data:t.data},label:e.formatGroupLabel(t.data)}),t.options.map((function(e){return R(e,"".concat(a,"-").concat(e.index))})))}if("option"===t.type)return R(t,"".concat(t.index))}));else if(m){var I=y({inputValue:g});if(null===I)return null;A=f.createElement(l,c,I)}else{var T=F({inputValue:g});if(null===T)return null;A=f.createElement(u,c,T)}var O={minMenuHeight:b,maxMenuHeight:w,menuPlacement:k,menuPosition:D,menuShouldScrollIntoView:E},P=f.createElement(K,(0,h.Z)({},c,O),(function(t){var n=t.ref,r=t.placerProps,a=r.placement,l=r.maxHeight;return f.createElement(i,(0,h.Z)({},c,O,{innerRef:n,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove,id:e.getElementId("listbox")},isLoading:m,placement:a}),f.createElement(Ge,{captureEnabled:v,onTopArrive:M,onBottomArrive:Z,lockEnabled:S},(function(t){return f.createElement(o,(0,h.Z)({},c,{innerRef:function(n){e.getMenuListRef(n),t(n)},isLoading:m,maxHeight:l,focusedOption:d}),A)})))}));return C||"fixed"===D?f.createElement(a,(0,h.Z)({},c,{appendTo:C,controlElement:this.controlRef,menuPlacement:k,menuPosition:D}),P):P}},{key:"renderFormField",value:function(){var e=this,t=this.props,n=t.delimiter,r=t.isDisabled,i=t.isMulti,o=t.name,a=this.state.selectValue;if(o&&!r){if(i){if(n){var l=a.map((function(t){return e.getOptionValue(t)})).join(n);return f.createElement("input",{name:o,type:"hidden",value:l})}var u=a.length>0?a.map((function(t,n){return f.createElement("input",{key:"i-".concat(n),name:o,type:"hidden",value:e.getOptionValue(t)})})):f.createElement("input",{name:o,type:"hidden"});return f.createElement("div",null,u)}var s=a[0]?this.getOptionValue(a[0]):"";return f.createElement("input",{name:o,type:"hidden",value:s})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,t=this.state,n=t.ariaSelection,r=t.focusedOption,i=t.focusedValue,o=t.isFocused,a=t.selectValue,l=this.getFocusableOptions();return f.createElement(Se,(0,h.Z)({},e,{id:this.getElementId("live-region"),ariaSelection:n,focusedOption:r,focusedValue:i,isFocused:o,selectValue:a,focusableOptions:l}))}},{key:"render",value:function(){var e=this.getComponents(),t=e.Control,n=e.IndicatorsContainer,r=e.SelectContainer,i=e.ValueContainer,o=this.props,a=o.className,l=o.id,u=o.isDisabled,s=o.menuIsOpen,c=this.state.isFocused,d=this.commonProps=this.getCommonProps();return f.createElement(r,(0,h.Z)({},d,{className:a,innerProps:{id:l,onKeyDown:this.onKeyDown},isDisabled:u,isFocused:c}),this.renderLiveRegion(),f.createElement(t,(0,h.Z)({},d,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:u,isFocused:c,menuIsOpen:s}),f.createElement(i,(0,h.Z)({},d,{isDisabled:u}),this.renderPlaceholderOrValue(),this.renderInput()),f.createElement(n,(0,h.Z)({},d,{isDisabled:u}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=t.prevProps,r=t.clearFocusValueOnUpdate,i=t.inputIsHiddenAfterUpdate,o=t.ariaSelection,a=t.isFocused,l=t.prevWasFocused,u=e.options,s=e.value,c=e.menuIsOpen,d=e.inputValue,f=e.isMulti,h=I(s),p={};if(n&&(s!==n.value||u!==n.options||c!==n.menuIsOpen||d!==n.inputValue)){var v=c?function(e,t){return rt(nt(e,t))}(e,h):[],g=r?function(e,t){var n=e.focusedValue,r=e.selectValue.indexOf(n);if(r>-1){if(t.indexOf(n)>-1)return n;if(r<t.length)return t[r]}return null}(t,h):null,m=function(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}(t,v);p={selectValue:h,focusedOption:m,focusedValue:g,clearFocusValueOnUpdate:!1}}var y=null!=i&&e!==n?{inputIsHidden:i,inputIsHiddenAfterUpdate:void 0}:{},b=o,w=a&&l;return a&&!w&&(b={value:j(f,h,h[0]||null),options:h,action:"initial-input-focus"},w=!l),"initial-input-focus"===(null===o||void 0===o?void 0:o.action)&&(b=null),C(C(C({},p),y),{},{prevProps:e,ariaSelection:b,prevWasFocused:w})}}]),n}(f.Component);ft.defaultProps=et;var ht=n(25621),pt=n(17715),vt=(0,f.forwardRef)((function(e,t){var n=function(e){var t=e.defaultInputValue,n=void 0===t?"":t,r=e.defaultMenuIsOpen,i=void 0!==r&&r,a=e.defaultValue,l=void 0===a?null:a,s=e.inputValue,c=e.menuIsOpen,d=e.onChange,h=e.onInputChange,p=e.onMenuClose,v=e.onMenuOpen,g=e.value,m=(0,o.Z)(e,we),y=(0,f.useState)(void 0!==s?s:n),b=(0,u.Z)(y,2),w=b[0],x=b[1],k=(0,f.useState)(void 0!==c?c:i),D=(0,u.Z)(k,2),S=D[0],E=D[1],F=(0,f.useState)(void 0!==g?g:l),M=(0,u.Z)(F,2),Z=M[0],A=M[1],R=(0,f.useCallback)((function(e,t){"function"===typeof d&&d(e,t),A(e)}),[d]),I=(0,f.useCallback)((function(e,t){var n;"function"===typeof h&&(n=h(e,t)),x(void 0!==n?n:e)}),[h]),T=(0,f.useCallback)((function(){"function"===typeof v&&v(),E(!0)}),[v]),O=(0,f.useCallback)((function(){"function"===typeof p&&p(),E(!1)}),[p]),P=void 0!==s?s:w,H=void 0!==c?c:S,L=void 0!==g?g:Z;return C(C({},m),{},{inputValue:P,menuIsOpen:H,onChange:R,onInputChange:I,onMenuClose:O,onMenuOpen:T,value:L})}(e);return f.createElement(ft,(0,h.Z)({ref:t},n))})),gt=(f.Component,vt),mt=["children"],yt=[[50,5],[61.23,39.55],[97.55,39.55],[68.16,60.9],[79.39,95.45],[50,74.1],[20.61,95.45],[31.84,60.9],[2.45,39.55],[38.77,39.55]];function bt(e,t,n){for(var r=!1,i=0,o=yt;i<o.length;i++){var a=o[i],l=(a[0]-50)*(n/100)+t[0],u=(a[1]-50)*(n/100)+t[1];r?e.lineTo(l,u):(e.moveTo(l,u),r=!0)}e.closePath()}var wt=function(){return(0,f.createElement)("svg",{width:"100",height:"100",viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,f.createElement)("path",{d:"M47.1468 13.7811C48.0449 11.0172 51.9551 11.0172 52.8532 13.7812L60.5522 37.4762C60.9538 38.7123 62.1056 39.5491 63.4053 39.5491H88.3198C91.226 39.5491 92.4343 43.268 90.0831 44.9762L69.9269 59.6205C68.8755 60.3845 68.4355 61.7386 68.8371 62.9746L76.5361 86.6697C77.4342 89.4336 74.2707 91.732 71.9196 90.0238L51.7634 75.3794C50.7119 74.6155 49.2881 74.6155 48.2366 75.3795L28.0804 90.0238C25.7293 91.732 22.5659 89.4336 23.4639 86.6697L31.1629 62.9746C31.5645 61.7386 31.1245 60.3845 30.0731 59.6205L9.91686 44.9762C7.56572 43.268 8.77405 39.5491 11.6802 39.5491H36.5947C37.8944 39.5491 39.0462 38.7123 39.4478 37.4762L47.1468 13.7811Z",fill:"currentColor"}))},xt=(0,c.d)("div")({name:"EditorWrap",class:"e17a8fyf"}),kt={kind:d.p6.Custom,isMatch:function(e){return"star-cell"===e.data.kind},needsHover:!0,draw:function(e,t){var n=e.ctx,r=e.theme,i=e.rect,o=e.hoverAmount,a=t.data.rating,l=r.cellHorizontalPadding,u=i.x+l,s=Math.min(5,Math.ceil(a));u+=8,n.beginPath();for(var c=0;c<s;c++)bt(n,[u,i.y+i.height/2],16),u+=18;return n.fillStyle=r.textDark,n.globalAlpha=.6+.4*o,n.fill(),n.globalAlpha=1,!0},provideEditor:function(){return function(e){return(0,f.createElement)(xt,null,[0,1,2,3,4].map((function(t){return(0,f.createElement)("div",{key:t,className:e.value.data.rating<t+1?"inactive":"active",onClick:function(){e.onChange((0,s.Z)((0,s.Z)({},e.value),{},{data:(0,s.Z)((0,s.Z)({},e.value.data),{},{rating:t+1})}))}},(0,f.createElement)(wt,null))})))}},onPaste:function(e,t){var n=Number.parseInt(e);return(0,s.Z)((0,s.Z)({},t),{},{rating:Number.isNaN(n)?0:n})}},Dt=kt,Ct={kind:d.p6.Custom,isMatch:function(e){return"sparkline-cell"===e.data.kind},needsHover:!0,needsHoverPosition:!0,draw:function(e,t){var n,r=e.ctx,i=e.theme,o=e.rect,a=e.hoverAmount,s=e.hoverX,c=t.data,f=c.values,h=c.yAxis,p=c.color,v=c.graphKind,g=void 0===v?"line":v,m=c.displayValues,y=(0,u.Z)(h,2),b=y[0],w=y[1];if(0===f.length)return!0;f=f.map((function(e){return Math.min(1,Math.max(0,(e-b)/(w-b)))}));var x=i.cellHorizontalPadding,k=x+o.x,D=o.y+3,C=o.height-6,S=o.width-2*x,E=w<=0?D:b>=0?D+C:D+C*(w/(w-b));if(b<=0&&w>=0&&(r.beginPath(),r.moveTo(k,E),r.lineTo(k+S,E),r.globalAlpha=.4,r.lineWidth=1,r.strokeStyle=i.textLight,r.stroke(),r.globalAlpha=1),"bar"===g){r.beginPath();var F,M=(S-2*(f.length-1))/f.length,Z=k,A=(0,l.Z)(f);try{for(A.s();!(F=A.n()).done;){var R=F.value,I=D+C-R*C;r.moveTo(Z,E),r.lineTo(Z+M,E),r.lineTo(Z+M,I),r.lineTo(Z,I),Z+=M+2}}catch(U){A.e(U)}finally{A.f()}r.fillStyle=null!=(n=t.data.color)?n:i.accentColor,r.fill()}else{1===f.length&&(f=[f[0],f[0]]),r.beginPath();var T,O=(o.width-16)/(f.length-1),P=f.map((function(e,t){return{x:k+O*t,y:D+C-e*C}}));for(r.moveTo(P[0].x,P[0].y),T=1;T<P.length-2;T++){var H=(P[T].x+P[T+1].x)/2,L=(P[T].y+P[T+1].y)/2;r.quadraticCurveTo(P[T].x,P[T].y,H,L)}r.quadraticCurveTo(P[T].x,P[T].y,P[T+1].x,P[T+1].y),r.strokeStyle=null!=p?p:i.accentColor,r.lineWidth=1+.5*a,r.stroke(),r.lineTo(o.x+o.width-x,E),r.lineTo(o.x+x,E),r.closePath(),r.globalAlpha=.2+.2*a;var z=r.createLinearGradient(0,D,0,D*****C);z.addColorStop(0,null!=p?p:i.accentColor);var B=(0,d.dF)(null!=p?p:i.accentColor),_=(0,u.Z)(B,3),V=_[0],N=_[1],W=_[2];if(z.addColorStop(1,"rgba(".concat(V,", ").concat(N,", ").concat(W,", 0)")),r.fillStyle=z,r.fill(),r.globalAlpha=1,void 0!==s&&"line"===g&&void 0!==m){r.beginPath();var j=Math.min(f.length-1,Math.max(0,Math.round((s-x)/O)));r.moveTo(k+j*O,o.y),r.lineTo(k+j*O,o.y+o.height),r.lineWidth=1,r.strokeStyle=i.textLight,r.stroke(),r.save(),r.font="8px ".concat(i.fontFamily),r.fillStyle=i.textMedium,r.textBaseline="top",r.fillText(m[j],k,o.y+i.cellVerticalPadding),r.restore()}}return!0},provideEditor:function(){},onPaste:function(e,t){return t}},St=Ct;function Et(e,t,n,r,i,o){0!==o?("number"===typeof o&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.min(o.tl,i/2,r/2),tr:Math.min(o.tr,i/2,r/2),bl:Math.min(o.bl,i/2,r/2),br:Math.min(o.br,i/2,r/2)},e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)):e.rect(t,n,r,i)}var Ft=20,Mt=(0,c.d)("div")({name:"EditorWrap",class:"e43amum",vars:{"e43amum-0":[function(e){return e.tagHeight/2},"px"],"e43amum-1":[function(e){return e.tagHeight},"px"],"e43amum-2":[function(e){return e.innerPad},"px"]}}),Zt={kind:d.p6.Custom,isMatch:function(e){return"tags-cell"===e.data.kind},draw:function(e,t){var n,r,i,o=e.ctx,a=e.theme,u=e.rect,s=t.data,c=s.possibleTags,f=s.tags,h={x:u.x+a.cellHorizontalPadding,y:u.y+a.cellVerticalPadding,width:u.width-2*a.cellHorizontalPadding,height:u.height-2*a.cellVerticalPadding},p=Math.max(1,Math.floor(h.height/26)),v=h.x,g=1,m=h.y+(h.height-p*Ft-6*(p-1))/2,y=(0,l.Z)(f);try{var b=function(){var e=i.value,t=null!=(r=null==(n=c.find((function(t){return t.tag===e})))?void 0:n.color)?r:a.bgBubble;o.font="12px ".concat(a.fontFamily);var l=(0,d.P7)(e,o).width+12;if(v!==h.x&&v+l>h.x+h.width&&g<p&&(g++,m+=26,v=h.x),o.fillStyle=t,o.beginPath(),Et(o,v,m,l,Ft,10),o.fill(),o.fillStyle=a.textDark,o.fillText(e,v+6,m+10+(0,d.aX)(o,"12px ".concat(a.fontFamily))),(v+=l+8)>h.x+h.width&&g>=p)return"break"};for(y.s();!(i=y.n()).done;){if("break"===b())break}}catch(w){y.e(w)}finally{y.f()}return!0},provideEditor:function(){return function(e){var t=e.onChange,n=e.value,r=n.data,i=r.possibleTags,o=r.tags,l=r.readonly,u=void 0!==l&&l;return(0,f.createElement)(Mt,{tagHeight:Ft,innerPad:6,className:u?"readonly":""},i.map((function(r){var i=-1!==o.indexOf(r.tag);return(0,f.createElement)("label",{key:r.tag},!u&&(0,f.createElement)("input",{className:"gdg-input",type:"checkbox",checked:i,onChange:function(){var l=i?o.filter((function(e){return e!==r.tag})):[].concat((0,a.Z)(o),[r.tag]);t((0,s.Z)((0,s.Z)({},e.value),{},{data:(0,s.Z)((0,s.Z)({},n.data),{},{tags:l})}))}}),(0,f.createElement)("div",{className:"pill "+(i?"selected":"unselected"),style:{backgroundColor:i?r.color:void 0}},r.tag))})))}},onPaste:function(e,t){return(0,s.Z)((0,s.Z)({},t),{},{tags:t.possibleTags.map((function(e){return e.tag})).filter((function(t){return e.split(",").map((function(e){return e.trim()})).includes(t)}))})}},At={kind:d.p6.Custom,isMatch:function(e){return"user-profile-cell"===e.data.kind},draw:function(e,t){var n=e.ctx,r=e.rect,i=e.theme,o=e.imageLoader,a=e.col,l=e.row,u=t.data,s=u.image,c=u.name,f=u.initial,h=u.tint,p=i.cellHorizontalPadding,v=Math.min(12,r.height/2-i.cellVerticalPadding),g=r.x+p,m=o.loadOrGetImage(s,a,l);n.save(),n.beginPath(),n.arc(g+v,r.y+r.height/2,v,0,2*Math.PI),n.globalAlpha=.2,n.fillStyle=h,n.fill(),n.globalAlpha=1,n.font="600 16px ".concat(i.fontFamily);var y=(0,d.P7)(f[0],n);return n.fillText(f[0],g+v-y.width/2,r.y+r.height/2+(0,d.aX)(n,"600 16px ".concat(i.fontFamily))),void 0!==m&&(n.save(),n.beginPath(),n.arc(g+v,r.y+r.height/2,v,0,2*Math.PI),n.clip(),n.drawImage(m,g,r.y+r.height/2-v,2*v,2*v),n.restore()),void 0!==c&&(n.font="".concat(i.baseFontStyle," ").concat(i.fontFamily),n.fillStyle=i.textDark,n.fillText(c,g+2*v+p,r.y+r.height/2+(0,d.aX)(n,i))),n.restore(),!0},provideEditor:function(){return function(e){var t,n=e.isHighlighted,r=e.onChange,i=e.value;return(0,f.createElement)(d.t5,{highlight:n,autoFocus:!0,value:null!=(t=i.data.name)?t:"",onChange:function(e){return r((0,s.Z)((0,s.Z)({},i),{},{data:(0,s.Z)((0,s.Z)({},i.data),{},{name:e.target.value})}))}})}},onPaste:function(e,t){return(0,s.Z)((0,s.Z)({},t),{},{name:e})}},Rt=function(e){var t=be.Menu,n=e.children,r=(0,o.Z)(e,mt);return(0,f.createElement)(t,(0,s.Z)({},r),n)},It=(0,c.d)("div")({name:"Wrap",class:"w13j932a"}),Tt=(0,c.d)("div")({name:"PortalWrap",class:"p19663q2"}),Ot=(0,c.d)("div")({name:"ReadOnlyWrap",class:"r1jyvvws"}),Pt=function(e){var t=e.value,n=e.onFinishedEditing,o=e.initialValue,a=t.data,l=a.allowedValues,c=a.value,h=(0,f.useState)(c),p=(0,u.Z)(h,2),v=p[0],g=p[1],m=(0,f.useState)(null!=o?o:""),y=(0,u.Z)(m,2),b=y[0],w=y[1],x=(0,d.Fg)(),k=(0,f.useMemo)((function(){return l.map((function(e){return{value:e,label:e}}))}),[l]);return t.readonly?(0,f.createElement)(Ot,null,(0,f.createElement)(d.t5,{highlight:!0,autoFocus:!1,disabled:!0,value:null!=v?v:"",onChange:function(){}})):(0,f.createElement)(It,null,(0,f.createElement)(gt,{className:"glide-select",inputValue:b,onInputChange:w,menuPlacement:"auto",value:k.find((function(e){return e.value===v})),styles:{control:function(e){return(0,s.Z)((0,s.Z)({},e),{},{border:0,boxShadow:"none"})},option:function(e){return(0,s.Z)((0,s.Z)({},e),{},{fontSize:x.editorFontSize,fontFamily:x.fontFamily,":empty::after":{content:'"&nbsp;"',visibility:"hidden"}})}},theme:function(e){return(0,s.Z)((0,s.Z)({},e),{},{colors:(0,s.Z)((0,s.Z)({},e.colors),{},{neutral0:x.bgCell,neutral5:x.bgCell,neutral10:x.bgCell,neutral20:x.bgCellMedium,neutral30:x.bgCellMedium,neutral40:x.bgCellMedium,neutral50:x.textLight,neutral60:x.textMedium,neutral70:x.textMedium,neutral80:x.textDark,neutral90:x.textDark,neutral100:x.textDark,primary:x.accentColor,primary75:x.accentColor,primary50:x.accentColor,primary25:x.accentLight})})},menuPortalTarget:document.getElementById("portal"),autoFocus:!0,openMenuOnFocus:!0,components:{DropdownIndicator:function(){return null},IndicatorSeparator:function(){return null},Menu:function(e){return(0,f.createElement)(Tt,null,(0,f.createElement)(Rt,(0,s.Z)({className:"click-outside-ignore"},e)))}},options:k,onChange:function(){var e=(0,i.Z)((0,r.Z)().mark((function e(i){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!==i){e.next=2;break}return e.abrupt("return");case 2:return g(i.value),e.next=5,new Promise((function(e){return window.requestAnimationFrame(e)}));case 5:n((0,s.Z)((0,s.Z)({},t),{},{data:(0,s.Z)((0,s.Z)({},t.data),{},{value:i.value})}));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}))},Ht={kind:d.p6.Custom,isMatch:function(e){return"dropdown-cell"===e.data.kind},draw:function(e,t){var n=e.ctx,r=e.theme,i=e.rect,o=t.data.value;return o&&(n.fillStyle=r.textDark,n.fillText(o,i.x+r.cellHorizontalPadding,i.y+i.height/2+(0,d.aX)(n,r))),!0},measure:function(e,t){var n=t.data.value;return n?e.measureText(n).width+16:16},provideEditor:function(){return{editor:Pt,disablePadding:!0,deletedValue:function(e){return(0,s.Z)((0,s.Z)({},e),{},{copyData:"",data:(0,s.Z)((0,s.Z)({},e.data),{},{value:""})})}}},onPaste:function(e,t){return(0,s.Z)((0,s.Z)({},t),{},{value:t.allowedValues.includes(e)?e:t.value})}},Lt=(0,f.lazy)((0,i.Z)((0,r.Z)().mark((function e(){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.e(5733).then(n.bind(n,45733));case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))),zt={kind:d.p6.Custom,isMatch:function(e){return"article-cell"===e.data.kind},draw:function(e,t){var n=e.ctx,r=e.theme,i=e.rect,o=t.data.markdown;o.includes("\n")&&(o=o.split(/\r?\n/)[0]);var a=i.width/4;return o.length>a&&(o=o.slice(0,a)),n.fillStyle=r.textDark,n.fillText(o,i.x+r.cellHorizontalPadding,i.y+i.height/2+(0,d.aX)(n,r)),!0},provideEditor:function(){return{editor:function(e){return(0,f.createElement)(f.Suspense,{fallback:null},(0,f.createElement)(Lt,(0,s.Z)({},e)))},styleOverride:{position:"fixed",left:"12.5vw",top:"12.5vh",width:"75vw",borderRadius:"9px",maxWidth:"unset",maxHeight:"unset"},disablePadding:!0}},onPaste:function(e,t){return(0,s.Z)((0,s.Z)({},t),{},{markdown:e})}},Bt={marginRight:8},_t={display:"flex",alignItems:"center",flexGrow:1},Vt={kind:d.p6.Custom,isMatch:function(e){return"range-cell"===e.data.kind},draw:function(e,t){var n=e.ctx,r=e.theme,i=e.rect,o=t.data,a=o.min,l=o.max,u=o.value,s=o.label,c=o.measureLabel,f=i.x+r.cellHorizontalPadding,h=i.y+i.height/2,p=(u-a)/(l-a);n.save();var v=0;void 0!==s&&(n.font="12px ".concat(r.fontFamily),v=(0,d.P7)(null!=c?c:s,n,"12px ".concat(r.fontFamily)).width+r.cellHorizontalPadding);var g=i.width-2*r.cellHorizontalPadding-v;if(g>=6){var m=n.createLinearGradient(f,h,f+g,h);m.addColorStop(0,r.accentColor),m.addColorStop(p,r.accentColor),m.addColorStop(p,r.bgBubble),m.addColorStop(1,r.bgBubble),n.beginPath(),n.fillStyle=m,Et(n,f,h-3,g,6,3),n.fill(),n.beginPath(),Et(n,f+.5,h-3+.5,g-1,5,2.5),n.strokeStyle=r.accentLight,n.lineWidth=1,n.stroke()}return void 0!==s&&(n.textAlign="right",n.fillStyle=r.textDark,n.fillText(s,i.x+i.width-r.cellHorizontalPadding,h+(0,d.aX)(n,"12px ".concat(r.fontFamily)))),n.restore(),!0},provideEditor:function(){return function(e){var t=e.value.data,n=t.value.toString(),r=t.min.toString(),i=t.max.toString(),o=t.step.toString();return(0,f.createElement)("label",{style:_t},(0,f.createElement)("input",{style:Bt,type:"range",value:n,min:r,max:i,step:o,onChange:function(n){e.onChange((0,s.Z)((0,s.Z)({},e.value),{},{data:(0,s.Z)((0,s.Z)({},t),{},{value:Number(n.target.value)})}))}}),n)}},onPaste:function(e,t){var n=Number.parseFloat(e);return n=Number.isNaN(n)?t.value:Math.max(t.min,Math.min(t.max,n)),(0,s.Z)((0,s.Z)({},t),{},{value:n})}},Nt={kind:d.p6.Custom,isMatch:function(e){return"spinner-cell"===e.data.kind},draw:function(e){var t=e.ctx,n=e.theme,r=e.rect,i=e.requestAnimationFrame,o=window.performance.now()%1e3/1e3,a=r.x+r.width/2,l=r.y+r.height/2;return t.arc(a,l,Math.min(12,r.height/6),2*Math.PI*o,2*Math.PI*o*****Math.PI),t.strokeStyle=n.textMedium,t.lineWidth=1.5,t.stroke(),t.lineWidth=1,i(),!0},provideEditor:function(){}},Wt=(0,c.d)("input")({name:"StyledInputBox",class:"s1sdc9r3"}),jt=function(e,t){if(void 0===t||null===t)return"";var n=t.toISOString();switch(e){case"date":return n.split("T")[0];case"datetime-local":return n.replace("Z","");case"time":return n.split("T")[1].replace("Z","");default:throw new Error("Unknown date kind ".concat(e))}},Ut=function(e){var t=e.value.data,n=t.format,r=t.displayDate,i=void 0===t.step||Number.isNaN(Number(t.step))?void 0:Number(t.step),o=t.min instanceof Date?jt(n,t.min):t.min,a=t.max instanceof Date?jt(n,t.max):t.max,l=t.date,u=t.timezoneOffset?60*t.timezoneOffset*1e3:0;u&&l&&(l=new Date(l.getTime()+u));var c=jt(n,l);return e.value.readonly?f.createElement(d.t5,{highlight:!0,autoFocus:!1,disabled:!0,value:null!=r?r:"",onChange:function(){}}):f.createElement(Wt,{"data-testid":"date-picker-cell",required:!0,type:n,defaultValue:c,min:o,max:a,step:i,autoFocus:!0,onChange:function(t){isNaN(t.target.valueAsNumber)?e.onChange((0,s.Z)((0,s.Z)({},e.value),{},{data:(0,s.Z)((0,s.Z)({},e.value.data),{},{date:void 0})})):e.onChange((0,s.Z)((0,s.Z)({},e.value),{},{data:(0,s.Z)((0,s.Z)({},e.value.data),{},{date:new Date(t.target.valueAsNumber-u)})}))}})},Xt={kind:d.p6.Custom,isMatch:function(e){return"date-picker-cell"===e.data.kind},draw:function(e,t){var n=t.data.displayDate;return(0,d.uN)(e,n,t.contentAlign),!0},measure:function(e,t){var n=t.data.displayDate;return e.measureText(n).width+16},provideEditor:function(){return{editor:Ut}},onPaste:function(e,t){var n=NaN;return e&&(n=Number(e).valueOf(),Number.isNaN(n)&&(n=Date.parse(e),"time"===t.format&&Number.isNaN(n)&&(n=Date.parse("1970-01-01T".concat(e,"Z"))))),(0,s.Z)((0,s.Z)({},t),{},{date:Number.isNaN(n)?void 0:new Date(n)})}};function Yt(e){if("click"!==e.cell.data.navigateOn===e.ctrlKey){var t=document.createElement("canvas").getContext("2d",{alpha:!1});if(null!==t){var n=e.posX,r=e.bounds,i=e.cell,o=e.theme,a="".concat(o.baseFontStyle," ").concat(o.fontFamily);t.font=a;var s,c=i.data.links,f=o.cellHorizontalPadding,h=r.x+f,p=r.x+n,v=(0,l.Z)(c.entries());try{for(v.s();!(s=v.n()).done;){var g=(0,u.Z)(s.value,2),m=g[0],y=g[1],b=m<c.length-1,w=(0,d.P7)(y.title,t),x=b?(0,d.P7)(y.title+",",t,a):w;if(p>h&&p<h+w.width)return y;h+=x.width+4}}catch(k){v.e(k)}finally{v.f()}}}}var Kt={kind:d.p6.Custom,needsHover:!0,needsHoverPosition:!0,isMatch:function(e){return"links-cell"===e.data.kind},onSelect:function(e){void 0!==Yt(e)&&e.preventDefault()},onClick:function(e){var t,n=Yt(e);void 0!==n&&(null==(t=n.onClick)||t.call(n),e.preventDefault())},draw:function(e,t){var n,r=e.ctx,i=e.rect,o=e.theme,a=e.hoverX,s=void 0===a?-100:a,c=e.highlighted,f=t.data,h=f.links,p=f.underlineOffset,v=void 0===p?5:p,g=o.cellHorizontalPadding,m=i.x+g,y=i.x+s,b="".concat(o.baseFontStyle," ").concat(o.fontFamily),w=(0,d.aX)(r,b),x=i.y+i.height/2+w,k=(0,l.Z)(h.entries());try{for(k.s();!(n=k.n()).done;){var D=(0,u.Z)(n.value,2),C=D[0],S=D[1],E=C<h.length-1,F=(0,d.P7)(S.title,r,b),M=E?(0,d.P7)(S.title+",",r,b):F;y>m&&y<m+F.width&&(r.moveTo(m,Math.floor(x+v)+.5),r.lineTo(m+F.width,Math.floor(x+v)+.5),r.strokeStyle=o.textDark,r.stroke(),r.fillStyle=c?(0,d.NH)(o.accentLight,o.bgCell):o.bgCell,r.fillText(E?S.title+",":S.title,m-1,x),r.fillText(E?S.title+",":S.title,m+1,x),r.fillText(E?S.title+",":S.title,m-2,x),r.fillText(E?S.title+",":S.title,m+2,x)),r.fillStyle=o.textDark,r.fillText(E?S.title+",":S.title,m,x),m+=M.width+4}}catch(Z){k.e(Z)}finally{k.f()}return!0},provideEditor:function(){return function(e){var t=e.value,n=e.onChange,r=t.data,i=r.links,o=r.maxLinks,l=void 0===o?Number.MAX_SAFE_INTEGER:o;return(0,f.createElement)($t,{onKeyDown:Gt},i.map((function(e,r){var o;return(0,f.createElement)(qt,{key:r,link:null!=(o=e.href)?o:"",title:e.title,focus:0===r,onDelete:i.length>1?function(){var e=(0,a.Z)(i);e.splice(r,1),n((0,s.Z)((0,s.Z)({},t),{},{data:(0,s.Z)((0,s.Z)({},t.data),{},{links:e})}))}:void 0,onChange:function(e,o){var l=(0,a.Z)(i);l[r]={href:e,title:o},n((0,s.Z)((0,s.Z)({},t),{},{data:(0,s.Z)((0,s.Z)({},t.data),{},{links:l})}))}})})),(0,f.createElement)("button",{disabled:i.length>=l,className:"add-link",onClick:function(){var e=[].concat((0,a.Z)(i),[{title:""}]);n((0,s.Z)((0,s.Z)({},t),{},{data:(0,s.Z)((0,s.Z)({},t.data),{},{links:e})}))}},"Add link"))}},onPaste:function(e,t){var n=e.split(",");if(!t.links.some((function(e,t){return n[t]!==e.title})))return(0,s.Z)((0,s.Z)({},t),{},{links:n.map((function(e){return{title:e}}))})}},$t=(0,c.d)("div")({name:"LinksCellEditorStyle",class:"lneeve5"});function Gt(e){"Tab"===e.key&&e.stopPropagation()}var qt=function(e){var t=e.link,n=e.onChange,r=e.title,i=e.onDelete,o=e.focus;return(0,f.createElement)("div",{className:"gdg-link-title-editor"},(0,f.createElement)("input",{className:"gdg-title-input",value:r,placeholder:"Title",autoFocus:o,onChange:function(e){n(t,e.target.value)}}),(0,f.createElement)("input",{className:"gdg-link-input",value:t,placeholder:"URL",onChange:function(e){n(e.target.value,r)}}),void 0!==i&&(0,f.createElement)("button",{onClick:i},(0,f.createElement)("svg",{width:16,height:16,viewBox:"0 0 24 24",fill:"none",id:"icon-import",xmlns:"http://www.w3.org/2000/svg"},(0,f.createElement)("path",{d:"M3 6L5 6L21 6",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}),(0,f.createElement)("path",{d:"M17.9019 6C18.491 6 18.9525 6.50676 18.8975 7.09334L17.67 20.1867C17.5736 21.2144 16.711 22 15.6787 22H8.32127C7.28902 22 6.42635 21.2144 6.33 20.1867L5.1025 7.09334C5.04751 6.50676 5.50898 6 6.09813 6H17.9019Z",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}),(0,f.createElement)("path",{d:"M14.4499 10.211L13.9949 17",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}),(0,f.createElement)("path",{d:"M9.55499 10.211L10.0049 17",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}),(0,f.createElement)("path",{d:"M7.5 2.25H16.5",stroke:"currentColor",strokeWidth:"1px",strokeLinecap:"round",strokeLinejoin:"round"}))))},Qt=Kt;function Jt(e,t,n){if("string"===typeof e)return void 0!==t[e]?t[e]:e;var r=(0,u.Z)(e,2),i=r[0],o=r[1];return void 0!==t[i]&&(i=t[i]),void 0!==t[o]&&(o=t[o]),(0,d.Nz)(i,o,n)}var en={kind:d.p6.Custom,isMatch:function(e){return"button-cell"===e.data.kind},needsHover:!0,onSelect:function(e){return e.preventDefault()},onClick:function(e){var t,n;null==(n=(t=e.cell.data).onClick)||n.call(t)},drawPrep:function(e){return e.ctx.textAlign="center",{deprep:function(e){e.ctx.textAlign="start"}}},draw:function(e,t){var n=e.ctx,r=e.theme,i=e.rect,o=e.hoverAmount,a=t.data,l=a.title,u=a.backgroundColor,s=a.color,c=a.borderColor,f=a.borderRadius,h=Math.floor(i.x+r.cellHorizontalPadding+1),p=Math.floor(i.y+r.cellVerticalPadding+1),v=Math.ceil(i.width-2*r.cellHorizontalPadding-1),g=Math.ceil(i.height-2*r.cellVerticalPadding-1);return void 0!==u&&(n.beginPath(),Et(n,h,p,v,g,null!=f?f:0),n.fillStyle=Jt(u,r,o),n.fill()),void 0!==c&&(n.beginPath(),Et(n,h+.5,p+.5,v-1,g-1,null!=f?f:0),n.strokeStyle=Jt(c,r,o),n.lineWidth=1,n.stroke()),n.fillStyle=Jt(null!=s?s:r.accentColor,r,o),n.fillText(l,h+v/2,p+g/2+(0,d.aX)(n,"".concat(r.baseFontStyle," ").concat(r.fontFamily))),!0},provideEditor:void 0},tn=[Dt,St,Zt,At,Ht,zt,Nt,Vt,Xt,Qt,en];function nn(){return(0,d.R$)(tn)}},37753:function(e,t,n){"use strict";n.d(t,{fF:function(){return s}});var r=n(11026),i=n(35396),o=n(7974),a=n(66845);function l(e){var t,n,r,o,a;switch(e.kind){case i.p6.Number:return null!==(t=null===(n=e.data)||void 0===n?void 0:n.toString())&&void 0!==t?t:"";case i.p6.Boolean:return null!==(r=null===(o=e.data)||void 0===o?void 0:o.toString())&&void 0!==r?r:"";case i.p6.Markdown:case i.p6.RowID:case i.p6.Text:case i.p6.Uri:return null!==(a=e.data)&&void 0!==a?a:"";case i.p6.Bubble:case i.p6.Image:return e.data.join("");case i.p6.Drilldown:return e.data.map((function(e){return e.text})).join("");case i.p6.Protected:case i.p6.Loading:return"";case i.p6.Custom:return e.copyData}}function u(e){if("number"===typeof e)return e;if(e.length>0){var t=Number(e);isNaN(t)||(e=t)}return e}function s(e){var t,n=e.sort,i=e.rows,s=e.getCellContent,c=void 0===n?void 0:e.columns.findIndex((function(e){return n.column===e||void 0!==e.id&&n.column.id===e.id}));-1===c&&(c=void 0);var d=null!==(t=null===n||void 0===n?void 0:n.direction)&&void 0!==t?t:"asc",f=a.useMemo((function(){if(void 0!==c){for(var e,t=new Array(i),r=[c,0],a=0;a<i;a++)r[1]=a,t[a]=l(s(r));return e="raw"===(null===n||void 0===n?void 0:n.mode)?o(i).sort((function(e,n){return function(e,t){return e>t?1:e===t?0:-1}(t[e],t[n])})):"smart"===(null===n||void 0===n?void 0:n.mode)?o(i).sort((function(e,n){return function(e,t){return e=u(e),t=u(t),"string"===typeof e&&"string"===typeof t?e.localeCompare(t):"number"===typeof e&&"number"===typeof t?e===t?0:e>t?1:-1:e==t?0:e>t?1:-1}(t[e],t[n])})):o(i).sort((function(e,n){return t[e].localeCompare(t[n])})),"desc"===d&&e.reverse(),e}}),[s,i,null===n||void 0===n?void 0:n.mode,d,c]),h=a.useCallback((function(e){return void 0===f?e:f[e]}),[f]),p=a.useCallback((function(e){var t=(0,r.Z)(e,2),n=t[0],i=t[1];return void 0===f||(i=f[i]),s([n,i])}),[s,f]);return void 0===f?{getCellContent:e.getCellContent,getOriginalIndex:h}:{getOriginalIndex:h,getCellContent:p}}},47920:function(e,t,n){"use strict";n.d(t,{d:function(){return d}});var r=n(11026),i=n(66845);var o=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|inert|itemProp|itemScope|itemType|itemID|itemRef|on|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,a=function(e){var t={};return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}((function(e){return o.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),l=function(){var e=Array.prototype.slice.call(arguments).filter(Boolean),t={},n=[];e.forEach((function(e){(e?e.split(" "):[]).forEach((function(e){if(e.startsWith("atm_")){var i=e.split("_"),o=(0,r.Z)(i,2)[1];t[o]=e}else n.push(e)}))}));var i=[];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&i.push(t[o]);return i.push.apply(i,n),i.join(" ")},u=function(e){return e.toUpperCase()===e},s=function(e,t){var n={};return Object.keys(e).filter(function(e){return function(t){return-1===e.indexOf(t)}}(t)).forEach((function(t){n[t]=e[t]})),n};var c=function(e,t){if(!("string"===typeof e||"number"===typeof e&&isFinite(e))){var n="object"===typeof e?JSON.stringify(e):String(e);console.warn("An interpolation evaluated to '".concat(n,"' in the component '").concat(t,"', which is probably a mistake. You should explicitly cast or transform the value to a string."))}};var d=new Proxy((function(e){return function(t){if(Array.isArray(t))throw new Error('Using the "styled" tag in runtime is not supported. Make sure you have set up the Babel plugin correctly. See https://github.com/callstack/linaria#setup');var n=function(n,r){var o=n.as,d=void 0===o?e:o,f=n.class,h=function(e,t,n){var r=s(t,n);return"string"!==typeof e||-1!==e.indexOf("-")||u(e[0])||Object.keys(r).forEach((function(e){a(e)||delete r[e]})),r}(d,n,["as","class"]);h.ref=r,h.className=t.atomic?l(t.class,h.className||f):l(h.className||f,t.class);var p=t.vars;if(p){var v={};for(var g in p){var m=p[g],y=m[0],b=m[1]||"",w="function"===typeof y?y(n):y;c(w,t.name),v["--".concat(g)]="".concat(w).concat(b)}var x=h.style||{},k=Object.keys(x);k.length>0&&k.forEach((function(e){v[e]=x[e]})),h.style=v}return e.__linaria&&e!==d?(h.as=d,i.createElement(e,h)):i.createElement(d,h)},r=i.forwardRef?i.forwardRef(n):function(e){var t=s(e,["innerRef"]);return n(t,e.innerRef)};return r.displayName=t.name,r.__linaria={className:t.class,extends:e},r}}),{get:function(e,t){return e(t)}})},35396:function(e,t,n){"use strict";n.d(t,{EV:function(){return it},Nd:function(){return Qr},p6:function(){return Te},t5:function(){return Ir},NH:function(){return fn},uN:function(){return Yt},aX:function(){return Wt},Nz:function(){return hn},P7:function(){return Nt},dF:function(){return cn},R$:function(){return ei},Fg:function(){return Ie}});var r=n(64649),i=n(11092),o=n(27791),a=n(60726),l=n(649),u=n(11026),s=n(50189),c=n(53782),d=n(22951),f=n(91976),h=n(67591),p=n(94337),v=n(47920),g=n(66845),m=n(51586),y=n(17015),b=n(86995),w=n(7974),x=n(56797),k=n(17664),D=n(82781),C=n(19266),S=new Map,E=new Map,F=new Map;function M(e,t,n,r){var i,o,l=E.get(n);if(r&&void 0!==l&&l.count>2e4){var u=F.get(n);if(void 0===u&&(u=function(e,t){var n,r,i=new Map,o=0,l=(0,a.Z)("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890,.-+=?");try{for(l.s();!(r=l.n()).done;){var u=r.value,s=e.measureText(u).width;i.set(u,s),o+=s}}catch(g){l.e(g)}finally{l.f()}var c,d=o/i.size,f=(t/d+3)/4,h=i.keys(),p=(0,a.Z)(h);try{for(p.s();!(c=p.n()).done;){var v=c.value;i.set(v,(null!=(n=i.get(v))?n:d)*f)}}catch(g){p.e(g)}finally{p.f()}return i}(e,l.size),F.set(n,u)),l.count>5e5){var s,c=0,d=(0,a.Z)(t);try{for(d.s();!(s=d.n()).done;){var f=s.value;c+=null!=(i=u.get(f))?i:l.size}}catch(b){d.e(b)}finally{d.f()}return 1.01*c}var h=e.measureText(t);return function(e,t,n,r,i){var o,l,u,s,c=0,d={},f=(0,a.Z)(e);try{for(f.s();!(s=f.n()).done;){var h=s.value;c+=null!=(o=n.get(h))?o:i,d[h]=(null!=(l=d[h])?l:0)+1}}catch(b){f.e(b)}finally{f.f()}for(var p=t-c,v=0,g=Object.keys(d);v<g.length;v++){var m=g[v],y=d[m],w=null!=(u=n.get(m))?u:i,x=w+p*(w*y/c)*r/y;n.set(m,x)}}(t,h.width,u,Math.max(.05,1-l.count/2e5),l.size),E.set(n,{count:l.count+t.length,size:l.size}),h.width}var p=e.measureText(t),v=p.width/t.length;if((null!=(o=null==l?void 0:l.count)?o:0)>2e4)return p.width;if(void 0===l)E.set(n,{count:t.length,size:v});else{var g=v-l.size,m=t.length/(l.count+t.length),y=l.size+g*m;E.set(n,{count:l.count+t.length,size:y})}return p.width}function Z(e,t,n,r,i,o,l,u){if(t.length<=1)return t.length;if(i<n)return-1;var s=Math.floor(n/i*o),c=M(e,t.slice(0,Math.max(0,s)),r,l),d=null==u?void 0:u(t);if(c!==n)if(c<n){for(;c<n;)s++,c=M(e,t.slice(0,Math.max(0,s)),r,l);s--}else for(;c>n;){var f=void 0!==d?0:t.lastIndexOf(" ",s-1);f>0?s=f:s--,c=M(e,t.slice(0,Math.max(0,s)),r,l)}if(" "!==t[s]){var h=0;if(void 0===d)h=t.lastIndexOf(" ",s);else{var p,v=(0,a.Z)(d);try{for(v.s();!(p=v.n()).done;){var g=p.value;if(g>s)break;h=g}}catch(m){v.e(m)}finally{v.f()}}h>0&&(s=h)}return s}var A=n(76236),R=n(44303);function I(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}var T={async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1};var O=/[&<>"']/,P=new RegExp(O.source,"g"),H=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,L=new RegExp(H.source,"g"),z={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},B=function(e){return z[e]};function _(e,t){if(t){if(O.test(e))return e.replace(P,B)}else if(H.test(e))return e.replace(L,B);return e}var V=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function N(e){return e.replace(V,(function(e,t){return"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""}))}var W=/(^|[^\[])\^/g;function j(e,t){e="string"===typeof e?e:e.source,t=t||"";var n={replace:function(t,r){return r=(r=r.source||r).replace(W,"$1"),e=e.replace(t,r),n},getRegex:function(){return new RegExp(e,t)}};return n}var U=/[^\w:]/g,X=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function Y(e,t,n){if(e){var r;try{r=decodeURIComponent(N(n)).replace(U,"").toLowerCase()}catch(i){return null}if(0===r.indexOf("javascript:")||0===r.indexOf("vbscript:")||0===r.indexOf("data:"))return null}t&&!X.test(n)&&(n=function(e,t){K[" "+e]||($.test(e)?K[" "+e]=e+"/":K[" "+e]=ee(e,"/",!0));e=K[" "+e];var n=-1===e.indexOf(":");return"//"===t.substring(0,2)?n?t:e.replace(G,"$1")+t:"/"===t.charAt(0)?n?t:e.replace(q,"$1")+t:e+t}(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch(i){return null}return n}var K={},$=/^[^:]+:\/*[^/]*$/,G=/^([^:]+:)[\s\S]*$/,q=/^([^:]+:\/*[^/]*)[\s\S]*$/;var Q={exec:function(){}};function J(e,t){var n=e.replace(/\|/g,(function(e,t,n){for(var r=!1,i=t;--i>=0&&"\\"===n[i];)r=!r;return r?"|":" |"})).split(/ \|/),r=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function ee(e,t,n){var r=e.length;if(0===r)return"";for(var i=0;i<r;){var o=e.charAt(r-i-1);if(o!==t||n){if(o===t||!n)break;i++}else i++}return e.slice(0,r-i)}function te(e,t){if(t<1)return"";for(var n="";t>1;)1&t&&(n+=e),t>>=1,e+=e;return n+e}function ne(e,t,n,r){var i=t.href,o=t.title?_(t.title):null,a=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){r.state.inLink=!0;var l={type:"link",raw:n,href:i,title:o,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,l}return{type:"image",raw:n,href:i,title:o,text:_(a)}}var re=function(){function e(t){(0,d.Z)(this,e),this.options=t||T}return(0,f.Z)(e,[{key:"space",value:function(e){var t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}},{key:"code",value:function(e){var t=this.rules.block.code.exec(e);if(t){var n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:ee(n,"\n")}}}},{key:"fences",value:function(e){var t=this.rules.block.fences.exec(e);if(t){var n=t[0],r=function(e,t){var n=e.match(/^(\s+)(?:```)/);if(null===n)return t;var r=n[1];return t.split("\n").map((function(e){var t=e.match(/^\s+/);return null===t?e:(0,u.Z)(t,1)[0].length>=r.length?e.slice(r.length):e})).join("\n")}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline._escapes,"$1"):t[2],text:r}}}},{key:"heading",value:function(e){var t=this.rules.block.heading.exec(e);if(t){var n=t[2].trim();if(/#$/.test(n)){var r=ee(n,"#");this.options.pedantic?n=r.trim():r&&!/ $/.test(r)||(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}},{key:"hr",value:function(e){var t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}},{key:"blockquote",value:function(e){var t=this.rules.block.blockquote.exec(e);if(t){var n=t[0].replace(/^ *>[ \t]?/gm,""),r=this.lexer.state.top;this.lexer.state.top=!0;var i=this.lexer.blockTokens(n);return this.lexer.state.top=r,{type:"blockquote",raw:t[0],tokens:i,text:n}}}},{key:"list",value:function(e){var t=this.rules.block.list.exec(e);if(t){var n,r,i,o,a,l,u,s,c,d,f,h,p=t[1].trim(),v=p.length>1,g={type:"list",raw:"",ordered:v,start:v?+p.slice(0,-1):"",loose:!1,items:[]};p=v?"\\d{1,9}\\".concat(p.slice(-1)):"\\".concat(p),this.options.pedantic&&(p=v?p:"[*+-]");for(var m=new RegExp("^( {0,3}".concat(p,")((?:[\t ][^\\n]*)?(?:\\n|$))"));e&&(h=!1,t=m.exec(e))&&!this.rules.block.hr.test(e);){if(n=t[0],e=e.substring(n.length),s=t[2].split("\n",1)[0].replace(/^\t+/,(function(e){return" ".repeat(3*e.length)})),c=e.split("\n",1)[0],this.options.pedantic?(o=2,f=s.trimLeft()):(o=(o=t[2].search(/[^ ]/))>4?1:o,f=s.slice(o),o+=t[1].length),l=!1,!s&&/^ *$/.test(c)&&(n+=c+"\n",e=e.substring(c.length+1),h=!0),!h)for(var y=new RegExp("^ {0,".concat(Math.min(3,o-1),"}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))")),b=new RegExp("^ {0,".concat(Math.min(3,o-1),"}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)")),w=new RegExp("^ {0,".concat(Math.min(3,o-1),"}(?:```|~~~)")),x=new RegExp("^ {0,".concat(Math.min(3,o-1),"}#"));e&&(c=d=e.split("\n",1)[0],this.options.pedantic&&(c=c.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!w.test(c))&&!x.test(c)&&!y.test(c)&&!b.test(e);){if(c.search(/[^ ]/)>=o||!c.trim())f+="\n"+c.slice(o);else{if(l)break;if(s.search(/[^ ]/)>=4)break;if(w.test(s))break;if(x.test(s))break;if(b.test(s))break;f+="\n"+c}l||c.trim()||(l=!0),n+=d+"\n",e=e.substring(d.length+1),s=c.slice(o)}g.loose||(u?g.loose=!0:/\n *\n *$/.test(n)&&(u=!0)),this.options.gfm&&(r=/^\[[ xX]\] /.exec(f))&&(i="[ ] "!==r[0],f=f.replace(/^\[[ xX]\] +/,"")),g.items.push({type:"list_item",raw:n,task:!!r,checked:i,loose:!1,text:f}),g.raw+=n}g.items[g.items.length-1].raw=n.trimRight(),g.items[g.items.length-1].text=f.trimRight(),g.raw=g.raw.trimRight();var k=g.items.length;for(a=0;a<k;a++)if(this.lexer.state.top=!1,g.items[a].tokens=this.lexer.blockTokens(g.items[a].text,[]),!g.loose){var D=g.items[a].tokens.filter((function(e){return"space"===e.type})),C=D.length>0&&D.some((function(e){return/\n.*\n/.test(e.raw)}));g.loose=C}if(g.loose)for(a=0;a<k;a++)g.items[a].loose=!0;return g}}},{key:"html",value:function(e){var t=this.rules.block.html.exec(e);if(t){var n={type:"html",raw:t[0],pre:!this.options.sanitizer&&("pre"===t[1]||"script"===t[1]||"style"===t[1]),text:t[0]};if(this.options.sanitize){var r=this.options.sanitizer?this.options.sanitizer(t[0]):_(t[0]);n.type="paragraph",n.text=r,n.tokens=this.lexer.inline(r)}return n}}},{key:"def",value:function(e){var t=this.rules.block.def.exec(e);if(t){var n=t[1].toLowerCase().replace(/\s+/g," "),r=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline._escapes,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:i}}}},{key:"table",value:function(e){var t=this.rules.block.table.exec(e);if(t){var n={type:"table",header:J(t[1]).map((function(e){return{text:e}})),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(n.header.length===n.align.length){n.raw=t[0];var r,i,o,a,l=n.align.length;for(r=0;r<l;r++)/^ *-+: *$/.test(n.align[r])?n.align[r]="right":/^ *:-+: *$/.test(n.align[r])?n.align[r]="center":/^ *:-+ *$/.test(n.align[r])?n.align[r]="left":n.align[r]=null;for(l=n.rows.length,r=0;r<l;r++)n.rows[r]=J(n.rows[r],n.header.length).map((function(e){return{text:e}}));for(l=n.header.length,i=0;i<l;i++)n.header[i].tokens=this.lexer.inline(n.header[i].text);for(l=n.rows.length,i=0;i<l;i++)for(a=n.rows[i],o=0;o<a.length;o++)a[o].tokens=this.lexer.inline(a[o].text);return n}}}},{key:"lheading",value:function(e){var t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}},{key:"paragraph",value:function(e){var t=this.rules.block.paragraph.exec(e);if(t){var n="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}},{key:"text",value:function(e){var t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}},{key:"escape",value:function(e){var t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:_(t[1])}}},{key:"tag",value:function(e){var t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):_(t[0]):t[0]}}},{key:"link",value:function(e){var t=this.rules.inline.link.exec(e);if(t){var n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;var r=ee(n.slice(0,-1),"\\");if((n.length-r.length)%2===0)return}else{var i=function(e,t){if(-1===e.indexOf(t[1]))return-1;for(var n=e.length,r=0,i=0;i<n;i++)if("\\"===e[i])i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&--r<0)return i;return-1}(t[2],"()");if(i>-1){var o=(0===t[0].indexOf("!")?5:4)+t[1].length+i;t[2]=t[2].substring(0,i),t[0]=t[0].substring(0,o).trim(),t[3]=""}}var a=t[2],l="";if(this.options.pedantic){var u=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(a);u&&(a=u[1],l=u[3])}else l=t[3]?t[3].slice(1,-1):"";return a=a.trim(),/^</.test(a)&&(a=this.options.pedantic&&!/>$/.test(n)?a.slice(1):a.slice(1,-1)),ne(t,{href:a?a.replace(this.rules.inline._escapes,"$1"):a,title:l?l.replace(this.rules.inline._escapes,"$1"):l},t[0],this.lexer)}}},{key:"reflink",value:function(e,t){var n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){var r=(n[2]||n[1]).replace(/\s+/g," ");if(!(r=t[r.toLowerCase()])){var i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return ne(n,r,n[0],this.lexer)}}},{key:"emStrong",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=this.rules.inline.emStrong.lDelim.exec(e);if(r&&(!r[3]||!n.match(/(?:[0-9A-Za-z\xAA\xB2\xB3\xB5\xB9\xBA\xBC-\xBE\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u0660-\u0669\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07C0-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0966-\u096F\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09E6-\u09F1\u09F4-\u09F9\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A66-\u0A6F\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AE6-\u0AEF\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B66-\u0B6F\u0B71-\u0B77\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0BE6-\u0BF2\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C66-\u0C6F\u0C78-\u0C7E\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CE6-\u0CEF\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D58-\u0D61\u0D66-\u0D78\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DE6-\u0DEF\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F20-\u0F33\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F-\u1049\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u1090-\u1099\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1369-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A16\u1A20-\u1A54\u1A80-\u1A89\u1A90-\u1A99\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B50-\u1B59\u1B83-\u1BA0\u1BAE-\u1BE5\u1C00-\u1C23\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2070\u2071\u2074-\u2079\u207F-\u2089\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2150-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2CFD\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3192-\u3195\u31A0-\u31BF\u31F0-\u31FF\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA830-\uA835\uA840-\uA873\uA882-\uA8B3\uA8D0-\uA8D9\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA900-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF-\uA9D9\uA9E0-\uA9E4\uA9E6-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA50-\uAA59\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDE80-\uDE9C\uDEA0-\uDED0\uDEE1-\uDEFB\uDF00-\uDF23\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC58-\uDC76\uDC79-\uDC9E\uDCA7-\uDCAF\uDCE0-\uDCF2\uDCF4\uDCF5\uDCFB-\uDD1B\uDD20-\uDD39\uDD80-\uDDB7\uDDBC-\uDDCF\uDDD2-\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE40-\uDE48\uDE60-\uDE7E\uDE80-\uDE9F\uDEC0-\uDEC7\uDEC9-\uDEE4\uDEEB-\uDEEF\uDF00-\uDF35\uDF40-\uDF55\uDF58-\uDF72\uDF78-\uDF91\uDFA9-\uDFAF]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDCFA-\uDD23\uDD30-\uDD39\uDE60-\uDE7E\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF27\uDF30-\uDF45\uDF51-\uDF54\uDF70-\uDF81\uDFB0-\uDFCB\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC52-\uDC6F\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD03-\uDD26\uDD36-\uDD3F\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDD0-\uDDDA\uDDDC\uDDE1-\uDDF4\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDEF0-\uDEF9\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC50-\uDC59\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE50-\uDE59\uDE80-\uDEAA\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF30-\uDF3B\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCF2\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC50-\uDC6C\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDF50-\uDF59\uDFB0\uDFC0-\uDFD4]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF50-\uDF59\uDF5B-\uDF61\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE96\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD834[\uDEC0-\uDED3\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB\uDEF0-\uDEF9]|\uD839[\uDCD0-\uDCEB\uDCF0-\uDCF9\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCC7-\uDCCF\uDD00-\uDD43\uDD4B\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83C[\uDD00-\uDD0C]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])/))){var i=r[1]||r[2]||"";if(!i||i&&(""===n||this.rules.inline.punctuation.exec(n))){var o,a,l=r[0].length-1,u=l,s=0,c="*"===r[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+l);null!=(r=c.exec(t));)if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6])if(a=o.length,r[3]||r[4])u+=a;else if(!((r[5]||r[6])&&l%3)||(l+a)%3){if(!((u-=a)>0)){a=Math.min(a,a+u+s);var d=e.slice(0,l+r.index+(r[0].length-o.length)+a);if(Math.min(l,a)%2){var f=d.slice(1,-1);return{type:"em",raw:d,text:f,tokens:this.lexer.inlineTokens(f)}}var h=d.slice(2,-2);return{type:"strong",raw:d,text:h,tokens:this.lexer.inlineTokens(h)}}}else s+=a}}}},{key:"codespan",value:function(e){var t=this.rules.inline.code.exec(e);if(t){var n=t[2].replace(/\n/g," "),r=/[^ ]/.test(n),i=/^ /.test(n)&&/ $/.test(n);return r&&i&&(n=n.substring(1,n.length-1)),n=_(n,!0),{type:"codespan",raw:t[0],text:n}}}},{key:"br",value:function(e){var t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}},{key:"del",value:function(e){var t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}},{key:"autolink",value:function(e,t){var n,r,i=this.rules.inline.autolink.exec(e);if(i)return r="@"===i[2]?"mailto:"+(n=_(this.options.mangle?t(i[1]):i[1])):n=_(i[1]),{type:"link",raw:i[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}},{key:"url",value:function(e,t){var n;if(n=this.rules.inline.url.exec(e)){var r,i;if("@"===n[2])i="mailto:"+(r=_(this.options.mangle?t(n[0]):n[0]));else{var o;do{o=n[0],n[0]=this.rules.inline._backpedal.exec(n[0])[0]}while(o!==n[0]);r=_(n[0]),i="www."===n[1]?"http://"+n[0]:n[0]}return{type:"link",raw:n[0],text:r,href:i,tokens:[{type:"text",raw:r,text:r}]}}}},{key:"inlineText",value:function(e,t){var n,r=this.rules.inline.text.exec(e);if(r)return n=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(r[0]):_(r[0]):r[0]:_(this.options.smartypants?t(r[0]):r[0]),{type:"text",raw:r[0],text:n}}}]),e}(),ie={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:Q,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/};ie.def=j(ie.def).replace("label",ie._label).replace("title",ie._title).getRegex(),ie.bullet=/(?:[*+-]|\d{1,9}[.)])/,ie.listItemStart=j(/^( *)(bull) */).replace("bull",ie.bullet).getRegex(),ie.list=j(ie.list).replace(/bull/g,ie.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+ie.def.source+")").getRegex(),ie._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",ie._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,ie.html=j(ie.html,"i").replace("comment",ie._comment).replace("tag",ie._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ie.paragraph=j(ie._paragraph).replace("hr",ie.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ie._tag).getRegex(),ie.blockquote=j(ie.blockquote).replace("paragraph",ie.paragraph).getRegex(),ie.normal=(0,s.Z)({},ie),ie.gfm=(0,s.Z)((0,s.Z)({},ie.normal),{},{table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"}),ie.gfm.table=j(ie.gfm.table).replace("hr",ie.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ie._tag).getRegex(),ie.gfm.paragraph=j(ie._paragraph).replace("hr",ie.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",ie.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ie._tag).getRegex(),ie.pedantic=(0,s.Z)((0,s.Z)({},ie.normal),{},{html:j("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",ie._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Q,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:j(ie.normal._paragraph).replace("hr",ie.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",ie.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()});var oe={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:Q,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:Q,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};function ae(e){return e.replace(/---/g,"\u2014").replace(/--/g,"\u2013").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1\u2018").replace(/'/g,"\u2019").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1\u201c").replace(/"/g,"\u201d").replace(/\.{3}/g,"\u2026")}function le(e){var t,n,r="",i=e.length;for(t=0;t<i;t++)n=e.charCodeAt(t),Math.random()>.5&&(n="x"+n.toString(16)),r+="&#"+n+";";return r}oe._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~",oe.punctuation=j(oe.punctuation).replace(/punctuation/g,oe._punctuation).getRegex(),oe.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g,oe.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g,oe._comment=j(ie._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),oe.emStrong.lDelim=j(oe.emStrong.lDelim).replace(/punct/g,oe._punctuation).getRegex(),oe.emStrong.rDelimAst=j(oe.emStrong.rDelimAst,"g").replace(/punct/g,oe._punctuation).getRegex(),oe.emStrong.rDelimUnd=j(oe.emStrong.rDelimUnd,"g").replace(/punct/g,oe._punctuation).getRegex(),oe._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,oe._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,oe._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,oe.autolink=j(oe.autolink).replace("scheme",oe._scheme).replace("email",oe._email).getRegex(),oe._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,oe.tag=j(oe.tag).replace("comment",oe._comment).replace("attribute",oe._attribute).getRegex(),oe._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,oe._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,oe._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,oe.link=j(oe.link).replace("label",oe._label).replace("href",oe._href).replace("title",oe._title).getRegex(),oe.reflink=j(oe.reflink).replace("label",oe._label).replace("ref",ie._label).getRegex(),oe.nolink=j(oe.nolink).replace("ref",ie._label).getRegex(),oe.reflinkSearch=j(oe.reflinkSearch,"g").replace("reflink",oe.reflink).replace("nolink",oe.nolink).getRegex(),oe.normal=(0,s.Z)({},oe),oe.pedantic=(0,s.Z)((0,s.Z)({},oe.normal),{},{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:j(/^!?\[(label)\]\((.*?)\)/).replace("label",oe._label).getRegex(),reflink:j(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",oe._label).getRegex()}),oe.gfm=(0,s.Z)((0,s.Z)({},oe.normal),{},{escape:j(oe.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),oe.gfm.url=j(oe.gfm.url,"i").replace("email",oe.gfm._extended_email).getRegex(),oe.breaks=(0,s.Z)((0,s.Z)({},oe.gfm),{},{br:j(oe.br).replace("{2,}","*").getRegex(),text:j(oe.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()});var ue=function(){function e(t){(0,d.Z)(this,e),this.tokens=[],this.tokens.links=Object.create(null),this.options=t||T,this.options.tokenizer=this.options.tokenizer||new re,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};var n={block:ie.normal,inline:oe.normal};this.options.pedantic?(n.block=ie.pedantic,n.inline=oe.pedantic):this.options.gfm&&(n.block=ie.gfm,this.options.breaks?n.inline=oe.breaks:n.inline=oe.gfm),this.tokenizer.rules=n}return(0,f.Z)(e,[{key:"lex",value:function(e){var t;for(e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens}},{key:"blockTokens",value:function(e){var t,n,r,i,o=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(function(e,t,n){return t+"    ".repeat(n.length)}));for(var l=function(){if(o.options.extensions&&o.options.extensions.block&&o.options.extensions.block.some((function(n){return!!(t=n.call({lexer:o},e,a))&&(e=e.substring(t.raw.length),a.push(t),!0)})))return"continue";if(t=o.tokenizer.space(e))return e=e.substring(t.raw.length),1===t.raw.length&&a.length>0?a[a.length-1].raw+="\n":a.push(t),"continue";if(t=o.tokenizer.code(e))return e=e.substring(t.raw.length),!(n=a[a.length-1])||"paragraph"!==n.type&&"text"!==n.type?a.push(t):(n.raw+="\n"+t.raw,n.text+="\n"+t.text,o.inlineQueue[o.inlineQueue.length-1].src=n.text),"continue";if(t=o.tokenizer.fences(e))return e=e.substring(t.raw.length),a.push(t),"continue";if(t=o.tokenizer.heading(e))return e=e.substring(t.raw.length),a.push(t),"continue";if(t=o.tokenizer.hr(e))return e=e.substring(t.raw.length),a.push(t),"continue";if(t=o.tokenizer.blockquote(e))return e=e.substring(t.raw.length),a.push(t),"continue";if(t=o.tokenizer.list(e))return e=e.substring(t.raw.length),a.push(t),"continue";if(t=o.tokenizer.html(e))return e=e.substring(t.raw.length),a.push(t),"continue";if(t=o.tokenizer.def(e))return e=e.substring(t.raw.length),!(n=a[a.length-1])||"paragraph"!==n.type&&"text"!==n.type?o.tokens.links[t.tag]||(o.tokens.links[t.tag]={href:t.href,title:t.title}):(n.raw+="\n"+t.raw,n.text+="\n"+t.raw,o.inlineQueue[o.inlineQueue.length-1].src=n.text),"continue";if(t=o.tokenizer.table(e))return e=e.substring(t.raw.length),a.push(t),"continue";if(t=o.tokenizer.lheading(e))return e=e.substring(t.raw.length),a.push(t),"continue";if(r=e,o.options.extensions&&o.options.extensions.startBlock){var l,u=1/0,s=e.slice(1);o.options.extensions.startBlock.forEach((function(e){"number"===typeof(l=e.call({lexer:this},s))&&l>=0&&(u=Math.min(u,l))})),u<1/0&&u>=0&&(r=e.substring(0,u+1))}if(o.state.top&&(t=o.tokenizer.paragraph(r)))return n=a[a.length-1],i&&"paragraph"===n.type?(n.raw+="\n"+t.raw,n.text+="\n"+t.text,o.inlineQueue.pop(),o.inlineQueue[o.inlineQueue.length-1].src=n.text):a.push(t),i=r.length!==e.length,e=e.substring(t.raw.length),"continue";if(t=o.tokenizer.text(e))return e=e.substring(t.raw.length),(n=a[a.length-1])&&"text"===n.type?(n.raw+="\n"+t.raw,n.text+="\n"+t.text,o.inlineQueue.pop(),o.inlineQueue[o.inlineQueue.length-1].src=n.text):a.push(t),"continue";if(e){var c="Infinite loop on byte: "+e.charCodeAt(0);if(o.options.silent)return console.error(c),"break";throw new Error(c)}};e;){var u=l();if("continue"!==u&&"break"===u)break}return this.state.top=!0,a}},{key:"inline",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return this.inlineQueue.push({src:e,tokens:t}),t}},{key:"inlineTokens",value:function(e){var t,n,r,i,o,a,l=this,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=e;if(this.tokens.links){var c=Object.keys(this.tokens.links);if(c.length>0)for(;null!=(i=this.tokenizer.rules.inline.reflinkSearch.exec(s));)c.includes(i[0].slice(i[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,i.index)+"["+te("a",i[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(i=this.tokenizer.rules.inline.blockSkip.exec(s));)s=s.slice(0,i.index)+"["+te("a",i[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(i=this.tokenizer.rules.inline.escapedEmSt.exec(s));)s=s.slice(0,i.index+i[0].length-2)+"++"+s.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(var d=function(){if(o||(a=""),o=!1,l.options.extensions&&l.options.extensions.inline&&l.options.extensions.inline.some((function(n){return!!(t=n.call({lexer:l},e,u))&&(e=e.substring(t.raw.length),u.push(t),!0)})))return"continue";if(t=l.tokenizer.escape(e))return e=e.substring(t.raw.length),u.push(t),"continue";if(t=l.tokenizer.tag(e))return e=e.substring(t.raw.length),(n=u[u.length-1])&&"text"===t.type&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):u.push(t),"continue";if(t=l.tokenizer.link(e))return e=e.substring(t.raw.length),u.push(t),"continue";if(t=l.tokenizer.reflink(e,l.tokens.links))return e=e.substring(t.raw.length),(n=u[u.length-1])&&"text"===t.type&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):u.push(t),"continue";if(t=l.tokenizer.emStrong(e,s,a))return e=e.substring(t.raw.length),u.push(t),"continue";if(t=l.tokenizer.codespan(e))return e=e.substring(t.raw.length),u.push(t),"continue";if(t=l.tokenizer.br(e))return e=e.substring(t.raw.length),u.push(t),"continue";if(t=l.tokenizer.del(e))return e=e.substring(t.raw.length),u.push(t),"continue";if(t=l.tokenizer.autolink(e,le))return e=e.substring(t.raw.length),u.push(t),"continue";if(!l.state.inLink&&(t=l.tokenizer.url(e,le)))return e=e.substring(t.raw.length),u.push(t),"continue";if(r=e,l.options.extensions&&l.options.extensions.startInline){var i,c=1/0,d=e.slice(1);l.options.extensions.startInline.forEach((function(e){"number"===typeof(i=e.call({lexer:this},d))&&i>=0&&(c=Math.min(c,i))})),c<1/0&&c>=0&&(r=e.substring(0,c+1))}if(t=l.tokenizer.inlineText(r,ae))return e=e.substring(t.raw.length),"_"!==t.raw.slice(-1)&&(a=t.raw.slice(-1)),o=!0,(n=u[u.length-1])&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):u.push(t),"continue";if(e){var f="Infinite loop on byte: "+e.charCodeAt(0);if(l.options.silent)return console.error(f),"break";throw new Error(f)}};e;){var f=d();if("continue"!==f&&"break"===f)break}return u}}],[{key:"rules",get:function(){return{block:ie,inline:oe}}},{key:"lex",value:function(t,n){return new e(n).lex(t)}},{key:"lexInline",value:function(t,n){return new e(n).inlineTokens(t)}}]),e}(),se=function(){function e(t){(0,d.Z)(this,e),this.options=t||T}return(0,f.Z)(e,[{key:"code",value:function(e,t,n){var r=(t||"").match(/\S*/)[0];if(this.options.highlight){var i=this.options.highlight(e,r);null!=i&&i!==e&&(n=!0,e=i)}return e=e.replace(/\n$/,"")+"\n",r?'<pre><code class="'+this.options.langPrefix+_(r)+'">'+(n?e:_(e,!0))+"</code></pre>\n":"<pre><code>"+(n?e:_(e,!0))+"</code></pre>\n"}},{key:"blockquote",value:function(e){return"<blockquote>\n".concat(e,"</blockquote>\n")}},{key:"html",value:function(e){return e}},{key:"heading",value:function(e,t,n,r){if(this.options.headerIds){var i=this.options.headerPrefix+r.slug(n);return"<h".concat(t,' id="').concat(i,'">').concat(e,"</h").concat(t,">\n")}return"<h".concat(t,">").concat(e,"</h").concat(t,">\n")}},{key:"hr",value:function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"}},{key:"list",value:function(e,t,n){var r=t?"ol":"ul";return"<"+r+(t&&1!==n?' start="'+n+'"':"")+">\n"+e+"</"+r+">\n"}},{key:"listitem",value:function(e){return"<li>".concat(e,"</li>\n")}},{key:"checkbox",value:function(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}},{key:"paragraph",value:function(e){return"<p>".concat(e,"</p>\n")}},{key:"table",value:function(e,t){return t&&(t="<tbody>".concat(t,"</tbody>")),"<table>\n<thead>\n"+e+"</thead>\n"+t+"</table>\n"}},{key:"tablerow",value:function(e){return"<tr>\n".concat(e,"</tr>\n")}},{key:"tablecell",value:function(e,t){var n=t.header?"th":"td";return(t.align?"<".concat(n,' align="').concat(t.align,'">'):"<".concat(n,">"))+e+"</".concat(n,">\n")}},{key:"strong",value:function(e){return"<strong>".concat(e,"</strong>")}},{key:"em",value:function(e){return"<em>".concat(e,"</em>")}},{key:"codespan",value:function(e){return"<code>".concat(e,"</code>")}},{key:"br",value:function(){return this.options.xhtml?"<br/>":"<br>"}},{key:"del",value:function(e){return"<del>".concat(e,"</del>")}},{key:"link",value:function(e,t,n){if(null===(e=Y(this.options.sanitize,this.options.baseUrl,e)))return n;var r='<a href="'+e+'"';return t&&(r+=' title="'+t+'"'),r+=">"+n+"</a>"}},{key:"image",value:function(e,t,n){if(null===(e=Y(this.options.sanitize,this.options.baseUrl,e)))return n;var r='<img src="'.concat(e,'" alt="').concat(n,'"');return t&&(r+=' title="'.concat(t,'"')),r+=this.options.xhtml?"/>":">"}},{key:"text",value:function(e){return e}}]),e}(),ce=function(){function e(){(0,d.Z)(this,e)}return(0,f.Z)(e,[{key:"strong",value:function(e){return e}},{key:"em",value:function(e){return e}},{key:"codespan",value:function(e){return e}},{key:"del",value:function(e){return e}},{key:"html",value:function(e){return e}},{key:"text",value:function(e){return e}},{key:"link",value:function(e,t,n){return""+n}},{key:"image",value:function(e,t,n){return""+n}},{key:"br",value:function(){return""}}]),e}(),de=function(){function e(){(0,d.Z)(this,e),this.seen={}}return(0,f.Z)(e,[{key:"serialize",value:function(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}},{key:"getNextSafeSlug",value:function(e,t){var n=e,r=0;if(this.seen.hasOwnProperty(n)){r=this.seen[e];do{n=e+"-"+ ++r}while(this.seen.hasOwnProperty(n))}return t||(this.seen[e]=r,this.seen[n]=0),n}},{key:"slug",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.serialize(e);return this.getNextSafeSlug(n,t.dryrun)}}]),e}(),fe=function(){function e(t){(0,d.Z)(this,e),this.options=t||T,this.options.renderer=this.options.renderer||new se,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new ce,this.slugger=new de}return(0,f.Z)(e,[{key:"parse",value:function(e){var t,n,r,i,o,a,l,u,s,c,d,f,h,p,v,g,m,y,b,w=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],x="",k=e.length;for(t=0;t<k;t++)if(c=e[t],!(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[c.type])||!1===(b=this.options.extensions.renderers[c.type].call({parser:this},c))&&["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(c.type))switch(c.type){case"space":continue;case"hr":x+=this.renderer.hr();continue;case"heading":x+=this.renderer.heading(this.parseInline(c.tokens),c.depth,N(this.parseInline(c.tokens,this.textRenderer)),this.slugger);continue;case"code":x+=this.renderer.code(c.text,c.lang,c.escaped);continue;case"table":for(u="",l="",i=c.header.length,n=0;n<i;n++)l+=this.renderer.tablecell(this.parseInline(c.header[n].tokens),{header:!0,align:c.align[n]});for(u+=this.renderer.tablerow(l),s="",i=c.rows.length,n=0;n<i;n++){for(l="",o=(a=c.rows[n]).length,r=0;r<o;r++)l+=this.renderer.tablecell(this.parseInline(a[r].tokens),{header:!1,align:c.align[r]});s+=this.renderer.tablerow(l)}x+=this.renderer.table(u,s);continue;case"blockquote":s=this.parse(c.tokens),x+=this.renderer.blockquote(s);continue;case"list":for(d=c.ordered,f=c.start,h=c.loose,i=c.items.length,s="",n=0;n<i;n++)g=(v=c.items[n]).checked,m=v.task,p="",v.task&&(y=this.renderer.checkbox(g),h?v.tokens.length>0&&"paragraph"===v.tokens[0].type?(v.tokens[0].text=y+" "+v.tokens[0].text,v.tokens[0].tokens&&v.tokens[0].tokens.length>0&&"text"===v.tokens[0].tokens[0].type&&(v.tokens[0].tokens[0].text=y+" "+v.tokens[0].tokens[0].text)):v.tokens.unshift({type:"text",text:y}):p+=y),p+=this.parse(v.tokens,h),s+=this.renderer.listitem(p,m,g);x+=this.renderer.list(s,d,f);continue;case"html":x+=this.renderer.html(c.text);continue;case"paragraph":x+=this.renderer.paragraph(this.parseInline(c.tokens));continue;case"text":for(s=c.tokens?this.parseInline(c.tokens):c.text;t+1<k&&"text"===e[t+1].type;)s+="\n"+((c=e[++t]).tokens?this.parseInline(c.tokens):c.text);x+=w?this.renderer.paragraph(s):s;continue;default:var D='Token with "'+c.type+'" type was not found.';if(this.options.silent)return void console.error(D);throw new Error(D)}else x+=b||"";return x}},{key:"parseInline",value:function(e,t){t=t||this.renderer;var n,r,i,o="",a=e.length;for(n=0;n<a;n++)if(r=e[n],!(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type])||!1===(i=this.options.extensions.renderers[r.type].call({parser:this},r))&&["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type))switch(r.type){case"escape":case"text":o+=t.text(r.text);break;case"html":o+=t.html(r.text);break;case"link":o+=t.link(r.href,r.title,this.parseInline(r.tokens,t));break;case"image":o+=t.image(r.href,r.title,r.text);break;case"strong":o+=t.strong(this.parseInline(r.tokens,t));break;case"em":o+=t.em(this.parseInline(r.tokens,t));break;case"codespan":o+=t.codespan(r.text);break;case"br":o+=t.br();break;case"del":o+=t.del(this.parseInline(r.tokens,t));break;default:var l='Token with "'+r.type+'" type was not found.';if(this.options.silent)return void console.error(l);throw new Error(l)}else o+=i||"";return o}}],[{key:"parse",value:function(t,n){return new e(n).parse(t)}},{key:"parseInline",value:function(t,n){return new e(n).parseInline(t)}}]),e}(),he=function(){function e(t){(0,d.Z)(this,e),this.options=t||T}return(0,f.Z)(e,[{key:"preprocess",value:function(e){return e}},{key:"postprocess",value:function(e){return e}}]),e}();function pe(e,t){return function(n,r,i){"function"===typeof r&&(i=r,r=null);var o=(0,s.Z)({},r),a=function(e,t,n){return function(r){if(r.message+="\nPlease report this to https://github.com/markedjs/marked.",e){var i="<p>An error occurred:</p><pre>"+_(r.message+"",!0)+"</pre>";return t?Promise.resolve(i):n?void n(null,i):i}if(t)return Promise.reject(r);if(!n)throw r;n(r)}}((r=(0,s.Z)((0,s.Z)({},ve.defaults),o)).silent,r.async,i);if("undefined"===typeof n||null===n)return a(new Error("marked(): input parameter is undefined or null"));if("string"!==typeof n)return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(function(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}(r),r.hooks&&(r.hooks.options=r),i){var l,u=r.highlight;try{r.hooks&&(n=r.hooks.preprocess(n)),l=e(n,r)}catch(p){return a(p)}var c=function(e){var n;if(!e)try{r.walkTokens&&ve.walkTokens(l,r.walkTokens),n=t(l,r),r.hooks&&(n=r.hooks.postprocess(n))}catch(p){e=p}return r.highlight=u,e?a(e):i(null,n)};if(!u||u.length<3)return c();if(delete r.highlight,!l.length)return c();var d=0;return ve.walkTokens(l,(function(e){"code"===e.type&&(d++,setTimeout((function(){u(e.text,e.lang,(function(t,n){if(t)return c(t);null!=n&&n!==e.text&&(e.text=n,e.escaped=!0),0===--d&&c()}))}),0))})),void(0===d&&c())}if(r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(n):n).then((function(t){return e(t,r)})).then((function(e){return r.walkTokens?Promise.all(ve.walkTokens(e,r.walkTokens)).then((function(){return e})):e})).then((function(e){return t(e,r)})).then((function(e){return r.hooks?r.hooks.postprocess(e):e})).catch(a);try{r.hooks&&(n=r.hooks.preprocess(n));var f=e(n,r);r.walkTokens&&ve.walkTokens(f,r.walkTokens);var h=t(f,r);return r.hooks&&(h=r.hooks.postprocess(h)),h}catch(p){return a(p)}}}function ve(e,t,n){return pe(ue.lex,fe.parse)(e,t,n)}(0,r.Z)(he,"passThroughHooks",new Set(["preprocess","postprocess"])),ve.options=ve.setOptions=function(e){var t;return ve.defaults=(0,s.Z)((0,s.Z)({},ve.defaults),e),t=ve.defaults,T=t,ve},ve.getDefaults=I,ve.defaults=T,ve.use=function(){for(var e=ve.defaults.extensions||{renderers:{},childTokens:{}},t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];n.forEach((function(t){var n=(0,s.Z)({},t);if(n.async=ve.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach((function(t){if(!t.name)throw new Error("extension name required");if(t.renderer){var n=e.renderers[t.name];e.renderers[t.name]=n?function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];var o=t.renderer.apply(this,r);return!1===o&&(o=n.apply(this,r)),o}:t.renderer}if(t.tokenizer){if(!t.level||"block"!==t.level&&"inline"!==t.level)throw new Error("extension level must be 'block' or 'inline'");e[t.level]?e[t.level].unshift(t.tokenizer):e[t.level]=[t.tokenizer],t.start&&("block"===t.level?e.startBlock?e.startBlock.push(t.start):e.startBlock=[t.start]:"inline"===t.level&&(e.startInline?e.startInline.push(t.start):e.startInline=[t.start]))}t.childTokens&&(e.childTokens[t.name]=t.childTokens)})),n.extensions=e),t.renderer){var r=ve.defaults.renderer||new se,i=function(e){var n=r[e];r[e]=function(){for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];var l=t.renderer[e].apply(r,o);return!1===l&&(l=n.apply(r,o)),l}};for(var o in t.renderer)i(o);n.renderer=r}if(t.tokenizer){var a=ve.defaults.tokenizer||new re,l=function(e){var n=a[e];a[e]=function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];var l=t.tokenizer[e].apply(a,i);return!1===l&&(l=n.apply(a,i)),l}};for(var u in t.tokenizer)l(u);n.tokenizer=a}if(t.hooks){var c=ve.defaults.hooks||new he,d=function(e){var n=c[e];he.passThroughHooks.has(e)?c[e]=function(r){if(ve.defaults.async)return Promise.resolve(t.hooks[e].call(c,r)).then((function(e){return n.call(c,e)}));var i=t.hooks[e].call(c,r);return n.call(c,i)}:c[e]=function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];var a=t.hooks[e].apply(c,i);return!1===a&&(a=n.apply(c,i)),a}};for(var f in t.hooks)d(f);n.hooks=c}if(t.walkTokens){var h=ve.defaults.walkTokens;n.walkTokens=function(e){var n=[];return n.push(t.walkTokens.call(this,e)),h&&(n=n.concat(h.call(this,e))),n}}ve.setOptions(n)}))},ve.walkTokens=function(e,t){var n,r=[],i=(0,a.Z)(e);try{var o=function(){var e=n.value;switch(r=r.concat(t.call(ve,e)),e.type){case"table":var i,o=(0,a.Z)(e.header);try{for(o.s();!(i=o.n()).done;){var l=i.value;r=r.concat(ve.walkTokens(l.tokens,t))}}catch(p){o.e(p)}finally{o.f()}var u,s=(0,a.Z)(e.rows);try{for(s.s();!(u=s.n()).done;){var c,d=u.value,f=(0,a.Z)(d);try{for(f.s();!(c=f.n()).done;){var h=c.value;r=r.concat(ve.walkTokens(h.tokens,t))}}catch(p){f.e(p)}finally{f.f()}}}catch(p){s.e(p)}finally{s.f()}break;case"list":r=r.concat(ve.walkTokens(e.items,t));break;default:ve.defaults.extensions&&ve.defaults.extensions.childTokens&&ve.defaults.extensions.childTokens[e.type]?ve.defaults.extensions.childTokens[e.type].forEach((function(n){r=r.concat(ve.walkTokens(e[n],t))})):e.tokens&&(r=r.concat(ve.walkTokens(e.tokens,t)))}};for(i.s();!(n=i.n()).done;)o()}catch(l){i.e(l)}finally{i.f()}return r},ve.parseInline=pe(ue.lexInline,fe.parseInline),ve.Parser=fe,ve.parser=fe.parse,ve.Renderer=se,ve.TextRenderer=ce,ve.Lexer=ue,ve.lexer=ue.lex,ve.Tokenizer=re,ve.Slugger=de,ve.Hooks=he,ve.parse=ve;ve.options,ve.setOptions,ve.use,ve.walkTokens,ve.parseInline,fe.parse,ue.lex;var ge,me=["onClickOutside","isOutsideClick"],ye=["width"],be=["width"],we=["inWidth","inHeight","children"],xe=["placeholder","value","onKeyDown","highlight","altNewline","validatedSelection"];function ke(){throw new Error(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"This should not happen")}function De(e){if(!e)return ke(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Assertion failed")}function Ce(e,t){return ke(null!=t?t:"Hell froze over")}var Se=Object.prototype.hasOwnProperty;function Ee(e,t){var n,r;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&Ee(e[r],t[r]););return-1===r}if(!n||"object"===typeof e){for(n in r=0,e){if(Se.call(e,n)&&++r&&!Se.call(t,n))return!1;if(!(n in t)||!Ee(e[n],t[n]))return!1}return Object.keys(t).length===r}}return e!==e&&t!==t}var Fe=function(e){(0,h.Z)(n,e);var t=(0,p.Z)(n);function n(){var e;return(0,d.Z)(this,n),(e=t.apply(this,arguments)).wrapperRef=g.createRef(),e.clickOutside=function(t){if((!e.props.isOutsideClick||e.props.isOutsideClick(t))&&null!==e.wrapperRef.current&&!e.wrapperRef.current.contains(t.target)){for(var n=t.target;null!==n;){if(n.classList.contains("click-outside-ignore"))return;n=n.parentElement}e.props.onClickOutside()}},e}return(0,f.Z)(n,[{key:"componentDidMount",value:function(){document.addEventListener("touchend",this.clickOutside,!0),document.addEventListener("mousedown",this.clickOutside,!0),document.addEventListener("contextmenu",this.clickOutside,!0)}},{key:"componentWillUnmount",value:function(){document.removeEventListener("touchend",this.clickOutside,!0),document.removeEventListener("mousedown",this.clickOutside,!0),document.removeEventListener("contextmenu",this.clickOutside,!0)}},{key:"render",value:function(){var e=this.props,t=(e.onClickOutside,e.isOutsideClick,(0,c.Z)(e,me));return g.createElement("div",(0,s.Z)((0,s.Z)({},t),{},{ref:this.wrapperRef}),this.props.children)}}]),n}(g.PureComponent);function Me(e){var t,n;return{"--gdg-accent-color":e.accentColor,"--gdg-accent-fg":e.accentFg,"--gdg-accent-light":e.accentLight,"--gdg-text-dark":e.textDark,"--gdg-text-medium":e.textMedium,"--gdg-text-light":e.textLight,"--gdg-text-bubble":e.textBubble,"--gdg-bg-icon-header":e.bgIconHeader,"--gdg-fg-icon-header":e.fgIconHeader,"--gdg-text-header":e.textHeader,"--gdg-text-group-header":null!=(t=e.textGroupHeader)?t:e.textHeader,"--gdg-text-header-selected":e.textHeaderSelected,"--gdg-bg-cell":e.bgCell,"--gdg-bg-cell-medium":e.bgCellMedium,"--gdg-bg-header":e.bgHeader,"--gdg-bg-header-has-focus":e.bgHeaderHasFocus,"--gdg-bg-header-hovered":e.bgHeaderHovered,"--gdg-bg-bubble":e.bgBubble,"--gdg-bg-bubble-selected":e.bgBubbleSelected,"--gdg-bg-search-result":e.bgSearchResult,"--gdg-border-color":e.borderColor,"--gdg-horizontal-border-color":null!=(n=e.horizontalBorderColor)?n:e.borderColor,"--gdg-drilldown-border":e.drilldownBorder,"--gdg-link-color":e.linkColor,"--gdg-cell-horizontal-padding":"".concat(e.cellHorizontalPadding,"px"),"--gdg-cell-vertical-padding":"".concat(e.cellVerticalPadding,"px"),"--gdg-header-font-style":e.headerFontStyle,"--gdg-base-font-style":e.baseFontStyle,"--gdg-marker-font-style":e.markerFontStyle,"--gdg-font-family":e.fontFamily,"--gdg-editor-font-size":e.editorFontSize}}var Ze={accentColor:"#4F5DFF",accentFg:"#FFFFFF",accentLight:"rgba(62, 116, 253, 0.1)",textDark:"#313139",textMedium:"#737383",textLight:"#B2B2C0",textBubble:"#313139",bgIconHeader:"#737383",fgIconHeader:"#FFFFFF",textHeader:"#313139",textGroupHeader:"#313139BB",textHeaderSelected:"#FFFFFF",bgCell:"#FFFFFF",bgCellMedium:"#FAFAFB",bgHeader:"#F7F7F8",bgHeaderHasFocus:"#E9E9EB",bgHeaderHovered:"#EFEFF1",bgBubble:"#EDEDF3",bgBubbleSelected:"#FFFFFF",bgSearchResult:"#fff9e3",borderColor:"rgba(115, 116, 131, 0.16)",drilldownBorder:"rgba(0, 0, 0, 0)",linkColor:"#4F5DFF",cellHorizontalPadding:8,cellVerticalPadding:3,headerIconSize:18,headerFontStyle:"600 13px",baseFontStyle:"13px",markerFontStyle:"9px",fontFamily:"Inter, Roboto, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, noto, arial, sans-serif",editorFontSize:"13px",lineHeight:1.4};function Ae(){return Ze}var Re=g.createContext(Ze);function Ie(){return g.useContext(Re)}var Te,Oe,Pe,He,Le=null,ze=void 0,Be="header",_e="group-header",Ve="out-of-bounds";(Oe=Te||(Te={})).Uri="uri",Oe.Text="text",Oe.Image="image",Oe.RowID="row-id",Oe.Number="number",Oe.Bubble="bubble",Oe.Boolean="boolean",Oe.Loading="loading",Oe.Markdown="markdown",Oe.Drilldown="drilldown",Oe.Protected="protected",Oe.Custom="custom",(He=Pe||(Pe={})).HeaderRowID="headerRowID",He.HeaderCode="headerCode",He.HeaderNumber="headerNumber",He.HeaderString="headerString",He.HeaderBoolean="headerBoolean",He.HeaderAudioUri="headerAudioUri",He.HeaderVideoUri="headerVideoUri",He.HeaderEmoji="headerEmoji",He.HeaderImage="headerImage",He.HeaderUri="headerUri",He.HeaderPhone="headerPhone",He.HeaderMarkdown="headerMarkdown",He.HeaderDate="headerDate",He.HeaderTime="headerTime",He.HeaderEmail="headerEmail",He.HeaderReference="headerReference",He.HeaderIfThenElse="headerIfThenElse",He.HeaderSingleValue="headerSingleValue",He.HeaderLookup="headerLookup",He.HeaderTextTemplate="headerTextTemplate",He.HeaderMath="headerMath",He.HeaderRollup="headerRollup",He.HeaderJoinStrings="headerJoinStrings",He.HeaderSplitString="headerSplitString",He.HeaderGeoDistance="headerGeoDistance",He.HeaderArray="headerArray",He.RowOwnerOverlay="rowOwnerOverlay",He.ProtectedColumnOverlay="protectedColumnOverlay";var Ne,We,je,Ue="___gdg_header_cell_",Xe=Ue+"checked",Ye=Ue+"unchecked",Ke=Ue+"indeterminate";function $e(e){return"width"in e&&"number"===typeof e.width}function Ge(e){return qe.apply(this,arguments)}function qe(){return(qe=(0,o.Z)((0,i.Z)().mark((function e(t){return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("object"!==typeof t){e.next=2;break}return e.abrupt("return",t);case 2:return e.next=4,t();case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Qe(e){return e.kind!==Te.Loading&&e.kind!==Te.Bubble&&e.kind!==Te.RowID&&e.kind!==Te.Protected&&e.kind!==Te.Drilldown}function Je(e){return e.kind===Ne.Marker||e.kind===Ne.NewRow}function et(e){return!(!Qe(e)||e.kind===Te.Image)&&(e.kind===Te.Text||e.kind===Te.Number||e.kind===Te.Markdown||e.kind===Te.Uri||e.kind===Te.Custom||e.kind===Te.Boolean?!0!==e.readonly:void Ce(0,"A cell was passed with an invalid kind"))}function tt(e){return D(e,"editor")}function nt(e){var t;return!(null!=(t=e.readonly)&&t)}(We=Ne||(Ne={})).NewRow="new-row",We.Marker="marker";var rt=function(e){function t(e){var n=this;(0,d.Z)(this,t),this.items=e,this.offset=function(e){return 0===e?n:new t(n.items.map((function(t){return[t[0]+e,t[1]+e]})))},this.add=function(e){var r="number"===typeof e?[e,e+1]:e,i=function(e){if(0===e.length)return[];var t=(0,l.Z)(e),n=[];t.sort((function(e,t){return e[0]-t[0]})),n.push((0,l.Z)(t[0]));var r,i=(0,a.Z)(t.slice(1));try{for(i.s();!(r=i.n()).done;){var o=r.value,u=n[n.length-1];u[1]<o[0]?n.push((0,l.Z)(o)):u[1]<o[1]&&(u[1]=o[1])}}catch(s){i.e(s)}finally{i.f()}return n}([].concat((0,l.Z)(n.items),[r]));return new t(i)},this.remove=function(e){var r,i=(0,l.Z)(n.items),o="number"===typeof e?e:e[0],s="number"===typeof e?e+1:e[1],c=(0,a.Z)(i.entries());try{for(c.s();!(r=c.n()).done;){var d=(0,u.Z)(r.value,2),f=d[0],h=d[1],p=(0,u.Z)(h,2),v=p[0],g=p[1];if(v<=s&&o<=g){var m=[];v<o&&m.push([v,o]),s<g&&m.push([s,g]),i.splice.apply(i,[f,1].concat(m))}}}catch(y){c.e(y)}finally{c.f()}return new t(i)},this.first=function(){if(0!==n.items.length)return n.items[0][0]},this.last=function(){if(0!==n.items.length)return n.items.slice(-1)[0][1]-1},this.hasIndex=function(e){for(var t=0;t<n.items.length;t++){var r=(0,u.Z)(n.items[t],2),i=r[0],o=r[1];if(e>=i&&e<o)return!0}return!1},this.hasAll=function(e){for(var t=e[0];t<e[1];t++)if(!n.hasIndex(t))return!1;return!0},this.some=function(e){var t,r=(0,a.Z)(n);try{for(r.s();!(t=r.n()).done;){if(e(t.value))return!0}}catch(i){r.e(i)}finally{r.f()}return!1},this.equals=function(e){if(e===n)return!0;if(e.items.length!==n.items.length)return!1;for(var t=0;t<n.items.length;t++){var r=e.items[t],i=n.items[t];if(r[0]!==i[0]||r[1]!==i[1])return!1}return!0},this.toArray=function(){var e,t=[],r=(0,a.Z)(n.items);try{for(r.s();!(e=r.n()).done;)for(var i=(0,u.Z)(e.value,2),o=i[0],l=i[1],s=o;s<l;s++)t.push(s)}catch(c){r.e(c)}finally{r.f()}return t}}return(0,f.Z)(t,[{key:"length",get:function(){var e,t=0,n=(0,a.Z)(this.items);try{for(n.s();!(e=n.n()).done;){var r=(0,u.Z)(e.value,2),i=r[0];t+=r[1]-i}}catch(o){n.e(o)}finally{n.f()}return t}},{key:e,value:(0,i.Z)().mark((function e(){var t,n,r,o,l,s;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=(0,a.Z)(this.items),e.prev=1,t.s();case 3:if((n=t.n()).done){e.next=14;break}r=(0,u.Z)(n.value,2),o=r[0],l=r[1],s=o;case 6:if(!(s<l)){e.next=12;break}return e.next=9,s;case 9:s++,e.next=6;break;case 12:e.next=3;break;case 14:e.next=19;break;case 16:e.prev=16,e.t0=e.catch(1),t.e(e.t0);case 19:return e.prev=19,t.f(),e.finish(19);case 22:case"end":return e.stop()}}),e,this,[[1,16,19,22]])}))}]),t}(Symbol.iterator),it=rt;it.empty=function(){return null!=je?je:je=new rt([])},it.fromSingleSelection=function(e){return rt.empty().add(e)};var ot=(0,v.d)("div")({name:"DataGridOverlayEditorStyle",class:"d1t1th9s",vars:{"d1t1th9s-0":[function(e){return e.targetY},"px"],"d1t1th9s-1":[function(e){return e.targetX-1},"px"],"d1t1th9s-2":[function(e){return e.targetY-1},"px"],"d1t1th9s-3":[function(e){return e.targetWidth+2},"px"],"d1t1th9s-4":[function(e){return e.targetHeight+2},"px"],"d1t1th9s-5":[function(e){return e.targetY+10},"px"],"d1t1th9s-6":[function(e){return Math.max(0,(e.targetHeight-28)/2)},"px"]}});function at(){var e=function(){var e=g.useState(),t=(0,u.Z)(e,2),n=t[0];return[null!=n?n:void 0,t[1]]}(),t=(0,u.Z)(e,2),n=t[0],r=t[1],i=g.useState(0),o=(0,u.Z)(i,2),a=o[0],l=o[1],s=g.useState(!0),c=(0,u.Z)(s,2),d=c[0],f=c[1];return g.useLayoutEffect((function(){if(void 0!==n&&"IntersectionObserver"in window){var e=new IntersectionObserver((function(e){0!==e.length&&f(e[0].isIntersecting)}),{threshold:1});return e.observe(n),function(){return e.disconnect()}}}),[n]),g.useEffect((function(){if(!d&&void 0!==n){var e;return e=requestAnimationFrame((function t(){var r=n.getBoundingClientRect().right;l((function(e){return Math.min(e+window.innerWidth-r-10,0)})),e=requestAnimationFrame(t)})),function(){void 0!==e&&cancelAnimationFrame(e)}}}),[n,d]),{ref:r,style:g.useMemo((function(){return{transform:"translateX(".concat(a,"px)")}}),[a])}}var lt=function(e){var t=e.target,n=e.content,r=e.onFinishEditing,a=e.forceEditMode,l=e.initialValue,c=e.imageEditorOverride,d=e.markdownDivCreateNode,f=e.highlight,h=e.className,p=e.theme,v=e.id,m=e.cell,y=e.validateCell,b=e.getCellRenderer,w=e.provideEditor,x=e.isOutsideClick,D=g.useState(a?n:void 0),C=(0,u.Z)(D,2),S=C[0],E=C[1],F=g.useRef(null!=S?S:n);F.current=null!=S?S:n;var M,Z,A=g.useState((function(){return void 0===y||!(Qe(n)&&!1===(null==y?void 0:y(m,n,F.current)))})),R=(0,u.Z)(A,2),I=R[0],T=R[1],O=g.useCallback((function(e,t){r(I?e:void 0,t)}),[I,r]),P=g.useCallback((function(e){if(void 0!==y&&void 0!==e&&Qe(e)){var t=y(m,e,F.current);!1===t?T(!1):"object"===typeof t?(e=t,T(!0)):T(!0)}E(e)}),[m,y]),H=g.useRef(!1),L=g.useRef(void 0),z=g.useCallback((function(){O(S,[0,0]),H.current=!0}),[S,O]),B=g.useCallback((function(e,t){var n;O(e,null!=(n=null!=t?t:L.current)?n:[0,0]),H.current=!0}),[O]),_=g.useCallback(function(){var e=(0,o.Z)((0,i.Z)().mark((function e(t){var n;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=!1,"Escape"===t.key?(t.stopPropagation(),t.preventDefault(),L.current=[0,0]):"Enter"!==t.key||t.shiftKey?"Tab"===t.key&&(t.stopPropagation(),t.preventDefault(),L.current=[t.shiftKey?-1:1,0],n=!0):(t.stopPropagation(),t.preventDefault(),L.current=[0,1],n=!0),window.setTimeout((function(){H.current||void 0===L.current||(O(n?S:void 0,L.current),H.current=!0)}),0);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[O,S]),V=null!=S?S:n,N=g.useMemo((function(){var e,t;if(Je(n))return[];var r=null==w?void 0:w(n);return void 0!==r?[r,!1]:[null==(t=null==(e=b(n))?void 0:e.provideEditor)?void 0:t.call(e,n),!1]}),[n,b,w]),W=(0,u.Z)(N,2),j=W[0],U=W[1],X=at(),Y=X.ref,K=X.style,$=!0,G=!0;if(void 0!==j){$=!0!==j.disablePadding,G=!0!==j.disableStyling;var q=tt(j);q&&(Z=j.styleOverride);var Q=q?j.editor:j;M=g.createElement(Q,{isHighlighted:f,onChange:P,value:V,initialValue:l,onFinishedEditing:B,validatedSelection:Qe(V)?V.selectionRange:void 0,forceEditMode:a,target:t,imageEditorOverride:c,markdownDivCreateNode:d,isValid:I})}Z=(0,s.Z)((0,s.Z)({},Z),K);var J=document.getElementById("portal");if(null===J)return console.error('Cannot open Data Grid overlay editor, because portal not found.  Please add `<div id="portal" />` as the last child of your `<body>`.'),null;var ee=G?"gdg-style":"gdg-unstyle";return I||(ee+=" invalid"),$&&(ee+=" pad"),(0,k.createPortal)(g.createElement(Re.Provider,{value:p},g.createElement(Fe,{style:Me(p),className:h,onClickOutside:z,isOutsideClick:x},g.createElement(ot,{ref:Y,id:v,className:ee,style:Z,as:!0===U?"label":void 0,targetX:t.x,targetY:t.y,targetWidth:t.width,targetHeight:t.height},g.createElement("div",{className:"clip-region",onKeyDown:_},M)))),J)},ut=65536,st=[];function ct(e,t){return t*ut+e}function dt(e){return e%ut}function ft(e,t){return(e-t)/ut}function ht(e){var t=dt(e);return[t,ft(e,t)]}var pt=function(){function e(){var t=this;(0,d.Z)(this,e),this.imageLoaded=function(){},this.loadedLocations=[],this.visibleWindow={x:0,y:0,width:0,height:0},this.freezeCols=0,this.isInWindow=function(e){var n=dt(e),r=ft(e,n),i=t.visibleWindow;return n<t.freezeCols&&r>=i.y&&r<=i.y+i.height||n>=i.x&&n<=i.x+i.width&&r>=i.y&&r<=i.y+i.height},this.cache={},this.sendLoaded=C((function(){t.imageLoaded(t.loadedLocations),t.loadedLocations=[]}),20),this.clearOutOfWindow=function(){for(var e=0,n=Object.keys(t.cache);e<n.length;e++){for(var r=n[e],i=t.cache[r],o=!1,a=0;a<i.cells.length;a++){var l=i.cells[a];if(t.isInWindow(l)){o=!0;break}}o?i.cells=i.cells.filter(t.isInWindow):(i.cancel(),delete t.cache[r])}}}return(0,f.Z)(e,[{key:"setCallback",value:function(e){this.imageLoaded=e}},{key:"setWindow",value:function(e,t){this.visibleWindow.x===e.x&&this.visibleWindow.y===e.y&&this.visibleWindow.width===e.width&&this.visibleWindow.height===e.height&&this.freezeCols===t||(this.visibleWindow=e,this.freezeCols=t,this.clearOutOfWindow())}},{key:"loadImage",value:function(e,t,n,r){var l,u=this,s=!1,c=null!=(l=st.pop())?l:new Image,d=!1,f={img:void 0,cells:[ct(t,n)],url:e,cancel:function(){d||(d=!0,st.length<12?st.unshift(c):s||(c.src=""))}},h=new Promise((function(e){return c.addEventListener("load",(function(){return e(null)}))}));requestAnimationFrame((0,o.Z)((0,i.Z)().mark((function t(){var n,o,l,p;return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,c.src=e,t.next=4,h;case 4:return t.next=6,c.decode();case 6:if(void 0!==(n=u.cache[r])&&!d){n.img=c,o=(0,a.Z)(n.cells);try{for(o.s();!(l=o.n()).done;)p=l.value,u.loadedLocations.push(ht(p))}catch(i){o.e(i)}finally{o.f()}s=!0,u.sendLoaded()}t.next=13;break;case 10:t.prev=10,t.t0=t.catch(0),f.cancel();case 13:case"end":return t.stop()}}),t,null,[[0,10]])})))),this.cache[r]=f}},{key:"loadOrGetImage",value:function(e,t,n){var r=e,i=this.cache[r];if(void 0!==i){var o=ct(t,n);return i.cells.includes(o)||i.cells.push(o),i.img}this.loadImage(e,t,n,r)}}]),e}(),vt=pt;function gt(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=g.useRef();o.current=t,g.useEffect((function(){if(null!==n&&void 0!==n.addEventListener){var t=n,a=function(e){var n;null==(n=o.current)||n.call(t,e)};return t.addEventListener(e,a,{passive:r,capture:i}),function(){t.removeEventListener(e,a,{capture:i})}}}),[e,n,r,i])}function mt(e,t){return void 0===e?void 0:t}var yt=Math.PI;function bt(e){return e*yt/180}var wt=function(e,t,n){return{x1:e-n/2,y1:t-n/2,x2:e+n/2,y2:t+n/2}},xt=function(e,t,n,r,i){switch(e){case"left":return Math.floor(t)+r+i/2;case"center":return Math.floor(t+n/2);case"right":return Math.floor(t+n)-r-i/2}},kt=function(e,t,n){return Math.min(e,t-2*n)},Dt=function(e,t,n){return n.x1<=e&&e<=n.x2&&n.y1<=t&&t<=n.y2},Ct=function(e){var t,n=null!=(t=e.fgColor)?t:"currentColor";return g.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},g.createElement("path",{d:"M12.7073 7.05029C7.87391 11.8837 10.4544 9.30322 6.03024 13.7273C5.77392 13.9836 5.58981 14.3071 5.50189 14.6587L4.52521 18.5655C4.38789 19.1148 4.88543 19.6123 5.43472 19.475L9.34146 18.4983C9.69313 18.4104 10.0143 18.2286 10.2706 17.9722L16.9499 11.2929",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}),g.createElement("path",{d:"M20.4854 4.92901L19.0712 3.5148C18.2901 2.73375 17.0238 2.73375 16.2428 3.5148L14.475 5.28257C15.5326 7.71912 16.4736 8.6278 18.7176 9.52521L20.4854 7.75744C21.2665 6.97639 21.2665 5.71006 20.4854 4.92901Z",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}))},St=function(e){var t,n=null!=(t=e.fgColor)?t:"currentColor";return g.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},g.createElement("path",{d:"M19 6L10.3802 17L5.34071 11.8758",vectorEffect:"non-scaling-stroke",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))};var Et="\u0591-\u07ff\ufb1d-\ufdfd\ufe70-\ufefc",Ft="A-Za-z\xc0-\xd6\xd8-\xf6\xf8-\u02b8\u0300-\u0590\u0800-\u1fff\u200e\u2c00-\ufb1c\ufe00-\ufe6f\ufefd-\uffff",Mt=new RegExp("^[^"+Ft+"]*["+Et+"]"),Zt=new RegExp("^[^"+Et+"]*["+Ft+"]");function At(e){return Mt.test(e)?"rtl":Zt.test(e)?"ltr":"neutral"}var Rt=void 0;var It=Symbol();function Tt(e,t){return(null!=e?e:"")===(null!=t?t:"")}function Ot(e,t,n){var r=n.x,i=n.x+n.width-1,o=n.y,a=n.y+n.height-1,l=(0,u.Z)(e,2),s=l[0],c=l[1];if(c<o||c>a)return!1;if(void 0===t.span)return s>=r&&s<=i;var d=(0,u.Z)(t.span,2),f=d[0],h=d[1];return f>=r&&f<=i||h>=r&&f<=i||f<r&&h>i}function Pt(e,t){var n=e;if(void 0!==t){var r=(0,l.Z)(e),i=n[t.src];t.src>t.dest?(r.splice(t.src,1),r.splice(t.dest,0,i)):(r.splice(t.dest+1,0,i),r.splice(t.src,1)),n=r=r.map((function(t,n){return(0,s.Z)((0,s.Z)({},t),{},{sticky:e[n].sticky})}))}return n}function Ht(e,t){for(var n=0,r=Pt(e,t),i=0;i<r.length;i++){var o=r[i];if(!o.sticky)break;n+=o.width}return n}function Lt(e,t,n,r,i){var o,l=Pt(e,r),u=[],s=(0,a.Z)(l);try{for(s.s();!(o=s.n()).done;){var c=o.value;if(!c.sticky)break;u.push(c)}}catch(m){s.e(m)}finally{s.f()}if(u.length>0){var d,f=(0,a.Z)(u);try{for(f.s();!(d=f.n()).done;){n-=d.value.width}}catch(m){f.e(m)}finally{f.f()}}for(var h=t,p=null!=i?i:0;p<=n&&h<l.length;)p+=l[h].width,h++;for(var v=t;v<h;v++){var g=l[v];g.sticky||u.push(g)}return u}var zt=0,Bt={},_t="undefined"===typeof window;function Vt(){return(Vt=(0,o.Z)((0,i.Z)().mark((function e(){var t;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!_t&&void 0!==(null==(t=null==document?void 0:document.fonts)?void 0:t.ready)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,document.fonts.ready;case 4:zt=0,Bt={},S.clear(),F.clear(),E.clear();case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Nt(e,t,n){var r=function(e,t,n,r){return"".concat(e,"_").concat(null!=r?r:t.font,"_").concat(n)}(e,t,"middle",n),i=Bt[r];return void 0===i&&(i=t.measureText(e),Bt[r]=i,zt++),zt>1e4&&(Bt={},zt=0),i}function Wt(e,t){return"string"!==typeof t&&(t="".concat(t.baseFontStyle," ").concat(t.fontFamily)),function(e,t){var n,r=(0,a.Z)(Ut);try{for(r.s();!(n=r.n()).done;){var i=n.value;if(i.key===t)return i.val}}catch(s){r.e(s)}finally{r.f()}var o=jt(e,"alphabetic"),l=jt(e,"middle"),u=-(l.actualBoundingBoxDescent-o.actualBoundingBoxDescent)+o.actualBoundingBoxAscent/2;return Ut.push({key:t,val:u}),u}(e,t)}function jt(e,t){e.save(),e.textBaseline=t;var n=e.measureText("ABCDEFGHIJKLMNOPQRSTUVWXYZ");return e.restore(),n}!function(){Vt.apply(this,arguments)}();var Ut=[];function Xt(e,t,n){var r=e.ctx,i=e.theme,o=null!=t?t:{},a=null!=n?n:i.textDark;return a!==o.fillStyle&&(r.fillStyle=a,o.fillStyle=a),o}function Yt(e,t,n){var r=e.rect,i=e.ctx,o=e.theme;i.fillStyle=o.textDark,Gt({ctx:i,rect:r,theme:o},t,n)}function Kt(e,t,n,r,i,o,a,l,u){"right"===u?e.fillText(t,n+i-(l.cellHorizontalPadding+.5),r+o/2+a):"center"===u?e.fillText(t,n+i/2,r+o/2+a):e.fillText(t,n+l.cellHorizontalPadding+.5,r+o/2+a)}function $t(e,t){var n=Nt("ABCi09jgqpy",e,t);return n.actualBoundingBoxAscent+n.actualBoundingBoxDescent}function Gt(e,t,n,r,i){var o=e.ctx,l=e.rect,u=e.theme,s=l.x,c=l.y,d=l.width,f=l.height;if(!(r=null!=r&&r)){t.includes("\n")&&(t=t.split(/\r?\n/)[0]);var h=d/4;t.length>h&&(t=t.slice(0,h))}var p=Wt(o,u),v="rtl"===At(t);if(void 0===n&&v&&(n="right"),v&&(o.direction="rtl"),t.length>0){var g=!1;if("right"===n?(o.textAlign="right",g=!0):void 0!==n&&"left"!==n&&(o.textAlign=n,g=!0),r){var m="".concat(u.fontFamily," ").concat(u.baseFontStyle),y=function(e,t,n,r,i,o){var l="".concat(t,"_").concat(n,"_").concat(r,"px"),u=S.get(l);if(void 0!==u)return u;if(r<=0)return[];var s,c=[],d=t.split("\n"),f=E.get(n),h=void 0===f?t.length:r/f.size*1.5,p=i&&void 0!==f&&f.count>2e4,v=(0,a.Z)(d);try{for(v.s();!(s=v.n()).done;){var g=s.value,m=M(e,g.slice(0,Math.max(0,h)),n,p),y=Math.min(g.length,h);if(m<=r)c.push(g);else{for(;m>r;){var b=Z(e,g,r,n,m,y,p,o),w=g.slice(0,Math.max(0,b));g=g.slice(w.length),c.push(w),m=M(e,g.slice(0,Math.max(0,h)),n,p),y=Math.min(g.length,h)}m>0&&c.push(g)}}}catch(x){v.e(x)}finally{v.f()}return c=c.map((function(e,t){return 0===t?e.trimEnd():e.trim()})),S.set(l,c),S.size>500&&S.delete(S.keys().next().value),c}(o,t,m,d-2*u.cellHorizontalPadding,null!=i&&i),b=$t(o,m),w=u.lineHeight*b,x=b+w*(y.length-1),k=x+u.cellVerticalPadding>f;k&&(o.save(),o.rect(s,c,d,f),o.clip());var D,C=c+f/2-x/2,F=Math.max(c+u.cellVerticalPadding,C),A=(0,a.Z)(y);try{for(A.s();!(D=A.n()).done;){if(Kt(o,D.value,s,F,d,b,p,u,n),(F+=w)>c+f)break}}catch(R){A.e(R)}finally{A.f()}k&&o.restore()}else Kt(o,t,s,c,d,f,p,u,n);g&&(o.textAlign="start"),v&&(o.direction="inherit")}}function qt(e,t,n,r,i,o,a,l){var u=arguments.length>8&&void 0!==arguments[8]?arguments[8]:-20,s=arguments.length>9&&void 0!==arguments[9]?arguments[9]:-20,c=arguments.length>10&&void 0!==arguments[10]?arguments[10]:32,d=arguments.length>11&&void 0!==arguments[11]?arguments[11]:"center",f=Math.floor(i+a/2),h=kt(c,a,t.cellVerticalPadding),p=h/2,v=xt(d,r,o,t.cellHorizontalPadding,h),g=wt(v,f,h),m=Dt(r+u,i+s,g);switch(n){case!0:e.beginPath(),Jt(e,v-h/2,f-h/2,h,h,4),e.fillStyle=l?t.accentColor:t.textMedium,e.fill(),e.beginPath(),e.moveTo(v-p+h/4.23,f-p+h/1.97),e.lineTo(v-p+h/2.42,f-p+h/1.44),e.lineTo(v-p+h/1.29,f-p+h/3.25),e.strokeStyle=t.bgCell,e.lineJoin="round",e.lineCap="round",e.lineWidth=1.9,e.stroke();break;case Le:case!1:e.beginPath(),Jt(e,v-h/2+.5,f-h/2+.5,h-1,h-1,4),e.lineWidth=1,e.strokeStyle=m?t.textDark:t.textMedium,e.stroke();break;case ze:e.beginPath(),Jt(e,v-h/2,f-h/2,h,h,4),e.fillStyle=m?t.textMedium:t.textLight,e.fill(),e.beginPath(),e.moveTo(v-h/3,f),e.lineTo(v+h/3,f),e.strokeStyle=t.bgCell,e.lineCap="round",e.lineWidth=1.9,e.stroke();break;default:Ce()}}function Qt(e){e.ctx.textAlign="start"}function Jt(e,t,n,r,i,o){"number"===typeof o&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.min(o.tl,i/2,r/2),tr:Math.min(o.tr,i/2,r/2),bl:Math.min(o.bl,i/2,r/2),br:Math.min(o.br,i/2,r/2)},e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}var en={};function tn(e,t){var n,r=e.rect,i=e.theme,o=e.ctx,l=e.imageLoader,s=e.col,c=e.row,d=r.x,f=r.width,h="".concat(i.baseFontStyle," ").concat(i.fontFamily),p=$t(o,h),v=Math.min(r.height,Math.max(16,2*Math.ceil(p*i.lineHeight))),g=Math.floor(r.y+(r.height-v)/2),m=v-10,y=d+i.cellHorizontalPadding,b=function(e,t,n){var r=Math.ceil(window.devicePixelRatio),i=n-10,o=n*r,a=28*r,l="".concat(e,",").concat(t,",").concat(r,",").concat(n);if(void 0!==en[l])return{el:en[l],height:o,width:a,middleWidth:4*r,sideWidth:11*r,padding:5*r,dpr:r};var u=document.createElement("canvas"),s=u.getContext("2d");if(null===s)return null;u.width=a,u.height=o,s.scale(r,r),en[l]=u;var c=Math.min(6,9,i/2);return s.beginPath(),Jt(s,5,5,18,i,c),s.shadowColor="rgba(24, 25, 34, 0.4)",s.shadowBlur=1,s.fillStyle=e,s.fill(),s.shadowColor="rgba(24, 25, 34, 0.3)",s.shadowOffsetY=1,s.shadowBlur=5,s.fillStyle=e,s.fill(),s.shadowOffsetY=0,s.shadowBlur=0,s.shadowBlur=0,s.beginPath(),Jt(s,5.5,5.5,18,i,c),s.strokeStyle=t,s.lineWidth=1,s.stroke(),{el:u,height:o,width:a,sideWidth:11*r,middleWidth:6*r,padding:5*r,dpr:r}}(i.bgCell,i.drilldownBorder,v),w=[],x=(0,a.Z)(t);try{for(x.s();!(n=x.n()).done;){var k=n.value;if(y>d+f)break;var D=Nt(k.text,o,h).width,C=0;if(void 0!==k.img)void 0!==l.loadOrGetImage(k.img,s,c)&&(C=m-8+4);var S=D+C+16;w.push({x:y,width:S}),y+=S+4}}catch(J){x.e(J)}finally{x.f()}if(null!==b){var E,F=b.el,M=b.height,Z=b.middleWidth,A=b.sideWidth,R=b.width,I=b.dpr,T=A/I,O=b.padding/I,P=(0,a.Z)(w);try{for(P.s();!(E=P.n()).done;){var H=E.value,L=Math.floor(H.x),z=Math.floor(H.width),B=z-2*(T-O);o.imageSmoothingEnabled=!1,o.drawImage(F,0,0,A,M,L-O,g,T,v),B>0&&o.drawImage(F,A,0,Z,M,L+(T-O),g,B,v),o.drawImage(F,R-A,0,A,M,L+z-(T-O),g,T,v),o.imageSmoothingEnabled=!0}}catch(J){P.e(J)}finally{P.f()}}o.beginPath();var _,V=(0,a.Z)(w.entries());try{for(V.s();!(_=V.n()).done;){var N=(0,u.Z)(_.value,2),W=N[0],j=N[1],U=t[W],X=j.x+8;if(void 0!==U.img){var Y=l.loadOrGetImage(U.img,s,c);if(void 0!==Y){var K=m-8,$=0,G=0,q=Y.width,Q=Y.height;q>Q?($+=(q-Q)/2,q=Q):Q>q&&(G+=(Q-q)/2,Q=q),o.beginPath(),Jt(o,X,g+v/2-K/2,K,K,3),o.save(),o.clip(),o.drawImage(Y,$,G,q,Q,X,g+v/2-K/2,K,K),o.restore(),X+=K+4}}o.beginPath(),o.fillStyle=i.textBubble,o.fillText(U.text,X,g+v/2+Wt(o,i))}}catch(J){V.e(J)}finally{V.f()}}function nn(e,t,n,r,i,o,a,l,u,s,c,d,f,h,p){var v={x:0,y:o+s,width:0,height:0},g=o-i;if(e>=d){var m=a>e?-1:1,y=Ht(h);v.x+=y+u;for(var b=a;b!==e;b+=m)v.x+=h[1===m?b:b-1].width*m}else for(var w=0;w<e;w++)v.x+=h[w].width;if(v.width=h[e].width+1,-1===t)v.y=i,v.height=g;else if(-2===t){v.y=0,v.height=i;for(var x=e,k=h[e].group,D=h[e].sticky;x>0&&Tt(h[x-1].group,k)&&h[x-1].sticky===D;){var C=h[x-1];v.x-=C.width,v.width+=C.width,x--}for(var S=e;S+1<h.length&&Tt(h[S+1].group,k)&&h[S+1].sticky===D;){var E=h[S+1];v.width+=E.width,S++}if(!D){var F=Ht(h),M=v.x-F;M<0&&(v.x-=M,v.width+=M),v.x+v.width>n&&(v.width=n-v.x)}}else if(f&&t===c-1){var Z="number"===typeof p?p:p(t);v.y=r-Z,v.height=Z}else{var A=l>t?-1:1;if("number"===typeof p){var R=t-l;v.y+=R*p}else for(var I=l;I!==t;I+=A)v.y+=p(I)*A;v.height=("number"===typeof p?p:p(t))+1}return v}var rn='<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">',on=function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n<path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n<path fill-rule="evenodd" clip-rule="evenodd" d="M10.29 4.947a3.368 3.368 0 014.723.04 3.375 3.375 0 01.041 4.729l-.009.009-1.596 1.597a3.367 3.367 0 01-5.081-.364.71.71 0 011.136-.85 1.95 1.95 0 002.942.21l1.591-1.593a1.954 1.954 0 00-.027-2.733 1.95 1.95 0 00-2.732-.027l-.91.907a.709.709 0 11-1.001-1.007l.915-.911.007-.007z" fill="').concat(t,'"/>\n<path fill-rule="evenodd" clip-rule="evenodd" d="M6.55 8.678a3.368 3.368 0 015.082.364.71.71 0 01-1.136.85 1.95 1.95 0 00-2.942-.21l-1.591 1.593a1.954 1.954 0 00.027 2.733 1.95 1.95 0 002.73.028l.906-.906a.709.709 0 111.003 1.004l-.91.91-.008.01a3.368 3.368 0 01-4.724-.042 3.375 3.375 0 01-.041-4.728l.009-.009L6.55 8.678z" fill="').concat(t,'"/>\n</svg>\n  ')},an={headerRowID:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'<rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/><path d="M15.75 4h-1.5a.25.25 0 0 0-.177.074L9.308 8.838a3.75 3.75 0 1 0 1.854 1.854l1.155-1.157.967.322a.5.5 0 0 0 .65-.55l-.18-1.208.363-.363.727.331a.5.5 0 0 0 .69-.59l-.254-.904.647-.647A.25.25 0 0 0 16 5.75v-1.5a.25.25 0 0 0-.25-.25zM7.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0z" fill="').concat(t,'"/></svg>')},headerNumber:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M6.52 12.78H5.51V8.74l-1.33.47v-.87l2.29-.83h.05v5.27zm5.2 0H8.15v-.69l1.7-1.83a6.38 6.38 0 0 0 .34-.4c.09-.11.16-.22.22-.32s.1-.19.12-.27a.9.9 0 0 0 0-.56.63.63 0 0 0-.15-.23.58.58 0 0 0-.22-.15.75.75 0 0 0-.29-.05c-.27 0-.48.08-.62.23a.95.95 0 0 0-.2.65H8.03c0-.24.04-.46.13-.67a1.67 1.67 0 0 1 .97-.91c.23-.1.49-.14.77-.14.26 0 .5.04.7.11.21.08.38.18.52.32.14.13.25.3.32.48a1.74 1.74 0 0 1 .03 1.13 2.05 2.05 0 0 1-.24.47 4.16 4.16 0 0 1-.35.47l-.47.5-1 1.05h2.32v.8zm1.8-3.08h.55c.28 0 .48-.06.61-.2a.76.76 0 0 0 .2-.55.8.8 0 0 0-.05-.28.56.56 0 0 0-.13-.22.6.6 0 0 0-.23-.15.93.93 0 0 0-.32-.05.92.92 0 0 0-.29.05.72.72 0 0 0-.23.12.57.57 0 0 0-.21.46H12.4a1.3 1.3 0 0 1 .5-1.04c.15-.13.33-.23.54-.3a2.48 2.48 0 0 1 1.4 0c.2.06.4.15.55.28.15.13.27.28.36.47.08.19.13.4.13.65a1.15 1.15 0 0 1-.2.65 1.36 1.36 0 0 1-.58.49c.15.05.28.12.38.2a1.14 1.14 0 0 1 .43.62c.03.13.05.26.05.4 0 .25-.05.47-.14.66a1.42 1.42 0 0 1-.4.49c-.16.13-.35.23-.58.3a2.51 2.51 0 0 1-.73.1c-.22 0-.44-.03-.65-.09a1.8 1.8 0 0 1-.57-.28 1.43 1.43 0 0 1-.4-.47 1.41 1.41 0 0 1-.15-.66h1a.66.66 0 0 0 .22.5.87.87 0 0 0 .58.2c.25 0 .45-.07.6-.2a.71.71 0 0 0 .21-.56.97.97 0 0 0-.06-.36.61.61 0 0 0-.18-.25.74.74 0 0 0-.28-.15 1.33 1.33 0 0 0-.37-.04h-.55V9.7z" fill="').concat(t,'"/>\n  </svg>')},headerCode:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'<rect x="2" y="2" width="16" height="16" rx="4" fill="').concat(n,'"/><path d="m12.223 13.314 3.052-2.826a.65.65 0 0 0 0-.984l-3.052-2.822c-.27-.25-.634-.242-.865.022-.232.263-.206.636.056.882l2.601 2.41-2.601 2.41c-.262.245-.288.619-.056.882.231.263.595.277.865.026Zm-4.444.005c.266.25.634.241.866-.027.231-.263.206-.636-.06-.882L5.983 10l2.602-2.405c.266-.25.291-.62.06-.887-.232-.263-.596-.272-.866-.022L4.723 9.51a.653.653 0 0 0 0 .983l3.056 2.827Z" fill="').concat(t,'"/></svg>')},headerString:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path d="M8.182 12.4h3.636l.655 1.6H14l-3.454-8H9.455L6 14h1.527l.655-1.6zM10 7.44l1.36 3.651H8.64L10 7.441z" fill="').concat(t,'"/>\n</svg>')},headerBoolean:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n    <path\n        d="M16.2222 2H3.77778C2.8 2 2 2.8 2 3.77778V16.2222C2 17.2 2.8 18 3.77778 18H16.2222C17.2 18 17.9911 17.2 17.9911 16.2222L18 3.77778C18 2.8 17.2 2 16.2222 2Z"\n        fill="').concat(n,'"\n    />\n    <path\n        fill-rule="evenodd"\n        clip-rule="evenodd"\n        d="M7.66667 6.66669C5.73368 6.66669 4.16667 8.15907 4.16667 10C4.16667 11.841 5.73368 13.3334 7.66667 13.3334H12.3333C14.2663 13.3334 15.8333 11.841 15.8333 10C15.8333 8.15907 14.2663 6.66669 12.3333 6.66669H7.66667ZM12.5 12.5C13.8807 12.5 15 11.3807 15 10C15 8.61931 13.8807 7.50002 12.5 7.50002C11.1193 7.50002 10 8.61931 10 10C10 11.3807 11.1193 12.5 12.5 12.5Z"\n        fill="').concat(t,'"\n    />\n</svg>')},headerAudioUri:on,headerVideoUri:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M7 13.138a.5.5 0 00.748.434l5.492-3.138a.5.5 0 000-.868L7.748 6.427A.5.5 0 007 6.862v6.276z" fill="').concat(t,'"/>\n</svg>')},headerEmoji:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'\n    <path d="M10 5a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 9.17A4.17 4.17 0 0 1 5.83 10 4.17 4.17 0 0 1 10 5.83 4.17 4.17 0 0 1 14.17 10 4.17 4.17 0 0 1 10 14.17z" fill="').concat(t,'"/>\n    <path d="M8.33 8.21a.83.83 0 1 0-.03 ********** 0 0 0 .03-1.67zm3.34 0a.83.83 0 1 0-.04 ********** 0 0 0 .04-1.67z" fill="').concat(t,'"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.53 13.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="').concat(t,'"/>\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm0 11a5 5 0 1 1 .01-10.01A5 5 0 0 1 10 15z" fill="').concat(t,'"/>\n    <path d="M8 7.86a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2zm4 0a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2z" fill="').concat(t,'"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.53 11.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="').concat(t,'"/>\n  </svg>')},headerImage:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path opacity=".5" fill-rule="evenodd" clip-rule="evenodd" d="M12.499 10.801a.5.5 0 01.835 0l2.698 4.098a.5.5 0 01-.418.775H10.22a.5.5 0 01-.417-.775l2.697-4.098z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.07 8.934a.5.5 0 01.824 0l4.08 5.958a.5.5 0 01-.412.782h-8.16a.5.5 0 01-.413-.782l4.08-5.958zM13.75 8.333a2.083 2.083 0 100-4.166 2.083 2.083 0 000 4.166z" fill="').concat(t,'"/>\n</svg>')},headerUri:on,headerPhone:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'\n    <path fill="').concat(t,'" d="M3 3h14v14H3z"/>\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2zm-7.24 9.78h1.23c.15 0 .27.06.36.18l.98 1.28a.43.43 0 0 1-.05.58l-1.2 1.21a.45.45 0 0 1-.6.04A6.72 6.72 0 0 1 7.33 10c0-.61.1-1.2.25-1.78a6.68 6.68 0 0 1 2.12-********* 0 0 1 .6.04l1.2 1.2c.***********.05.59l-.98 1.29a.43.43 0 0 1-.36.17H8.98A5.38 5.38 0 0 0 8.67 10c0 .62.11 1.23.3 1.79z" fill="').concat(n,'"/>\n  </svg>')},headerMarkdown:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="m13.49 13.15-2.32-3.27h1.4V7h1.86v2.88h1.4l-2.34 3.27zM11 13H9v-3l-1.5 1.92L6 10v3H4V7h2l1.5 2L9 7h2v6z" fill="').concat(t,'"/>\n  </svg>')},headerDate:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path d="M14.8 4.182h-.6V3H13v1.182H7V3H5.8v1.182h-.6c-.66 0-1.2.532-1.2 1.182v9.454C4 15.468 4.54 16 5.2 16h9.6c.66 0 1.2-.532 1.2-1.182V5.364c0-.65-.54-1.182-1.2-1.182zm0 10.636H5.2V7.136h9.6v7.682z" fill="').concat(t,'"/>\n</svg>')},headerTime:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M10 4a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6zm0 10.8A4.8 4.8 0 0 1 5.2 10a4.8 4.8 0 1 1 4.8 4.8z" fill="').concat(t,'"/>\n    <path d="M10 7H9v3.93L12.5 13l.5-.8-3-1.76V7z" fill="').concat(t,'"/>\n  </svg>')},headerEmail:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 8.643a1.357 1.357 0 100 2.714 1.357 1.357 0 000-2.714zM7.357 10a2.643 2.643 0 115.286 0 2.643 2.643 0 01-5.286 0z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.589 4.898A5.643 5.643 0 0115.643 10v.5a2.143 2.143 0 01-4.286 0V8a.643.643 0 011.286 0v2.5a.857.857 0 001.714 0V10a4.357 4.357 0 10-1.708 3.46.643.643 0 01.782 1.02 5.643 5.643 0 11-5.842-9.582z" fill="').concat(t,'"/>\n</svg>')},headerReference:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'\n    <rect x="2" y="8" width="10" height="8" rx="2" fill="').concat(n,'"/>\n    <rect x="8" y="4" width="10" height="8" rx="2" fill="').concat(n,'"/>\n    <path d="M10.68 7.73V6l2.97 3.02-2.97 3.02v-1.77c-2.13 0-3.62.7-4.68 2.2.43-2.15 1.7-4.31 4.68-4.74z" fill="').concat(t,'"/>\n  </svg>')},headerIfThenElse:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <path fill="').concat(t,'" d="M4 3h12v14H4z"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.6 2A1.6 1.6 0 002 3.6v12.8A1.6 1.6 0 003.6 18h12.8a1.6 1.6 0 001.6-1.6V3.6A1.6 1.6 0 0016.4 2H3.6zm11.3 10.8a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7h-1.4a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.6-.693.117.117 0 00.1-.115V10.35a.117.117 0 00-.117-.116h-2.8a.117.117 0 00-.117.116v2.333c0 .***************.117h.117a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7H9.3a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.117a.117.117 0 00.117-.117V10.35a.117.117 0 00-.117-.117h-2.8a.117.117 0 00-.117.117v2.342c0 .*************.115a.7.7 0 01.6.693v1.4a.7.7 0 01-.7.7H5.1a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.35a.116.116 0 00.116-.117v-2.45c0-.515.418-.933.934-.933h2.917a.117.117 0 00.117-.117V6.85a.117.117 0 00-.117-.116h-2.45a.7.7 0 01-.7-.7V5.1a.7.7 0 01.7-.7h6.067a.7.7 0 01.7.7v.934a.7.7 0 01-.7.7h-2.45a.117.117 0 00-.118.116v2.333c0 .***************.117H13.5c.516 0 .934.418.934.934v2.45c0 .***************.116h.35z" fill="').concat(n,'"/>\n</svg>')},headerSingleValue:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'\n    <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n    <path d="M9.98 13.33c.45 0 .74-.3.73-.75l-.01-.1-.16-1.67 1.45 1.05a.81.81 0 0 0 .5.18c.37 0 .72-.32.72-.76 0-.3-.17-.54-.49-.68l-1.63-.77 1.63-.77c.32-.14.49-.37.49-.67 0-.45-.34-.76-.71-.76a.81.81 0 0 0-.5.18l-1.47 1.03.16-1.74.01-.08c.01-.46-.27-.76-.72-.76-.46 0-.76.32-.75.76l.01.08.16 1.74-1.47-1.03a.77.77 0 0 0-.5-.18.74.74 0 0 0-.72.76c0 .3.17.53.49.67l1.63.77-1.62.77c-.32.14-.5.37-.5.68 0 .44.35.75.72.75a.78.78 0 0 0 .5-.17L9.4 10.8l-.16 1.68v.09c-.02.44.28.75.74.75z" fill="').concat(t,'"/>\n  </svg>')},headerLookup:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'\n    <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n    <path d="M8 5.83H5.83a.83.83 0 0 0 0 1.67h1.69A4.55 4.55 0 0 1 8 5.83zm-.33 3.34H5.83a.83.83 0 0 0 0 1.66h2.72a4.57 4.57 0 0 1-.88-1.66zM5.83 12.5a.83.83 0 0 0 0 1.67h7.5a.83.83 0 1 0 0-1.67h-7.5zm8.8-2.9a3.02 3.02 0 0 0 .46-1.6c0-1.66-1.32-3-2.94-3C10.52 5 9.2 6.34 9.2 8s1.31 3 2.93 3c.58 0 1.11-.17 1.56-.47l2.04 2.08.93-.94-2.04-2.08zm-2.48.07c-.9 0-1.63-.75-1.63-1.67s.73-1.67 1.63-1.67c.9 0 1.63.75 1.63 1.67s-.73 1.67-1.63 1.67z" fill="').concat(t,'"/>\n  </svg>')},headerTextTemplate:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path d="M7.676 4.726V3l2.976 3.021-2.976 3.022v-1.77c-2.125 0-3.613.69-4.676 2.201.425-2.158 1.7-4.316 4.676-4.748zM10.182 14.4h3.636l.655 1.6H16l-3.454-8h-1.091L8 16h1.527l.655-1.6zM12 9.44l1.36 3.65h-2.72L12 9.44z" fill="').concat(t,'"/>\n</svg>')},headerMath:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.167 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666H4.167z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.083 4.167a.833.833 0 10-1.666 0v4.166a.833.833 0 101.666 0V4.167zM11.667 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666h-4.166zM5.367 11.688a.833.833 0 00-1.179 1.179l2.947 2.946a.833.833 0 001.178-1.178l-2.946-2.947z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.313 12.867a.833.833 0 10-1.178-1.179l-2.947 2.947a.833.833 0 101.179 1.178l2.946-2.946z" fill="').concat(t,'"/>\n  <path d="M10.833 12.5c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833zM10.833 15c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833z" fill="').concat(t,'"/>\n</svg>')},headerRollup:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'\n    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="').concat(n,'"/>\n    <path d="M10 8.84a1.16 1.16 0 1 0 0 2.32 1.16 1.16 0 0 0 0-2.32zm3.02 3.61a3.92 3.92 0 0 0 .78-********** 0 1 0-.95.2c.19.87-.02 1.78-.58 2.47a2.92 2.92 0 1 1-4.13-4.08 2.94 2.94 0 0 1 2.43-.62.49.49 0 1 0 .17-.96 3.89 3.89 0 1 0 2.28 6.27zM10 4.17a5.84 5.84 0 0 0-5.44 ********** 0 1 0 .9-.35 4.86 4.86 0 1 1 2.5 ********** 0 1 0-.4.88c.76.35 1.6.54 2.44.53a5.83 5.83 0 0 0 0-11.66zm3.02 3.5a.7.7 0 1 0-1.4 0 .7.7 0 0 0 1.4 0zm-6.97 5.35a.7.7 0 1 1 0 1.4.7.7 0 0 1 0-1.4z" fill="').concat(t,'"/>\n  </svg>')},headerJoinStrings:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path d="M12.4 13.565c1.865-.545 3.645-2.083 3.645-4.396 0-1.514-.787-2.604-2.071-2.604C12.69 6.565 12 7.63 12 8.939c1.114.072 1.865.726 1.865 1.683 0 .933-.8 1.647-1.84 2.023l.375.92zM4 5h6v2H4zM4 9h5v2H4zM4 13h4v2H4z" fill="').concat(t,'"/>\n</svg>')},headerSplitString:function(e){var t=e.fgColor,n=e.bgColor;return"\n    ".concat(rn,'\n    <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n    <path d="M12.4 13.56c1.86-.54 3.65-2.08 3.65-4.4 0-1.5-.8-2.6-2.08-2.6S12 7.64 12 8.95c1.11.07 1.86.73 1.86 1.68 0 .94-.8 1.65-1.83 2.03l.37.91zM4 5h6v2H4zm0 4h5v2H4zm0 4h4v2H4z" fill="').concat(t,'"/>\n  </svg>')},headerGeoDistance:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="').concat(n,'"/>\n  <path d="M10 7a1 1 0 100-2v2zm0 6a1 1 0 100 2v-2zm0-8H7v2h3V5zm-3 6h5V9H7v2zm5 2h-2v2h2v-2zm1-1a1 1 0 01-1 1v2a3 3 0 003-3h-2zm-1-1a1 1 0 011 1h2a3 3 0 00-3-3v2zM4 8a3 3 0 003 3V9a1 1 0 01-1-1H4zm3-3a3 3 0 00-3 3h2a1 1 0 011-1V5z" fill="').concat(t,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.856 12.014a.5.5 0 00-.712.702L5.409 14l-1.265 1.284a.5.5 0 00.712.702l1.255-1.274 1.255 1.274a.5.5 0 00.712-.702L6.813 14l1.265-1.284a.5.5 0 00-.712-.702L6.11 13.288l-1.255-1.274zM12.856 4.014a.5.5 0 00-.712.702L13.409 6l-1.265 1.284a.5.5 0 10.712.702l1.255-1.274 1.255 1.274a.5.5 0 10.712-.702L14.813 6l1.265-1.284a.5.5 0 00-.712-.702L14.11 5.288l-1.255-1.274z" fill="').concat(t,'"/>\n</svg>')},headerArray:function(e){var t=e.fgColor,n=e.bgColor;return"".concat(rn,'\n  <rect x="2" y="2" width="16" height="16" rx="2" fill="').concat(n,'"/>\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 7.25a.75.75 0 000-1.5h-6.5a.75.75 0 100 1.5h6.5zM15 10a.75.75 0 01-.75.75h-6.5a.75.75 0 010-1.5h6.5A.75.75 0 0115 10zm-.75 4.25a.75.75 0 000-1.5h-6.5a.75.75 0 000 1.5h6.5zm-8.987-7a.75.75 0 100-********* 0 000 1.5zm.75 2.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 4.25a.75.75 0 100-********* 0 000 1.5z" fill="').concat(t,'"/>\n</svg>')},rowOwnerOverlay:function(e){var t=e.fgColor,n=e.bgColor;return'\n    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M2 15v1h14v-2.5c0-.87-.44-1.55-.98-2.04a6.19 6.19 0 0 0-1.9-1.14 12.1 12.1 0 0 0-2.48-.67A4 4 0 1 0 5 6a4 4 0 0 0 2.36 3.65c-.82.13-1.7.36-2.48.67-.69.28-1.37.65-1.9 1.13A2.8 2.8 0 0 0 2 13.5V15z" fill="'.concat(n,'" stroke="').concat(t,'" stroke-width="2"/>\n  </svg>')},protectedColumnOverlay:function(e){var t=e.fgColor,n=e.bgColor;return'\n    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M12.43 6.04v-.18a3.86 3.86 0 0 0-7.72 0v.18A2.15 2.15 0 0 0 3 8.14v5.72C3 15.04 3.96 16 5.14 16H12c1.18 0 2.14-.96 2.14-2.14V8.14c0-1.03-.73-1.9-1.71-2.1zM7.86 6v-.14a.71.71 0 1 1 1.43 0V6H7.86z" fill="'.concat(n,'" stroke="').concat(t,'" stroke-width="2"/>\n  </svg>\n')},renameIcon:function(e){var t=e.bgColor;return"".concat(rn,'\n    <path stroke="').concat(t,'" stroke-width="2" d="M12 3v14"/>\n    <path stroke="').concat(t,'" stroke-width="2" stroke-linecap="round" d="M10 4h4m-4 12h4"/>\n    <path d="M11 14h4a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-4v2h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4v2ZM9.5 8H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h4.5v2H5a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3h4.5v2Z" fill="').concat(t,'"/>\n  </svg>\n')}};var ln=function(){function e(t,n){(0,d.Z)(this,e),this.onSettled=n,this.spriteMap=new Map,this.inFlight=0,this.headerIcons=(0,s.Z)((0,s.Z)({},an),t)}return(0,f.Z)(e,[{key:"drawSprite",value:function(e,t,n,r,i,o,a){var l=this,s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,c=function(e,t){return"normal"===e?[t.bgIconHeader,t.fgIconHeader]:"selected"===e?["white",t.accentColor]:[t.accentColor,t.bgHeader]}(t,a),d=(0,u.Z)(c,2),f=d[0],h=d[1],p=o*Math.ceil(window.devicePixelRatio),v="".concat(f,"_").concat(h,"_").concat(p,"_").concat(e),g=this.spriteMap.get(v);if(void 0===g){var m=this.headerIcons[e];if(void 0===m)return;var y=(g=document.createElement("canvas")).getContext("2d");if(null===y)return;var b=new Image;b.src="data:image/svg+xml;charset=utf-8,".concat(encodeURIComponent(m({fgColor:h,bgColor:f}))),this.spriteMap.set(v,g);var w=b.decode();if(void 0===w)return;this.inFlight++,w.then((function(){y.drawImage(b,0,0,p,p)})).finally((function(){l.inFlight--,0===l.inFlight&&l.onSettled()}))}else s<1&&(n.globalAlpha=s),n.drawImage(g,0,0,p,p,r,i,o,o),s<1&&(n.globalAlpha=1)}}]),e}(),un={},sn=null;function cn(e){var t=e.toLowerCase().trim();if(void 0!==un[t])return un[t];sn=sn||function(){var e=document.createElement("div");return e.style.opacity="0",e.style.pointerEvents="none",e.style.position="fixed",document.body.append(e),e}(),sn.style.color="#000",sn.style.color=t;var n=getComputedStyle(sn).color;sn.style.color="#fff",sn.style.color=t;var r=getComputedStyle(sn).color;if(r!==n)return[0,0,0,1];var i=r.replace(/[^\d.,]/g,"").split(",").map(Number.parseFloat);return i.length<4&&i.push(1),i=i.map((function(t){var n=Number.isNaN(t);return n&&console.warn("Could not parse color",e),n?0:t})),un[t]=i,i}function dn(e,t){var n=cn(e),r=(0,u.Z)(n,3),i=r[0],o=r[1],a=r[2];return"rgba(".concat(i,", ").concat(o,", ").concat(a,", ").concat(t,")")}function fn(e,t){if(void 0===t)return e;var n=cn(e),r=(0,u.Z)(n,4),i=r[0],o=r[1],a=r[2],l=r[3];if(1===l)return e;var s=cn(t),c=(0,u.Z)(s,4),d=c[0],f=c[1],h=c[2],p=c[3],v=l+p*(1-l),g=(l*o+p*f*(1-l))/v,m=(l*a+p*h*(1-l))/v;return"rgba(".concat((l*i+p*d*(1-l))/v,", ").concat(g,", ").concat(m,", ").concat(v,")")}function hn(e,t,n){if(n<=0)return e;if(n>=1)return t;var r=(0,l.Z)(cn(e));r[0]=r[0]*r[3],r[1]=r[1]*r[3],r[2]=r[2]*r[3];var i=(0,l.Z)(cn(t));i[0]=i[0]*i[3],i[1]=i[1]*i[3],i[2]=i[2]*i[3];var o=n,a=1-n,u=r[3]*a+i[3]*o,s=Math.floor((r[0]*a+i[0]*o)/u),c=Math.floor((r[1]*a+i[1]*o)/u),d=Math.floor((r[2]*a+i[2]*o)/u);return"rgba(".concat(s,", ").concat(c,", ").concat(d,", ").concat(u,")")}var pn={kind:Te.Loading,allowOverlay:!1};function vn(e,t,n,r,i,o,a,l,u,s,c,d,f,h,p,v,g,m,y,b){var w,x;void 0!==p&&p[0][0]===r&&p[0][1]===t&&(w=p[1][0],x=p[1][1]);var k=void 0,D={ctx:e,theme:s,col:r,row:t,cell:n,rect:{x:i,y:o,width:a,height:l},highlighted:u,hoverAmount:h,hoverX:w,hoverY:x,imageLoader:d,spriteManager:f,hyperWrapping:v,requestAnimationFrame:function(){C=!0}},C=!1,S=function(e,t,n,r,i){var o=e.ctx,a=e.rect,l=e.theme,u=Number.MAX_SAFE_INTEGER;if(void 0!==t&&(u=n-t)<500){var s=1-u/500;o.globalAlpha=s,o.fillStyle=l.bgSearchResult,o.fillRect(a.x,a.y,a.width,a.height),o.globalAlpha=1,void 0!==r&&(r.fillStyle=l.bgSearchResult)}return i(),u<500}(D,n.lastUpdated,g,m,(function(){var e,t;if(!(!Je(n)&&!0===(null==c?void 0:c(D)))){var r=b(n);if(void 0!==r){(null==m?void 0:m.renderer)!==r&&(null==(e=null==m?void 0:m.deprep)||e.call(m,D),m=void 0);var i=null==(t=r.drawPrep)?void 0:t.call(r,D,m);r.draw(D,n),k={deprep:null==i?void 0:i.deprep,fillStyle:null==i?void 0:i.fillStyle,font:null==i?void 0:i.font,renderer:r}}}}));return(S||C)&&(null==y||y([r,t])),k}function gn(e,t,n,r,i,o,l,u,s,c,d,f,h,p,v,g,m){var y,b,w,x=arguments.length>17&&void 0!==arguments[17]&&arguments[17];if(void 0!==s){e.beginPath(),e.save(),e.rect(0,0,o,l);var k,D=(0,a.Z)(s);try{for(D.s();!(k=D.n()).done;){var C=k.value;e.rect(C.x+1,C.y+1,C.width-1,C.height-1)}}catch(te){D.e(te)}finally{D.f()}e.clip("evenodd")}var S=null!=(y=m.horizontalBorderColor)?y:m.borderColor,E=m.borderColor,F=0,M=o,Z=0,R=l;if(void 0!==u&&u.length>0){F=Number.MAX_SAFE_INTEGER,Z=Number.MAX_SAFE_INTEGER,M=Number.MIN_SAFE_INTEGER,R=Number.MIN_SAFE_INTEGER;var I,T=(0,a.Z)(u);try{for(T.s();!(I=T.n()).done;){var O=I.value;F=Math.min(F,O.x-1),M=Math.max(M,O.x+O.width+1),Z=Math.min(Z,O.y-1),R=Math.max(R,O.y+O.height+1)}}catch(te){T.e(te)}finally{T.f()}}var P=[];e.beginPath();for(var H=.5,L=0;L<t.length;L++){var z=t[L];if(0!==z.width){H+=z.width;var B=z.sticky?H:H+r;B>=F&&B<=M&&p(L+1)&&P.push({x1:B,y1:Math.max(c,Z),x2:B,y2:Math.min(l,R),color:E})}}var _=f(g-1),V=l-_+.5,N="sticky"===v;if(N&&P.push({x1:F,y1:V,x2:M,y2:V,color:S}),!0!==x)for(var W=d+.5,j=n,U=N?l-_:l;W+i<=U;){var X=W+i;if(X>=Z&&X<=R-1&&(!N||j!==g-1||Math.abs(X-V)>1)){var Y=null==h?void 0:h(j);P.push({x1:F,y1:X,x2:M,y2:X,color:null!=(w=null!=(b=null==Y?void 0:Y.horizontalBorderColor)?b:null==Y?void 0:Y.borderColor)?w:S})}W+=f(j),j++}for(var K=A(P,(function(e){return e.color})),$=0,G=Object.keys(K);$<G.length;$++){var q=G[$];e.strokeStyle=q;var Q,J=(0,a.Z)(K[q]);try{for(J.s();!(Q=J.n()).done;){var ee=Q.value;e.moveTo(ee.x1,ee.y1),e.lineTo(ee.x2,ee.y2)}}catch(te){J.e(te)}finally{J.f()}e.stroke(),e.beginPath()}void 0!==s&&e.restore()}function mn(e,t){for(var n=[],r=e.x+e.width-26*t.length,i=e.y+e.height/2-13,o=0;o<t.length;o++)n.push({x:r,y:i,width:26,height:26}),r+=26;return n}function yn(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}var bn=30;function wn(e,t,n,r,i){return i?{x:e,y:t,width:bn,height:Math.min(bn,r)}:{x:e+n-bn,y:Math.max(t,t+r/2-bn/2),width:bn,height:Math.min(bn,r)}}function xn(e,t,n,r,i,o,a,l,u,c,d,f,h,p){var v=o.title.startsWith(Ue),g="rtl"===At(o.title),m=wn(t,n,r,i,g);if(void 0!==h){var y=o;if(v&&(y=(0,s.Z)((0,s.Z)({},o),{},{title:""})),h({ctx:e,theme:l,rect:{x:t,y:n,width:r,height:i},column:y,columnIndex:y.sourceIndex,isSelected:a,hoverAmount:d,isHovered:u,hasSelectedCell:c,spriteManager:f,menuBounds:m}))return}if(v){var b=void 0;return o.title===Xe&&(b=!0),o.title===Ye&&(b=!1),!0!==b&&(e.globalAlpha=d),qt(e,l,b,t,n,r,i,!1,void 0,void 0,18),void(!0!==b&&(e.globalAlpha=1))}var w=l.cellHorizontalPadding,x=a?l.textHeaderSelected:l.textHeader,k=!0===o.hasMenu&&(u||p&&a),D=g?-1:1,C=g?t+r-w:t+w;if(void 0!==o.icon){var S=a?"selected":"normal";"highlight"===o.style&&(S=a?"selected":"special");var E=l.headerIconSize;f.drawSprite(o.icon,S,e,g?C-E:C,n+(i-E)/2,E,l),void 0!==o.overlayIcon&&f.drawSprite(o.overlayIcon,a?"selected":"special",e,g?C-E+9:C+9,n+((i-18)/2+6),18,l),C+=Math.ceil(1.3*E)*D}if(k&&!0===o.hasMenu&&r>35){var F=(g?35:r-35)/r,M=(g?24.5:r-24.5)/r,Z=e.createLinearGradient(t,0,t+r,0),A=dn(x,0);Z.addColorStop(g?1:0,x),Z.addColorStop(F,x),Z.addColorStop(M,A),Z.addColorStop(g?0:1,A),e.fillStyle=Z}else e.fillStyle=x;if(g&&(e.textAlign="right"),e.fillText(o.title,C,n+i/2+Wt(e,"".concat(l.headerFontStyle," ").concat(l.fontFamily))),g&&(e.textAlign="left"),k&&!0===o.hasMenu){e.beginPath();var R=m.x+m.width/2-5.5,I=m.y+m.height/2-3;!function(e,t,n){for(var r,i=function(e,t){var n=t.x-e.x,r=t.y-e.y,i=Math.sqrt(n*n+r*r),o=n/i,a=r/i;return{x:n,y:t.y-e.y,len:i,nx:o,ny:a,ang:Math.atan2(a,o)}},o=t.length,a=t[o-1],l=0;l<o;l++){var u=t[l%o],s=t[(l+1)%o],c=i(u,a),d=i(u,s),f=c.nx*d.ny-c.ny*d.nx,h=c.nx*d.nx-c.ny*-d.ny,p=Math.asin(f<-1?-1:f>1?1:f),v=1,g=!1;h<0?p<0?p=Math.PI+p:(p=Math.PI-p,v=-1,g=!0):p>0&&(v=-1,g=!0),r=void 0!==u.radius?u.radius:n;var m=p/2,y=Math.abs(Math.cos(m)*r/Math.sin(m)),b=void 0;y>Math.min(c.len/2,d.len/2)?(y=Math.min(c.len/2,d.len/2),b=Math.abs(y*Math.sin(m)/Math.cos(m))):b=r;var w=u.x+d.nx*y,x=u.y+d.ny*y;w+=-d.ny*b*v,x+=d.nx*b*v,e.arc(w,x,b,c.ang+Math.PI/2*v,d.ang-Math.PI/2*v,g),a=u,u=s}e.closePath()}(e,[{x:R,y:I},{x:R+11,y:I},{x:R+5.5,y:I+6}],1),e.fillStyle=x,e.fill()}}function kn(e,t,n,r,i,o,a,l,c,d,f,h,p,v,g,m,y,b,w){var x,k=a+l;if(!(k<=0)){e.fillStyle=h.bgHeader,e.fillRect(0,0,i,k);var D=null!=(x=null==r?void 0:r[0])?x:[],C=(0,u.Z)(D,2),S=C[0],E=C[1],F="".concat(h.headerFontStyle," ").concat(h.fontFamily);e.font=F,An(t,0,o,0,k,(function(t,r,i,o){var u,g,x;if(void 0===y||y.some((function(e){return-1===e[1]&&e[0]===t.sourceIndex}))){var k=Math.max(0,o-r);e.save(),e.beginPath(),e.rect(r+k,l,t.width-k,a),e.clip();var D=m(null!=(u=t.group)?u:"").overrideTheme,C=void 0===t.themeOverride&&void 0===D?h:(0,s.Z)((0,s.Z)((0,s.Z)({},h),D),t.themeOverride);C.bgHeader!==h.bgHeader&&(e.fillStyle=C.bgHeader,e.fill());var M="".concat(C.headerFontStyle," ").concat(C.fontFamily);F!==M&&(e.font=M);var Z=f.columns.hasIndex(t.sourceIndex),A=void 0!==c||d,R=!A&&-1===E&&S===t.sourceIndex,I=A?0:null!=(x=null==(g=v.find((function(e){return e.item[0]===t.sourceIndex&&-1===e.item[1]})))?void 0:g.hoverAmount)?x:0,T=void 0!==(null==f?void 0:f.current)&&f.current.cell[0]===t.sourceIndex,O=Z?C.accentColor:T?C.bgHeaderHasFocus:C.bgHeader,P=n?l:0,H=0===t.sourceIndex?0:1;Z?(e.fillStyle=O,e.fillRect(r+H,P,t.width-H,a)):(T||I>0)&&(e.beginPath(),e.rect(r+H,P,t.width-H,a),T&&(e.fillStyle=C.bgHeaderHasFocus,e.fill()),I>0&&(e.globalAlpha=I,e.fillStyle=C.bgHeaderHovered,e.fill(),e.globalAlpha=1)),xn(e,r,P,t.width,a,t,Z,C,R,T,I,p,b,w),e.restore()}})),n&&function(e,t,n,r,i,o,a,l,c,d,f,h){var p,v=null!=(p=null==o?void 0:o[0])?p:[],g=(0,u.Z)(v,2),m=g[0],y=g[1],b=0;Rn(t,n,r,i,(function(t,n,r,c,p,v){var g,w;if(void 0===h||h.some((function(e){return-2===e[1]&&e[0]>=t[0]&&e[0]<=t[1]}))){e.save(),e.beginPath(),e.rect(r,c,p,v),e.clip();var x=f(n),k=void 0===(null==x?void 0:x.overrideTheme)?a:(0,s.Z)((0,s.Z)({},a),x.overrideTheme),D=-2===y&&void 0!==m&&m>=t[0]&&m<=t[1],C=D?k.bgHeaderHovered:k.bgHeader;if(C!==a.bgHeader&&(e.fillStyle=C,e.fill()),e.fillStyle=null!=(g=k.textGroupHeader)?g:k.textHeader,void 0!==x){var S=r;if(void 0!==x.icon&&(l.drawSprite(x.icon,"normal",e,S+8,(i-20)/2,20,k),S+=26),e.fillText(x.name,S+8,i/2+Wt(e,"".concat(a.headerFontStyle," ").concat(a.fontFamily))),void 0!==x.actions&&D){var E=mn({x:r,y:c,width:p,height:v},x.actions);e.beginPath();var F=E[0].x-10,M=r+p-F;e.rect(F,0,M,i);var Z=e.createLinearGradient(F,0,F+M,0),A=dn(C,0);Z.addColorStop(0,A),Z.addColorStop(10/M,C),Z.addColorStop(1,C),e.fillStyle=Z,e.fill(),e.globalAlpha=.6;for(var R=null!=(w=null==o?void 0:o[1])?w:[-1,-1],I=(0,u.Z)(R,2),T=I[0],O=I[1],P=0;P<x.actions.length;P++){var H=x.actions[P],L=E[P],z=yn(L,T+r,O);z&&(e.globalAlpha=1),l.drawSprite(H.icon,"normal",e,L.x+L.width/2-10,L.y+L.height/2-10,20,k),z&&(e.globalAlpha=.6)}e.globalAlpha=1}}0!==r&&d(t[0])&&(e.beginPath(),e.moveTo(r+.5,0),e.lineTo(r+.5,i),e.strokeStyle=a.borderColor,e.lineWidth=1,e.stroke()),e.restore(),b=r+p}})),e.beginPath(),e.moveTo(b+.5,0),e.lineTo(b+.5,i),e.moveTo(0,i+.5),e.lineTo(n,i+.5),e.strokeStyle=a.borderColor,e.lineWidth=1,e.stroke()}(e,t,i,o,l,r,h,p,0,g,m,y)}}function Dn(e,t,n,r,i,o,a,l){return e<=i+a&&i<=e+n&&t<=o+l&&o<=t+r}function Cn(e,t,n,r,i,o,a,l,u,s,c,d,f,h){if(void 0!==f&&0!==f.length){var p="sticky"===d?c(s-1):0;e.beginPath(),Rn(t,n,a,i,(function(t,n,r,i,o,a){for(var l=0;l<f.length;l++){var u=f[l];if(-2===u[1]&&u[0]>=t[0]&&u[0]<=t[1]){e.rect(r,i,o,a);break}}})),An(t,u,a,l,o,(function(t,n,a,l,u){for(var v=Math.max(0,l-n),g=n+v+1,m=t.width-v-1,y=0;y<f.length;y++){var b=f[y];if(b[0]===t.sourceIndex&&(-1===b[1]||void 0===b[1])){e.rect(g,i,m,o-i);break}}h&&Zn(u,a,r,s,c,d,(function(n,i,o,a){for(var l=!1,u=0;u<f.length;u++){var s=f[u];if(s[0]===t.sourceIndex&&s[1]===i){l=!0;break}}if(l){var c=n+1,d=(a?c+o-1:Math.min(c+o-1,r-p))-c;d>0&&e.rect(g,c,m,d)}}))})),e.clip()}}function Sn(e,t,n,r,i,o,a){var l,s,c,d,f=(0,u.Z)(e,2),h=f[0],p=f[1],v=null!=(s=null==(l=a.find((function(e){return!e.sticky})))?void 0:l.sourceIndex)?s:0;if(p>v){for(var g=Math.max(h,v),m=t,y=r,b=o.sourceIndex-1;b>=g;b--)m-=a[b].width,y+=a[b].width;for(var w=o.sourceIndex+1;w<=p;w++)y+=a[w].width;d={x:m,y:n,width:y,height:i}}if(v>h){for(var x=Math.min(p,v-1),k=t,D=r,C=o.sourceIndex-1;C>=h;C--)k-=a[C].width,D+=a[C].width;for(var S=o.sourceIndex+1;S<=x;S++)D+=a[S].width;c={x:k,y:n,width:D,height:i}}return[c,d]}function En(e,t,n,r,i,o,l,c,d,f,h,p,v,g,m,y,b,w,x,k,D,C,S,E,F,M,Z,A,R,I,T){var O,P,H=null!=(O=null==x?void 0:x.length)?O:Number.MAX_SAFE_INTEGER,L=performance.now(),z="".concat(R.baseFontStyle," ").concat(R.fontFamily);e.font=z;var B=new Set;return An(t,c,o,l,i,(function(t,o,l,c,O){var _,V=Math.max(0,c-o),N=o+V,W=i+1,j=t.width-V,U=r-i-1;if(w.length>0){for(var X=!1,Y=0;Y<w.length;Y++){var K=w[Y];if(Dn(N,W,j,U,K.x,K.y,K.width,K.height)){X=!0;break}}if(!X)return}var $=function(){e.save(),e.beginPath(),e.rect(N,W,j,U),e.clip()},G=k.columns.hasIndex(t.sourceIndex),q=p(null!=(_=t.group)?_:"").overrideTheme,Q=void 0===t.themeOverride&&void 0===q?R:(0,s.Z)((0,s.Z)((0,s.Z)({},R),q),t.themeOverride),J="".concat(Q.baseFontStyle," ").concat(Q.fontFamily);J!==z&&(z=J,e.font=J),$();var ee=void 0;return Zn(O,l,r,d,f,b,(function(r,i,l,f,p){var b,O,_,V;if(!(i<0)){if(void 0!==x){for(var N=!1,W=0;W<x.length;W++){var j=x[W];if(j[0]===t.sourceIndex&&j[1]===i){N=!0;break}}if(!N)return}if(w.length>0){for(var U=!1,X=0;X<w.length;X++){var Y=w[X];if(Dn(o,r,t.width,l,Y.x,Y.y,Y.width,Y.height)){U=!0;break}}if(!U)return}var K=k.rows.hasIndex(i),q=g.hasIndex(i),te=i<d?h([t.sourceIndex,i]):pn,ne=o,re=t.width,ie=!1,oe=!1;if(void 0!==te.span){var ae=(0,u.Z)(te.span,2),le=ae[0],ue=ae[1],se="".concat(i,",").concat(le,",").concat(ue,",").concat(t.sticky);if(B.has(se))return void H--;var ce=Sn(te.span,o,r,t.width,l,t,n),de=t.sticky?ce[0]:ce[1];if(t.sticky||void 0===ce[0]||(oe=!0),void 0!==de){ne=de.x,re=de.width,B.add(se),e.restore(),ee=void 0,e.save(),e.beginPath();var fe=Math.max(0,c-de.x);e.rect(de.x+fe,r,de.width-fe,l),void 0===P&&(P=[]),P.push({x:de.x+fe,y:r,width:de.width-fe,height:l}),e.clip(),ie=!0}}var he=null==v?void 0:v(i),pe=p&&void 0!==(null==(b=t.trailingRowOptions)?void 0:b.themeOverride)?null==(O=t.trailingRowOptions)?void 0:O.themeOverride:void 0,ve=void 0===te.themeOverride&&void 0===he&&void 0===pe?Q:(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},Q),he),pe),te.themeOverride);e.beginPath();var ge=[t.sourceIndex,i],me=function(e,t,n){if(void 0===(null==n?void 0:n.current))return!1;var r=(0,u.Z)(n.current.cell,2),i=r[0],o=r[1],a=(0,u.Z)(e,2),l=a[0];return a[1]===o&&(void 0===t.span?i===l:i>=t.span[0]&&i<=t.span[1])}(ge,te,k),ye=function(e,t,n){var r=0;if(void 0===n.current)return r;Ot(e,t,n.current.range)&&r++;var i,o=(0,a.Z)(n.current.rangeStack);try{for(o.s();!(i=o.n()).done;)Ot(e,t,i.value)&&r++}catch(l){o.e(l)}finally{o.f()}return r}(ge,te,k),be=void 0!==te.span&&k.columns.some((function(e){return void 0!==te.span&&e>=te.span[0]&&e<=te.span[1]}));me&&!m&&y?ye=0:me&&(ye=Math.max(ye,1)),be&&ye++,me||(K&&ye++,G&&!f&&ye++);var we,xe=te.kind===Te.Protected?ve.bgCellMedium:ve.bgCell;if((f||xe!==R.bgCell)&&(we=fn(xe,we)),ye>0||q){q&&(we=fn(ve.bgHeader,we));for(var ke=0;ke<ye;ke++)we=fn(ve.accentLight,we)}else!0===(null==D?void 0:D.some((function(e){return e[0]===t.sourceIndex&&e[1]===i})))&&(we=fn(ve.bgSearchResult,we));if(void 0!==C){var De,Ce=(0,a.Z)(C);try{for(Ce.s();!(De=Ce.n()).done;){var Se=De.value,Ee=Se.range;Ee.x<=t.sourceIndex&&t.sourceIndex<Ee.x+Ee.width&&Ee.y<=i&&i<Ee.y+Ee.height&&(we=fn(Se.color,we))}}catch(Ze){Ce.e(Ze)}finally{Ce.f()}}void 0!==we&&(e.fillStyle=we,void 0!==ee&&(ee.fillStyle=we),e.fillRect(ne,r,re,l)),"faded"===te.style&&(e.globalAlpha=.6);var Fe=M.find((function(e){return e.item[0]===t.sourceIndex&&e.item[1]===i}));if(re>10&&!oe){var Me="".concat(ve.baseFontStyle," ").concat(ve.fontFamily);Me!==z&&(e.font=Me,z=Me),ee=vn(e,i,te,t.sourceIndex,ne,r,re,l,ye>0,ve,S,E,F,null!=(_=null==Fe?void 0:Fe.hoverAmount)?_:0,Z,A,L,ee,I,T)}return"faded"===te.style&&(e.globalAlpha=1),H--,ie&&(e.restore(),null==(V=null==ee?void 0:ee.deprep)||V.call(ee,{ctx:e}),ee=void 0,$(),z=J,e.font=J),H<=0}})),e.restore(),H<=0})),P}function Fn(e,t,n,r,i,o,a,l,s,c,d,f,h,p,v,g){var m;if(void 0!==d.current&&a.some((function(e){var t;return e.sourceIndex===(null==(t=d.current)?void 0:t.cell[0])}))){var y=(0,u.Z)(d.current.cell,2),b=y[0],w=y[1],x=h(d.current.cell),k=null!=(m=x.span)?m:[b,b],D="sticky"!==p||"sticky"===p&&w===g-1?0:f(g-1)-1,C=void 0;if(An(a,r,i,o,c,(function(r,i,o,a,u){if(!(r.sticky&&b>r.sourceIndex)&&!(r.sourceIndex<k[0]||r.sourceIndex>k[1]))return Zn(u,o,n,g,f,p,(function(o,u,c){if(u===w){var d=i,f=r.width;if(void 0!==x.span){var h=Sn(x.span,i,o,r.width,c,r,l),p=r.sticky?h[0]:h[1];void 0!==p&&(d=p.x,f=p.width)}return C=function(){var i,l,u,h;a>d&&!r.sticky&&(e.beginPath(),e.rect(a,0,t-a,n),e.clip()),e.beginPath(),e.rect(d+.5,o+.5,f,c),e.strokeStyle=null!=(l=null==(i=r.themeOverride)?void 0:i.accentColor)?l:s.accentColor,e.lineWidth=1,e.stroke(),v&&(e.beginPath(),e.rect(d+f-4,o+c-4,4,4),e.fillStyle=null!=(h=null==(u=r.themeOverride)?void 0:u.accentColor)?h:s.accentColor,e.fill())},!0}})),!0})),void 0!==C){var S=function(){e.save(),e.beginPath(),e.rect(0,c,t,n-c-D),e.clip(),null==C||C(),e.restore()};return S(),S}}}function Mn(e,t){var n,r,i=e.canvas,o=e.headerCanvas,l=e.width,d=e.height,f=e.cellXOffset,h=e.cellYOffset,p=e.translateX,v=e.translateY,g=e.mappedColumns,m=e.enableGroups,y=e.freezeColumns,b=e.dragAndDropState,w=e.theme,x=e.drawFocus,k=e.headerHeight,D=e.groupHeaderHeight,C=e.disabledRows,S=e.rowHeight,E=e.verticalBorder,F=e.isResizing,M=e.selection,Z=e.fillHandle,A=e.lastRowSticky,R=e.rows,I=e.getCellContent,T=e.getGroupDetails,O=e.getRowThemeOverride,P=e.isFocused,H=e.drawCustomCell,L=e.drawHeaderCallback,z=e.prelightCells,B=e.highlightRegions,_=e.imageLoader,V=e.lastBlitData,N=e.hoverValues,W=e.hyperWrapping,j=e.hoverInfo,U=e.spriteManager,X=e.scrolling,Y=e.touchMode,K=e.enqueue,$=e.getCellRenderer,G=e.renderStrategy,q=e.bufferA,Q=e.bufferB,J=e.damage;if(0!==l&&0!==d){var ee="double-buffer"===G,te=X?1:Math.ceil(null!=(n=window.devicePixelRatio)?n:1),ne="direct"!==G&&function(e,t){if(void 0===t)return!1;if(e.width!==t.width||e.height!==t.height||e.theme!==t.theme||e.headerHeight!==t.headerHeight||e.rowHeight!==t.rowHeight||e.rows!==t.rows||e.getRowThemeOverride!==t.getRowThemeOverride||e.isFocused!==t.isFocused||e.isResizing!==t.isResizing||e.verticalBorder!==t.verticalBorder||e.getCellContent!==t.getCellContent||e.highlightRegions!==t.highlightRegions||e.selection!==t.selection||e.dragAndDropState!==t.dragAndDropState||e.prelightCells!==t.prelightCells||e.touchMode!==t.touchMode||e.scrolling!==t.scrolling)return!1;if(e.mappedColumns!==t.mappedColumns){if(e.mappedColumns.length>100||e.mappedColumns.length!==t.mappedColumns.length)return!1;for(var n,r=0;r<e.mappedColumns.length;r++){var i=e.mappedColumns[r],o=t.mappedColumns[r];if(!Ee(i,o)){if(void 0!==n)return!1;if(i.width===o.width)return!1;i.width;var a=(0,c.Z)(i,ye);if(o.width,!Ee(a,(0,c.Z)(o,be)))return!1;n=r}}return void 0===n||n}return!0}(e,t);i.width===l*te&&i.height===d*te||(i.width=l*te,i.height=d*te,i.style.width=l+"px",i.style.height=d+"px");var re=o,ie=m?D+k:k,oe=ie+1;re.width===l*te&&re.height===oe*te||(re.width=l*te,re.height=oe*te,re.style.width=l+"px",re.style.height=oe+"px"),!ee||q.width===l*te&&q.height===d*te||(q.width=l*te,q.height=d*te),!ee||Q.width===l*te&&Q.height===d*te||(Q.width=l*te,Q.height=d*te);var ae=V.current;if(!0!==ne||f!==(null==ae?void 0:ae.cellXOffset)||h!==(null==ae?void 0:ae.cellYOffset)||p!==(null==ae?void 0:ae.translateX)||v!==(null==ae?void 0:ae.translateY)){var le=null;ee&&(le=i.getContext("2d",{alpha:!1}));var ue,se=re.getContext("2d",{alpha:!1}),ce=(ue=ee?void 0!==J?"b"===(null==ae?void 0:ae.lastBuffer)?Q:q:"b"===(null==ae?void 0:ae.lastBuffer)?q:Q:i).getContext("2d",{alpha:!1}),de=ee?ue===q?Q:q:i;if(null!==se&&null!==ce){var fe="number"===typeof S?function(){return S}:S;se.save(),se.beginPath(),ce.save(),ce.beginPath(),se.textBaseline="middle",ce.textBaseline="middle",1!==te&&(se.scale(te,te),ce.scale(te,te));var he=Lt(g,f,l,b,p),pe=[],ve=x&&(null==(r=M.current)?void 0:r.cell[1])===h&&0===v,ge=function(){var e,t;kn(se,he,m,j,l,p,k,D,b,F,M,w,U,N,E,T,J,L,Y),gn(se,he,h,p,v,l,d,void 0,void 0,D,ie,fe,O,E,A,R,w,!0),se.beginPath(),se.moveTo(0,oe-.5),se.lineTo(l,oe-.5),se.strokeStyle=fn(null!=(t=null!=(e=w.headerBottomBorderColor)?e:w.horizontalBorderColor)?t:w.borderColor,w.bgHeader),se.stroke(),ve&&Fn(se,l,d,h,p,v,he,g,w,ie,M,fe,I,A,Z,R)};if(void 0!==J){var me=!1;return(J=J.filter((function(e){return me=me||e[1]<0,e[1]<0||Dn(f,h,he.length,300,e[0],e[1],1,1)||Dn(0,h,y,300,e[0],e[1],1,1)||A&&Dn(f,R-1,he.length,1,e[0],e[1],1,1)}))).length>0&&(Cn(ce,he,l,d,D,ie,p,v,h,R,fe,A,J,!0),ce.fillStyle=w.bgCell,ce.fillRect(0,ie+1,l,d-ie-1),En(ce,he,g,d,ie,p,v,h,R,fe,I,T,O,C,P,x,A,pe,J,M,z,B,H,_,U,N,j,W,w,K,$),Z&&x&&void 0!==M.current&&J.some((function(e){var t,n;return e[0]===(null==(t=M.current)?void 0:t.cell[0])&&e[1]===(null==(n=M.current)?void 0:n.cell[1])}))&&Fn(ce,l,d,h,p,v,he,g,w,ie,M,fe,I,A,Z,R)),me&&(Cn(se,he,l,ie,D,ie,p,v,h,R,fe,A,J,!1),ge()),ce.restore(),se.restore(),void(null!==le&&(le.fillStyle=w.bgCell,le.fillRect(0,0,l,d),le.drawImage(ce.canvas,0,0)))}if(!0===ne&&f===(null==ae?void 0:ae.cellXOffset)&&p===(null==ae?void 0:ae.translateX)&&ve===(null==ae?void 0:ae.mustDrawFocusOnHeader)||ge(),!0===ne){De(void 0!==de&&void 0!==ae);var we=function(e,t,n,r,i,o,a,l,u,s,c,d,f,h,p,v,g){var m=[],y=!1;e.imageSmoothingEnabled=!1;var b=Math.min(n.cellYOffset,i),w=Math.max(n.cellYOffset,i),x=0;if("number"===typeof v)x+=(w-b)*v;else for(var k=b;k<w;k++)x+=v(k);i>n.cellYOffset&&(x=-x),x+=a-n.translateY;for(var D=Math.min(n.cellXOffset,r),C=Math.max(n.cellXOffset,r),S=0,E=D;E<C;E++)S+=h[E].width;r>n.cellXOffset&&(S=-S),S+=o-n.translateX;var F=Ht(p);if(F>0&&F++,0!==S&&0!==x)return{regions:[],yOnly:!1};var M=l?"number"===typeof v?v:v(c-1):0,Z=u-F-Math.abs(S),A=s-d-M-Math.abs(x)-1;if(Z>150&&A>150){y=0===S;var R={sx:0,sy:0,sw:u*f,sh:s*f,dx:0,dy:0,dw:u*f,dh:s*f};x>0?(R.sy=(d+1)*f,R.sh=A*f,R.dy=(x+d+1)*f,R.dh=A*f,m.push({x:0,y:d,width:u,height:x+1})):x<0&&(R.sy=(-x+d+1)*f,R.sh=A*f,R.dy=(d+1)*f,R.dh=A*f,m.push({x:0,y:s+x-M,width:u,height:-x+M})),S>0?(R.sx=F*f,R.sw=Z*f,R.dx=(S+F)*f,R.dw=Z*f,m.push({x:F-1,y:0,width:S+2,height:s})):S<0&&(R.sx=(F-S)*f,R.sw=Z*f,R.dx=F*f,R.dw=Z*f,m.push({x:u+S,y:0,width:-S,height:s})),e.setTransform(1,0,0,1,0,0),F>0&&0!==S&&0===x&&g&&e.drawImage(t,0,0,F*f,s*f,0,0,F*f,s*f),e.drawImage(t,R.sx,R.sy,R.sw,R.sh,R.dx,R.dy,R.dw,R.dh),e.scale(f,f)}return e.imageSmoothingEnabled=!0,{regions:m,yOnly:y}}(ce,de,ae,f,h,p,v,"sticky"===A,l,d,R,ie,te,g,he,S,ee);pe=we.regions}else if(!1!==ne){De(void 0!==ae),pe=function(e,t,n,r,i,o,a,l,u,s){var c=[];return t!==e.cellXOffset||n!==e.cellYOffset||r!==e.translateX||i!==e.translateY||An(u,n,r,i,l,(function(e,t,n,r){if(e.sourceIndex===s){var i=Math.max(t,r)+1;return c.push({x:i,y:0,width:o-i,height:a}),!0}})),c}(ae,f,h,p,v,l,d,ie,he,ne)}!function(e,t,n,r,i,o,l,u,s){var c,d,f=!1,h=(0,a.Z)(t);try{for(h.s();!(d=h.n()).done;){var p=d.value;if(!p.sticky){f=l(p.sourceIndex);break}}}catch(b){h.e(b)}finally{h.f()}var v=null!=(c=s.horizontalBorderColor)?c:s.borderColor,g=s.borderColor,m=f?Ht(t):0;if(0!==m&&(e.beginPath(),e.moveTo(m+.5,0),e.lineTo(m+.5,r),e.strokeStyle=fn(g,s.bgCell),e.stroke()),i){var y=u(o-1);e.beginPath(),e.moveTo(0,r-y+.5),e.lineTo(n,r-y+.5),e.strokeStyle=fn(v,s.bgCell),e.stroke()}}(ce,he,l,d,"sticky"===A,R,E,fe,w);var xe=x?Fn(ce,l,d,h,p,v,he,g,w,ie,M,fe,I,A,Z,R):void 0,ke=function(e,t,n,r,i,o,l,s,c,d,f,h,p,v,g){var m=null==g?void 0:g.filter((function(e){return"no-outline"!==e.style}));if(void 0!==m&&0!==m.length){var y=m.map((function(e){var a,u,g,m,y,b=e.range,w=nn(b.x,b.y,t,n,f,d+f,r,i,o,l,v,c,p,s,h);if(1===b.width&&1===b.height)return b.x<c?[{color:e.color,style:null!=(a=e.style)?a:"dashed",rect:w},void 0]:[void 0,{color:e.color,style:null!=(u=e.style)?u:"dashed",rect:w}];var x=nn(b.x+b.width-1,b.y+b.height-1,t,n,f,d+f,r,i,o,l,v,c,p,s,h);if(b.x<c&&b.x+b.width>=c){var k=nn(c-1,b.y+b.height-1,t,n,f,d+f,r,i,o,l,v,c,p,s,h),D=nn(c,b.y+b.height-1,t,n,f,d+f,r,i,o,l,v,c,p,s,h);return[{color:e.color,style:null!=(g=e.style)?g:"dashed",rect:{x:w.x,y:w.y,width:k.x+k.width-w.x,height:k.y+k.height-w.y}},{color:e.color,style:null!=(m=e.style)?m:"dashed",rect:{x:D.x,y:D.y,width:x.x+x.width-D.x,height:x.y+x.height-D.y}}]}return[void 0,{color:e.color,style:null!=(y=e.style)?y:"dashed",rect:{x:w.x,y:w.y,width:x.x+x.width-w.x,height:x.y+x.height-w.y}}]})),b=Ht(s),w=function(){e.beginPath(),e.save();var r=!1,i=function(t){r!==t&&(e.setLineDash(t?[5,3]:[]),r=t)};e.lineWidth=1;var o,l=(0,a.Z)(y);try{for(l.s();!(o=l.n()).done;){var s=o.value,c=(0,u.Z)(s,1)[0];void 0!==c&&Dn(0,0,t,n,c.rect.x,c.rect.y,c.rect.width,c.rect.height)&&(i("dashed"===c.style),e.strokeStyle=dn(c.color,1),e.strokeRect(c.rect.x+1,c.rect.y+1,c.rect.width-2,c.rect.height-2))}}catch(g){l.e(g)}finally{l.f()}var d,f=!1,h=(0,a.Z)(y);try{for(h.s();!(d=h.n()).done;){var p=d.value,v=(0,u.Z)(p,2)[1];void 0!==v&&Dn(0,0,t,n,v.rect.x,v.rect.y,v.rect.width,v.rect.height)&&(i("dashed"===v.style),!f&&v.rect.x<b&&(e.rect(b,0,t,n),e.clip(),f=!0),e.strokeStyle=dn(v.color,1),e.strokeRect(v.rect.x+1,v.rect.y+1,v.rect.width-2,v.rect.height-2))}}catch(g){h.e(g)}finally{h.f()}e.restore()};return w(),w}}(ce,l,d,f,h,p,v,g,y,k,D,S,"sticky"===A,R,B);if(ce.fillStyle=w.bgCell,pe.length>0){ce.beginPath();var Ce,Se=(0,a.Z)(pe);try{for(Se.s();!(Ce=Se.n()).done;){var Fe=Ce.value;ce.rect(Fe.x,Fe.y,Fe.width,Fe.height)}}catch(Ae){Se.e(Ae)}finally{Se.f()}ce.clip(),ce.fill(),ce.beginPath()}else ce.fillRect(0,0,l,d);var Me=En(ce,he,g,d,ie,p,v,h,R,fe,I,T,O,C,P,x,A,pe,J,M,z,B,H,_,U,N,j,W,w,K,$);!function(e,t,n,r,i,o,a,l,u,c,d,f,h,p,v,g,m,y){void 0===m&&t[t.length-1]===n[t.length-1]&&An(t,u,a,l,o,(function(n,a,l,u,m){if(n===t[t.length-1]){a+=n.width;var b=Math.max(a,u);b>r||(e.save(),e.beginPath(),e.rect(b,o+1,1e4,i-o-1),e.clip(),Zn(m,l,i,c,d,v,(function(t,n,r,i){if(i||!(g.length>0)||g.some((function(e){return Dn(a,t,1e4,r,e.x,e.y,e.width,e.height)}))){var o=h.hasIndex(n),l=p.hasIndex(n);e.beginPath();var u=null==f?void 0:f(n),c=void 0===u?y:(0,s.Z)((0,s.Z)({},y),u);c.bgCell!==y.bgCell&&(e.fillStyle=c.bgCell,e.fillRect(a,t,1e4,r)),l&&(e.fillStyle=c.bgHeader,e.fillRect(a,t,1e4,r)),o&&(e.fillStyle=c.accentLight,e.fillRect(a,t,1e4,r))}})),e.restore())}}))}(ce,he,g,l,d,ie,p,v,h,R,fe,O,M.rows,C,A,pe,J,w),gn(ce,he,h,p,v,l,d,pe,Me,D,ie,fe,O,E,A,R,w),null==xe||xe(),null==ke||ke(),null!==le&&(le.fillStyle=w.bgCell,le.fillRect(0,0,l,d),le.drawImage(ce.canvas,0,0));var Ze=function(e,t,n,r,i,o,a,l,u){var s=0;return An(e,o,r,i,n,(function(e,n,r,i,o){return Zn(o,r,t,a,l,u,(function(e,t,n,r){r||(s=Math.max(t,s))})),!0})),s}(he,d,ie,p,v,h,R,fe,A);null==_||_.setWindow({x:f,y:h,width:he.length,height:Ze-h},y),V.current={cellXOffset:f,cellYOffset:h,translateX:p,translateY:v,mustDrawFocusOnHeader:ve,lastBuffer:ee?ue===q?"a":"b":void 0},ce.restore(),se.restore()}}}}function Zn(e,t,n,r,i,o,a){for(var l=t,u=e,s="sticky"===o;l<n||s;){var c=s&&l>=n;c&&(s=!1,u=r-1);var d=i(u);if(c&&(l=n-d),!(s&&u===r-1)&&!0===a(l,u,d,c,"none"!==o&&u===r-1))break;if(c)break;l+=d,u++}}function An(e,t,n,r,i,o){var l,u=0,s=0,c=i+r,d=(0,a.Z)(e);try{for(d.s();!(l=d.n()).done;){var f=l.value;if(!0===o(f,f.sticky?s:u+n,c,s,t))break;u+=f.width,s+=f.sticky?f.width:0}}catch(h){d.e(h)}finally{d.f()}}function Rn(e,t,n,r,i){for(var o,a=0,l=0,u=0;u<e.length;u++){var s=e[u],c=u+1,d=s.width;for(s.sticky&&(l+=d);c<e.length&&Tt(e[c].group,s.group)&&e[c].sticky===e[u].sticky;){var f=e[c];d+=f.width,c++,u++,f.sticky&&(l+=f.width)}var h=a+(s.sticky?0:n),p=s.sticky?0:Math.max(0,l-h),v=Math.min(d-p,t-(h+p));i([s.sourceIndex,e[c-1].sourceIndex],null!=(o=s.group)?o:"",h+p,0,v,r),a+=d}}function In(e){var t=e-1;return t*t*t+1}var Tn=(0,f.Z)((function e(t){var n=this;(0,d.Z)(this,e),this.callback=t,this.currentHoveredItem=void 0,this.leavingItems=[],this.areSameItems=function(e,t){return(null==e?void 0:e[0])===(null==t?void 0:t[0])&&(null==e?void 0:e[1])===(null==t?void 0:t[1])},this.addToLeavingItems=function(e){n.leavingItems.some((function(t){return n.areSameItems(t.item,e.item)}))||n.leavingItems.push(e)},this.removeFromLeavingItems=function(e){var t,r=n.leavingItems.find((function(t){return n.areSameItems(t.item,e)}));return n.leavingItems=n.leavingItems.filter((function(e){return e!==r})),null!=(t=null==r?void 0:r.hoverAmount)?t:0},this.cleanUpLeavingElements=function(){n.leavingItems=n.leavingItems.filter((function(e){return e.hoverAmount>0}))},this.shouldStep=function(){var e=n.leavingItems.length>0,t=void 0!==n.currentHoveredItem&&n.currentHoveredItem.hoverAmount<1;return e||t},this.getAnimatingItems=function(){return void 0!==n.currentHoveredItem?[].concat((0,l.Z)(n.leavingItems),[n.currentHoveredItem]):n.leavingItems.map((function(e){return(0,s.Z)((0,s.Z)({},e),{},{hoverAmount:In(e.hoverAmount)})}))},this.step=function(e){if(void 0===n.lastAnimationTime)n.lastAnimationTime=e;else{var t,r=(e-n.lastAnimationTime)/80,i=(0,a.Z)(n.leavingItems);try{for(i.s();!(t=i.n()).done;){var o=t.value;o.hoverAmount=m(o.hoverAmount-r,0,1)}}catch(u){i.e(u)}finally{i.f()}void 0!==n.currentHoveredItem&&(n.currentHoveredItem.hoverAmount=m(n.currentHoveredItem.hoverAmount+r,0,1));var l=n.getAnimatingItems();n.callback(l),n.cleanUpLeavingElements()}n.shouldStep()?(n.lastAnimationTime=e,window.requestAnimationFrame(n.step)):n.lastAnimationTime=void 0},this.setHovered=function(e){var t;if(!n.areSameItems(null==(t=n.currentHoveredItem)?void 0:t.item,e)){if(void 0!==n.currentHoveredItem&&n.addToLeavingItems(n.currentHoveredItem),void 0!==e){var r=n.removeFromLeavingItems(e);n.currentHoveredItem={item:e,hoverAmount:r}}else n.currentHoveredItem=void 0;void 0===n.lastAnimationTime&&window.requestAnimationFrame(n.step)}}})),On=function(){function e(t){(0,d.Z)(this,e),this.fn=t}return(0,f.Z)(e,[{key:"value",get:function(){var e;return null!=(e=this.val)?e:this.val=this.fn()}}]),e}();function Pn(e){return new On(e)}var Hn=Pn((function(){return window.navigator.userAgent.includes("Firefox")})),Ln=Pn((function(){return window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")&&!window.navigator.userAgent.includes("Chrome")})),zn=Pn((function(){return window.navigator.platform.toLowerCase().startsWith("mac")}));function Bn(e){var t=g.useRef([]),n=g.useRef(0),r=g.useRef(e);r.current=e;var i=g.useCallback((function(){var e=function(){var e=t.current;t.current=[],r.current(e),t.current.length>0?n.current++:n.current=0};window.requestAnimationFrame(n.current>600?function(){return window.requestAnimationFrame(e)}:e)}),[]);return g.useCallback((function(e){(function(e,t){var n,r=(0,a.Z)(e);try{for(r.s();!(n=r.n()).done;){var i=n.value;if(i[0]===t[0]&&i[1]===t[1])return!0}}catch(o){r.e(o)}finally{r.f()}return!1})(t.current,e)||(0===t.current.length&&i(),t.current.push(e))}),[i])}var _n=g.memo(g.forwardRef((function(e,t){var n,r,l,c,d,f,h,p,v=e.width,y=e.height,b=e.accessibilityHeight,k=e.columns,D=e.cellXOffset,C=e.cellYOffset,S=e.headerHeight,E=e.fillHandle,F=void 0!==E&&E,M=e.groupHeaderHeight,Z=e.rowHeight,A=e.rows,R=e.getCellContent,I=e.getRowThemeOverride,T=e.onHeaderMenuClick,O=e.enableGroups,P=e.isFilling,H=e.onCanvasFocused,L=e.onCanvasBlur,z=e.isFocused,B=e.selection,_=e.freezeColumns,V=e.onContextMenu,N=e.trailingRowType,W=e.fixedShadowX,j=void 0===W||W,U=e.fixedShadowY,X=void 0===U||U,Y=e.drawFocusRing,K=void 0===Y||Y,$=e.onMouseDown,G=e.onMouseUp,q=e.onMouseMoveRaw,Q=e.onMouseMove,J=e.onItemHovered,ee=e.dragAndDropState,te=e.firstColAccessible,ne=e.onKeyDown,re=e.onKeyUp,ie=e.highlightRegions,oe=e.canvasRef,ae=e.onDragStart,le=e.onDragEnd,ue=e.eventTargetRef,se=e.isResizing,ce=e.isDragging,de=e.isDraggable,fe=void 0!==de&&de,he=e.allowResize,pe=e.disabledRows,ve=e.getGroupDetails,ge=e.theme,me=e.prelightCells,ye=e.headerIcons,be=e.verticalBorder,we=e.drawHeader,xe=e.drawCustomCell,ke=e.onCellFocused,Ce=e.onDragOverCell,Se=e.onDrop,Ee=e.onDragLeave,Fe=e.imageWindowLoader,Me=e.smoothScrollX,Ze=void 0!==Me&&Me,Ae=e.smoothScrollY,Re=void 0!==Ae&&Ae,Ie=e.experimental,Oe=e.getCellRenderer,Pe=null!=(n=e.translateX)?n:0,He=null!=(r=e.translateY)?r:0,Le=Math.max(_,Math.min(k.length-1,D)),ze=g.useRef(null),We=g.useMemo((function(){return new vt}),[]),je=null!=Fe?Fe:We,Ue=g.useRef(),Xe=g.useState(!1),Ye=(0,u.Z)(Xe,2),Ke=Ye[0],$e=Ye[1],Ge=g.useRef([]),qe=g.useRef(),Qe=g.useState(),tt=(0,u.Z)(Qe,2),rt=tt[0],ot=tt[1],at=g.useState(),lt=(0,u.Z)(at,2),ut=lt[0],st=lt[1],ct=g.useRef(null),dt=g.useState(!1),ft=(0,u.Z)(dt,2),ht=ft[0],pt=ft[1],mt=g.useRef(ht);mt.current=ht;var yt=g.useMemo((function(){return new ln(ye,(function(){Ot.current=void 0,zt.current()}))}),[ye]),bt=O?M+S:S,wt=g.useRef(-1),xt=!0!==(null==Ie?void 0:Ie.enableFirefoxRescaling);g.useLayoutEffect((function(){Hn.value&&1!==window.devicePixelRatio&&!xt&&(-1!==wt.current&&$e(!0),window.clearTimeout(wt.current),wt.current=window.setTimeout((function(){$e(!1),wt.current=-1}),200))}),[C,Le,Pe,He,xt]);var kt=function(e,t){return g.useMemo((function(){return e.map((function(e,n){return(0,s.Z)((0,s.Z)({},e),{},{sourceIndex:n,sticky:n<t})}))}),[e,t])}(k,_),Dt=g.useCallback((function(e,t,n){var r=e.getBoundingClientRect();if(!(t>=kt.length||n>=A)){var i=r.width/v,o=nn(t,n,v,y,M,bt,Le,C,Pe,He,A,_,"sticky"===N,kt,Z);return 1!==i&&(o.x*=i,o.y*=i,o.width*=i,o.height*=i),o.x+=r.x,o.y+=r.y,o}}),[v,y,M,bt,Le,C,Pe,He,A,_,N,kt,Z]),Ct=g.useCallback((function(e,t,n,r){var i,o,l=e.getBoundingClientRect(),u=l.width/v,s=(t-l.left)/u,c=(n-l.top)/u,d=Lt(kt,Le,v,void 0,Pe),f=0;r instanceof MouseEvent&&(f=r.button);var h,p=function(e,t,n){var r,i=0,o=(0,a.Z)(t);try{for(o.s();!(r=o.n()).done;){var l=r.value;if(e<=(l.sticky?i:i+(null!=n?n:0))+l.width)return l.sourceIndex;i+=l.width}}catch(u){o.e(u)}finally{o.f()}return-1}(s,d,Pe),g=function(e,t,n,r,i,o,a,l,u,s){var c=r+i;if(n&&e<=i)return-2;if(e<=c)return-1;var d="number"===typeof a?a:a(o-1);if(s&&e>t-d)return o-1;var f=o-(s?1:0),h=e-(null!=u?u:0);if("number"===typeof a){var p=Math.floor((h-c)/a)+l;if(p>=f)return;return p}for(var v=c,g=l;g<f;g++){var m=a(g);if(h<=v+m)return g;v+=m}}(c,y,O,S,M,A,Z,C,He,"sticky"===N),m=!0===(null==r?void 0:r.shiftKey),b=!0===(null==r?void 0:r.ctrlKey),w=!0===(null==r?void 0:r.metaKey),x=void 0!==r&&!(r instanceof MouseEvent)||"touch"===(null==r?void 0:r.pointerType),k=[Math.abs(s)<20?-1:Math.abs(l.width-s)<20?1:0,Math.abs(c)<20?-1:Math.abs(l.height-c)<20?1:0];if(-1===p||c<0||s<0||void 0===g||s>v||c>y){var D=s>v?-1:s<0?1:0,E=c>y?1:c<0?-1:0,R=!1;if(-1===p&&-1===g){var I=Dt(e,kt.length-1,-1);De(void 0!==I),R=t<I.x+I.width+5}h={kind:Ve,location:[-1!==p?p:s<0?0:kt.length-1,null!=g?g:A-1],direction:[D,E],shiftKey:m,ctrlKey:b,metaKey:w,isEdge:R,isTouch:x,button:f,scrollEdge:k}}else if(g<=-1){var T=Dt(e,p,g);De(void 0!==T);var P=void 0!==T&&T.x+T.width-t<=5,H=p-1;t-T.x<=5&&H>=0?(P=!0,De(void 0!==(T=Dt(e,H,g))),h={kind:O&&-2===g?_e:Be,location:[H,g],bounds:T,group:null!=(i=kt[H].group)?i:"",isEdge:P,shiftKey:m,ctrlKey:b,metaKey:w,isTouch:x,localEventX:t-T.x,localEventY:n-T.y,button:f,scrollEdge:k}):h={kind:O&&-2===g?_e:Be,group:null!=(o=kt[p].group)?o:"",location:[p,g],bounds:T,isEdge:P,shiftKey:m,ctrlKey:b,metaKey:w,isTouch:x,localEventX:t-T.x,localEventY:n-T.y,button:f,scrollEdge:k}}else{var L=Dt(e,p,g);De(void 0!==L),h={kind:"cell",location:[p,g],bounds:L,isEdge:void 0!==L&&L.x+L.width-t<5,shiftKey:m,ctrlKey:b,isFillHandle:F&&void 0!==L&&L.x+L.width-t<6&&L.y+L.height-n<6,metaKey:w,isTouch:x,localEventX:t-L.x,localEventY:n-L.y,button:f,scrollEdge:k}}return h}),[kt,Le,v,Pe,y,O,S,M,A,Z,C,He,N,Dt,F]);var St=null!=rt?rt:[],Et=(0,u.Z)(St,1)[0],Ft=g.useRef((function(e){})),Mt=g.useRef(rt);Mt.current=rt;var Zt=g.useMemo((function(){var e=document.createElement("canvas"),t=document.createElement("canvas");return e.style.display="none",e.style.opacity="0",e.style.position="fixed",t.style.display="none",t.style.opacity="0",t.style.position="fixed",[e,t]}),[]),Rt=(0,u.Z)(Zt,2),It=Rt[0],Tt=Rt[1];g.useLayoutEffect((function(){return document.documentElement.append(It),document.documentElement.append(Tt),function(){It.remove(),Tt.remove()}}),[It,Tt]);var Ot=g.useRef(),Pt=g.useCallback((function(){var e,t,n=ze.current,r=ct.current;if(null!==n&&null!==r){var i=Ot.current,o={canvas:n,bufferA:It,bufferB:Tt,headerCanvas:r,width:v,height:y,cellXOffset:Le,cellYOffset:C,translateX:Math.round(Pe),translateY:Math.round(He),mappedColumns:kt,enableGroups:O,freezeColumns:_,dragAndDropState:ee,theme:ge,headerHeight:S,groupHeaderHeight:M,disabledRows:null!=pe?pe:it.empty(),rowHeight:Z,verticalBorder:be,isResizing:se,isFocused:z,selection:B,fillHandle:F,lastRowSticky:N,rows:A,drawFocus:K,getCellContent:R,getGroupDetails:null!=ve?ve:function(e){return{name:e}},getRowThemeOverride:I,drawCustomCell:xe,drawHeaderCallback:we,prelightCells:me,highlightRegions:ie,imageLoader:je,lastBlitData:qe,damage:Ue.current,hoverValues:Ge.current,hoverInfo:Mt.current,spriteManager:yt,scrolling:Ke,hyperWrapping:null!=(e=null==Ie?void 0:Ie.hyperWrapping)&&e,touchMode:ht,enqueue:Ft.current,renderStrategy:null!=(t=null==Ie?void 0:Ie.renderStrategy)?t:Ln.value?"double-buffer":"single-buffer",getCellRenderer:Oe};void 0===o.damage?(Ot.current=o,Mn(o,i)):Mn(o,void 0)}}),[It,Tt,v,y,Le,C,Pe,He,kt,O,_,ee,ge,S,M,pe,Z,be,se,z,B,F,N,A,K,R,ve,I,xe,we,me,ie,je,yt,Ke,null==Ie?void 0:Ie.hyperWrapping,null==Ie?void 0:Ie.renderStrategy,ht,Oe]),zt=g.useRef(Pt);g.useLayoutEffect((function(){Pt(),zt.current=Pt}),[Pt]),g.useLayoutEffect((function(){var e=function(){var e=(0,o.Z)((0,i.Z)().mark((function e(){var t;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0!==(null==(t=null==document?void 0:document.fonts)?void 0:t.ready)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,document.fonts.ready;case 4:Ot.current=void 0,zt.current();case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[]);var Bt=g.useCallback((function(e){Ue.current=e,zt.current(),Ue.current=void 0}),[]),_t=Bn(Bt);Ft.current=_t;var Vt=g.useCallback((function(e){Bt(e.map((function(e){return e.cell})))}),[Bt]);je.setCallback(Bt);var Nt,Wt=g.useState(!1),jt=(0,u.Z)(Wt,2),Ut=jt[0],Xt=jt[1],Yt=null!=Et?Et:[],Kt=(0,u.Z)(Yt,2),$t=Kt[0],Gt=Kt[1],qt=void 0!==$t&&-1===Gt,Qt=void 0!==$t&&-2===Gt,Jt=!1,en=!1;if(void 0!==$t&&void 0!==Gt&&Gt>-1){var tn=R([$t,Gt],!0);Jt=tn.kind===Ne.NewRow||tn.kind===Ne.Marker&&"number"!==tn.markerKind,en=tn.kind===Te.Boolean&&nt(tn),Nt=tn.cursor}var rn=ce?"grabbing":null!=ut&&ut||se?"col-resize":Ut||P?"crosshair":void 0!==Nt?Nt:qt||Jt||en||Qt?"pointer":"default",on=g.useMemo((function(){return{contain:"strict",display:"block",cursor:rn}}),[rn]),an=g.useRef("default"),un=null==ue?void 0:ue.current;null!==un&&void 0!==un&&an.current!==on.cursor&&(un.style.cursor=an.current=on.cursor);var sn=g.useCallback((function(e,t,n,r){if(void 0!==ve){var i=ve(e);if(void 0!==i.actions){var o,l=mn(t,i.actions),s=(0,a.Z)(l.entries());try{for(s.s();!(o=s.n()).done;){var c=(0,u.Z)(o.value,2),d=c[0],f=c[1];if(yn(f,n+t.x,r+f.y))return i.actions[d]}}catch(h){s.e(h)}finally{s.f()}}}}),[ve]),cn=g.useCallback((function(e,t,n,r){var i=k[t];if(!ce&&!se&&!0===i.hasMenu&&(null==ut||!ut)){var o=Dt(e,t,-1);De(void 0!==o);var a=wn(o.x,o.y,o.width,o.height,"rtl"===At(i.title));if(n>a.x&&n<a.x+a.width&&r>a.y&&r<a.y+a.height)return o}}),[k,Dt,ut,ce,se]),dn=g.useRef(0),fn=g.useRef(),hn=g.useRef(!1),pn=g.useCallback((function(e){var t=ze.current,n=null==ue?void 0:ue.current;if(null!==t&&(e.target===t||e.target===n)){var r,i;if(hn.current=!0,e instanceof MouseEvent?(r=e.clientX,i=e.clientY):(r=e.touches[0].clientX,i=e.touches[0].clientY),e.target===n&&null!==n){var o=n.getBoundingClientRect();if(r>o.right||i>o.bottom)return}var a=Ct(t,r,i,e);if(fn.current=a.location,a.isTouch&&(dn.current=Date.now()),mt.current!==a.isTouch&&pt(a.isTouch),a.kind!==Be||void 0===cn(t,a.location[0],r,i))a.kind===_e&&void 0!==sn(a.group,a.bounds,a.localEventX,a.localEventY)||(null==$||$(a),a.isTouch||!0===fe||fe===a.kind||e.preventDefault())}}),[ue,fe,Ct,sn,cn,$]);gt("touchstart",pn,window,!1),gt("mousedown",pn,window,!1);var gn=g.useCallback((function(e){var t,n,r=ze.current;if(hn.current=!1,void 0!==G&&null!==r){var i,o,a=null==ue?void 0:ue.current,l=e.target!==r&&e.target!==a;if(e instanceof MouseEvent){if(i=e.clientX,o=e.clientY,"touch"===e.pointerType)return}else i=e.changedTouches[0].clientX,o=e.changedTouches[0].clientY;var c=Ct(r,i,o,e);c.isTouch&&0!==dn.current&&Date.now()-dn.current>500&&(c=(0,s.Z)((0,s.Z)({},c),{},{isLongTouch:!0})),mt.current!==c.isTouch&&pt(c.isTouch),!l&&e.cancelable&&e.preventDefault();var d=(0,u.Z)(c.location,1)[0],f=cn(r,d,i,o);if(c.kind!==Be||void 0===f){if(c.kind===_e){var h=sn(c.group,c.bounds,c.localEventX,c.localEventY);if(void 0!==h)return void(0===c.button&&h.onClick(c))}G(c,l)}else 0===c.button&&(null==(t=fn.current)?void 0:t[0])===d&&-1===(null==(n=fn.current)?void 0:n[1])||G(c,!0)}}),[G,ue,Ct,cn,sn]);gt("mouseup",gn,window,!1),gt("touchend",gn,window,!1),gt("click",g.useCallback((function(e){var t,n,r=ze.current;if(null!==r){var i,o,a=null==ue?void 0:ue.current,l=e.target!==r&&e.target!==a;e instanceof MouseEvent?(i=e.clientX,o=e.clientY):(i=e.changedTouches[0].clientX,o=e.changedTouches[0].clientY);var s=Ct(r,i,o,e);mt.current!==s.isTouch&&pt(s.isTouch),!l&&e.cancelable&&e.preventDefault();var c=(0,u.Z)(s.location,1)[0],d=cn(r,c,i,o);if(s.kind===Be&&void 0!==d)0===s.button&&(null==(t=fn.current)?void 0:t[0])===c&&-1===(null==(n=fn.current)?void 0:n[1])&&(null==T||T(c,d));else if(s.kind===_e){var f=sn(s.group,s.bounds,s.localEventX,s.localEventY);void 0!==f&&0===s.button&&f.onClick(s)}}}),[ue,Ct,cn,T,sn]),window,!1),gt("contextmenu",g.useCallback((function(e){var t=ze.current;if(null!==t&&void 0!==V){var n=Ct(t,e.clientX,e.clientY,e);V(n,(function(){e.cancelable&&e.preventDefault()}))}}),[Ct,V]),null!=(l=null==ue?void 0:ue.current)?l:null,!1);var bn=g.useCallback((function(e){Ue.current=e.map((function(e){return e.item})),Ge.current=e,zt.current(),Ue.current=void 0}),[]),kn=g.useMemo((function(){return new Tn(bn)}),[bn]),Dn=g.useRef(kn);Dn.current=kn,g.useLayoutEffect((function(){var e=Dn.current;if(void 0===Et||Et[1]<0)e.setHovered(Et);else{var t=R(Et),n=Oe(t);e.setHovered(void 0===n&&t.kind===Te.Custom||!0===(null==n?void 0:n.needsHover)?Et:void 0)}}),[R,Oe,Et]);var Cn=g.useRef();gt("mousemove",g.useCallback((function(e){var t,n=ze.current;if(null!==n){var r=null==ue?void 0:ue.current,i=e.target!==n&&e.target!==r,o=Ct(n,e.clientX,e.clientY,e);if("out-of-bounds"===o.kind||!i||hn.current||o.isTouch){if(s=o,c=Cn.current,s===c||(null==s?void 0:s.kind)===(null==c?void 0:c.kind)&&(null==s?void 0:s.location[0])===(null==c?void 0:c.location[0])&&(null==s?void 0:s.location[1])===(null==c?void 0:c.location[1])){if("cell"===o.kind||o.kind===Be||o.kind===_e){var a=[o.location,[o.localEventX,o.localEventY]];if(ot(a),Mt.current=a,"cell"===o.kind){var l=R(o.location);l.kind!==Te.Custom&&!0!==(null==(t=Oe(l))?void 0:t.needsHoverPosition)||Bt([o.location])}else o.kind===_e&&Bt([o.location])}}else null==J||J(o),ot(o.kind===Ve?void 0:[o.location,[o.localEventX,o.localEventY]]),Cn.current=o;var s,c,d=o.location[0]>=(te?0:1);if(st(o.kind===Be&&o.isEdge&&d&&!0===he),F&&void 0!==B.current){var f=(0,u.Z)(B.current.cell,2),h=f[0],p=f[1],v=Dt(n,h,p),g=e.clientX,m=e.clientY;De(void 0!==v),Xt(g>=v.x+v.width-6&&g<=v.x+v.width&&m>=v.y+v.height-6&&m<=v.y+v.height)}else Xt(!1);null==q||q(e),Q(o)}}}),[ue,Ct,te,he,F,B,q,Q,J,R,Oe,Bt,Dt]),window,!0);var Sn=g.useCallback((function(e){var t=ze.current;if(null!==t){var n,r=void 0;void 0!==B.current&&(n=Dt(t,B.current.cell[0],B.current.cell[1]),r=B.current.cell),null==ne||ne({bounds:n,stopPropagation:function(){return e.stopPropagation()},preventDefault:function(){return e.preventDefault()},cancel:function(){},ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey,altKey:e.altKey,key:e.key,keyCode:e.keyCode,rawEvent:e,location:r})}}),[ne,B,Dt]),En=g.useCallback((function(e){var t=ze.current;if(null!==t){var n,r=void 0;void 0!==B.current&&(n=Dt(t,B.current.cell[0],B.current.cell[1]),r=B.current.cell),null==re||re({bounds:n,stopPropagation:function(){return e.stopPropagation()},preventDefault:function(){return e.preventDefault()},cancel:function(){},ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey,altKey:e.altKey,key:e.key,keyCode:e.keyCode,rawEvent:e,location:r})}}),[re,B,Dt]),Fn=g.useCallback((function(e){ze.current=e,void 0!==oe&&(oe.current=e)}),[oe]);gt("dragstart",g.useCallback((function(e){var t=ze.current;if(null===t||!1===fe||se)e.preventDefault();else{var n,r,i=Ct(t,e.clientX,e.clientY);if(!0===fe||i.kind===fe){var o,a,l,c=!1;if(null==ae||ae((0,s.Z)((0,s.Z)({},i),{},{setData:function(e,t){n=e,r=t},setDragImage:function(e,t,n){o=e,a=t,l=n},preventDefault:function(){return c=!0},defaultPrevented:function(){return c}})),c||void 0===n||void 0===r||null===e.dataTransfer)e.preventDefault();else if(e.dataTransfer.setData(n,r),e.dataTransfer.effectAllowed="copyLink",void 0!==o&&void 0!==a&&void 0!==l)e.dataTransfer.setDragImage(o,a,l);else{var d=(0,u.Z)(i.location,2),f=d[0],h=d[1];if(void 0!==h){var p=document.createElement("canvas"),v=Dt(t,f,h);De(void 0!==v),p.width=v.width,p.height=v.height;var g=p.getContext("2d");null!==g&&(g.textBaseline="middle",-1===h?(g.font="".concat(ge.headerFontStyle," ").concat(ge.fontFamily),g.fillStyle=ge.bgHeader,g.fillRect(0,0,p.width,p.height),xn(g,0,0,v.width,v.height,kt[f],!1,ge,!1,!1,0,yt,we,!1)):(g.font="".concat(ge.baseFontStyle," ").concat(ge.fontFamily),g.fillStyle=ge.bgCell,g.fillRect(0,0,p.width,p.height),vn(g,h,R([f,h]),0,0,0,v.width,v.height,!1,ge,xe,je,yt,1,void 0,!1,0,void 0,void 0,Oe))),p.style.left="-100%",p.style.position="absolute",document.body.append(p),e.dataTransfer.setDragImage(p,v.width/2,v.height/2),window.setTimeout((function(){p.remove()}),0)}}}else e.preventDefault()}}),[fe,se,Ct,ae,Dt,ge,kt,yt,we,R,xe,je,Oe]),null!=(c=null==ue?void 0:ue.current)?c:null,!1,!1);var Zn=g.useRef();gt("dragover",g.useCallback((function(e){var t,n=ze.current;if(void 0!==Se&&e.preventDefault(),null!==n&&void 0!==Ce){var r=Ct(n,e.clientX,e.clientY),i=(0,u.Z)(r.location,2),o=i[0],a=i[1],l=o-(te?0:1),s=null!=(t=Zn.current)?t:[],c=(0,u.Z)(s,2),d=c[0],f=c[1];d===l&&f===a||(Zn.current=[l,a],Ce([l,a],e.dataTransfer))}}),[te,Ct,Ce,Se]),null!=(d=null==ue?void 0:ue.current)?d:null,!1,!1),gt("dragend",g.useCallback((function(){Zn.current=void 0,null==le||le()}),[le]),null!=(f=null==ue?void 0:ue.current)?f:null,!1,!1),gt("drop",g.useCallback((function(e){var t=ze.current;if(null!==t&&void 0!==Se){e.preventDefault();var n=Ct(t,e.clientX,e.clientY),r=(0,u.Z)(n.location,2),i=r[0],o=r[1];Se([i-(te?0:1),o],e.dataTransfer)}}),[te,Ct,Se]),null!=(h=null==ue?void 0:ue.current)?h:null,!1,!1),gt("dragleave",g.useCallback((function(){null==Ee||Ee()}),[Ee]),null!=(p=null==ue?void 0:ue.current)?p:null,!1,!1);var An=g.useRef(B);An.current=B;var Rn=g.useRef(null),In=g.useCallback((function(e){var t;null!==ze.current&&ze.current.contains(document.activeElement)&&(null===e&&void 0!==An.current.current?null==(t=null==oe?void 0:oe.current)||t.focus({preventScroll:!0}):null!==e&&e.focus({preventScroll:!0}),Rn.current=e)}),[oe]);g.useImperativeHandle(t,(function(){return{focus:function(){var e,t=Rn.current;null!==t&&document.contains(t)?t.focus({preventScroll:!0}):null==(e=null==oe?void 0:oe.current)||e.focus({preventScroll:!0})},getBounds:function(e,t){if(void 0!==oe&&null!==oe.current)return Dt(oe.current,e,null!=t?t:-1)},damage:Vt}}),[oe,Vt,Dt]);var On=g.useRef(),Pn=function(e,t,n){var r=g.useState(e),i=(0,u.Z)(r,2),o=i[0],a=i[1],l=g.useRef(!0);g.useEffect((function(){return function(){l.current=!1}}),[]);var s=g.useRef(x((function(e){l.current&&a(e)}),n));return g.useLayoutEffect((function(){l.current&&s.current((function(){return e()}))}),t),o}((function(){var e,t,n,r;if(v<50)return null;var i=Lt(kt,Le,v,ee,Pe),o=te?0:-1;te||0!==(null==(e=i[0])?void 0:e.sourceIndex)||(i=i.slice(1));var a=null!=(n=null==(t=B.current)?void 0:t.cell)?n:[],l=(0,u.Z)(a,2),s=l[0],c=l[1],d=null==(r=B.current)?void 0:r.range,f=i.map((function(e){return e.sourceIndex})),h=w(C,Math.min(A,C+b));return void 0===s||void 0===c||f.includes(s)&&h.includes(c)||In(null),g.createElement("table",{key:"access-tree",role:"grid","aria-rowcount":A+1,"aria-multiselectable":"true","aria-colcount":kt.length+o},g.createElement("thead",{role:"rowgroup"},g.createElement("tr",{role:"row","aria-rowindex":1},i.map((function(e){return g.createElement("th",{role:"columnheader","aria-selected":B.columns.hasIndex(e.sourceIndex),"aria-colindex":e.sourceIndex+1+o,tabIndex:-1,onFocus:function(t){if(t.target!==Rn.current)return null==ke?void 0:ke([e.sourceIndex,-1])},key:e.sourceIndex},e.title)})))),g.createElement("tbody",{role:"rowgroup"},h.map((function(e){return g.createElement("tr",{role:"row","aria-selected":B.rows.hasIndex(e),key:e,"aria-rowindex":e+2},i.map((function(t){var n=t.sourceIndex,r="".concat(n,",").concat(e),i=s===n&&c===e,a=void 0!==d&&n>=d.x&&n<d.x+d.width&&e>=d.y&&e<d.y+d.height,l="glide-cell-".concat(n,"-").concat(e),u=[n,e],f=R(u,!0);return g.createElement("td",{key:r,role:"gridcell","aria-colindex":n+1+o,"aria-selected":a,"aria-readonly":Je(f)||!et(f),id:l,"data-testid":l,onClick:function(){var t=null==oe?void 0:oe.current;if(null!==t&&void 0!==t)return null==ne?void 0:ne({bounds:Dt(t,n,e),cancel:function(){},preventDefault:function(){},stopPropagation:function(){},ctrlKey:!1,key:"Enter",keyCode:13,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:u})},onFocusCapture:function(t){var r,i;if(t.target!==Rn.current&&((null==(r=On.current)?void 0:r[0])!==n||(null==(i=On.current)?void 0:i[1])!==e))return On.current=u,null==ke?void 0:ke(u)},ref:i?In:void 0,tabIndex:-1},function(e,t){var n;if(e.kind===Te.Custom)return e.copyData;var r=null==t?void 0:t(e);return null!=(n=null==r?void 0:r.getAccessibilityString(e))?n:""}(f,Oe))})))}))))}),[v,kt,Le,ee,Pe,A,C,b,B,In,R,oe,ne,Dt,ke],200),zn=j?Ht(kt,ee):0,_n=0!==_&&j?Le>_?1:m(-Pe/100,0,1):0,Vn=X?m(-(32*-C+He)/100,0,1):0,Nn=g.useMemo((function(){if(!_n&&!Vn)return null;var e={position:"absolute",top:0,left:zn,width:v-zn,height:y,opacity:_n,pointerEvents:"none",transition:Ze?void 0:"opacity 0.2s",boxShadow:"inset 13px 0 10px -13px rgba(0, 0, 0, 0.2)"},t={position:"absolute",top:bt,left:0,width:v,height:y,opacity:Vn,pointerEvents:"none",transition:Re?void 0:"opacity 0.2s",boxShadow:"inset 0 13px 10px -13px rgba(0, 0, 0, 0.2)"};return g.createElement(g.Fragment,null,_n>0&&g.createElement("div",{id:"shadow-x",style:e}),Vn>0&&g.createElement("div",{id:"shadow-y",style:t}))}),[_n,Vn,zn,v,Ze,bt,y,Re]),Wn=g.useMemo((function(){return{position:"absolute",top:0,left:0}}),[]);return g.createElement(g.Fragment,null,g.createElement("canvas",{"data-testid":"data-grid-canvas",tabIndex:0,onKeyDown:Sn,onKeyUp:En,onFocus:H,onBlur:L,ref:Fn,style:on},Pn),g.createElement("canvas",{ref:ct,style:Wn}),Nn)})));function Vn(e,t,n,r){var i;return m(Math.round(t-(null!=(i=e.growOffset)?i:0)),Math.ceil(n),Math.floor(r))}var Nn=function(e){var t,n=g.useState(),r=(0,u.Z)(n,2),i=r[0],o=r[1],l=g.useState(),s=(0,u.Z)(l,2),c=s[0],d=s[1],f=g.useState(),h=(0,u.Z)(f,2),p=h[0],v=h[1],m=g.useState(),y=(0,u.Z)(m,2),b=y[0],w=y[1],x=g.useState(!1),k=(0,u.Z)(x,2),D=k[0],C=k[1],S=g.useState(),E=(0,u.Z)(S,2),F=E[0],M=E[1],Z=g.useState(),A=(0,u.Z)(Z,2),R=A[0],I=A[1],T=g.useState(),O=(0,u.Z)(T,2),P=O[0],H=O[1],L=g.useState(!1),z=(0,u.Z)(L,2),B=z[0],_=z[1],V=g.useState(),N=(0,u.Z)(V,2),W=N[0],j=N[1],U=e.onHeaderMenuClick,X=e.getCellContent,Y=e.onColumnMoved,K=e.onColumnResize,$=e.onColumnResizeStart,G=e.onColumnResizeEnd,q=e.gridRef,Q=e.maxColumnWidth,J=e.minColumnWidth,ee=e.onRowMoved,te=e.lockColumns,ne=e.onMouseDown,re=e.onMouseUp,ie=e.onItemHovered,oe=e.onDragStart,ae=e.canvasRef,le=void 0!==(null!=(t=null!=K?K:G)?t:$),ue=e.columns,se=e.selection.columns,ce=g.useCallback((function(e){var t=(0,u.Z)(e.location,2),n=t[0],r=t[1];void 0!==p&&b!==n&&n>=te?(C(!0),w(n)):void 0!==R&&void 0!==r?(_(!0),H(Math.max(0,r))):null==ie||ie(e)}),[p,R,b,ie,te]),de=void 0!==Y,fe=g.useCallback((function(e){var t,n;if(0===e.button){var r=(0,u.Z)(e.location,2),i=r[0],a=r[1];if("out-of-bounds"===e.kind&&e.isEdge&&le){var l=null==(t=null==q?void 0:q.current)?void 0:t.getBounds(ue.length-1,-1);void 0!==l&&(o(l.x),d(ue.length-1))}else if("header"===e.kind&&i>=te){var s=null==ae?void 0:ae.current;if(e.isEdge&&le&&s){o(e.bounds.x),d(i);var c=s.getBoundingClientRect().width/s.offsetWidth,f=e.bounds.width/c;null==$||$(ue[i],f,i,f+(null!=(n=ue[i].growOffset)?n:0))}else"header"===e.kind&&de&&(M(e.bounds.x),v(i))}else"cell"===e.kind&&te>0&&0===i&&void 0!==a&&void 0!==ee&&(j(e.bounds.y),I(a))}null==ne||ne(e)}),[ne,le,te,ee,q,ue,de,$,ae]),he=g.useCallback((function(e,t){D||B||null==U||U(e,t)}),[D,B,U]),pe=g.useRef(-1),ve=g.useCallback((function(){pe.current=-1,I(void 0),H(void 0),j(void 0),_(!1),v(void 0),w(void 0),M(void 0),C(!1),d(void 0),o(void 0)}),[]),ge=g.useCallback((function(e,t){var n,r,i;if(0===e.button){if(void 0!==c){if(!0===(null==se?void 0:se.hasIndex(c))){var o,l=(0,a.Z)(se);try{for(l.s();!(o=l.n()).done;){var u=o.value;if(u!==c){var s=ue[u],d=Vn(s,pe.current,J,Q);null==K||K(s,d,u,d+(null!=(n=s.growOffset)?n:0))}}}catch(w){l.e(w)}finally{l.f()}}var f=Vn(ue[c],pe.current,J,Q);if(null==G||G(ue[c],f,c,f+(null!=(r=ue[c].growOffset)?r:0)),se.hasIndex(c)){var h,v=(0,a.Z)(se);try{for(v.s();!(h=v.n()).done;){var g=h.value;if(g!==c){var m=ue[g],y=Vn(m,pe.current,J,Q);null==G||G(m,y,g,y+(null!=(i=m.growOffset)?i:0))}}}catch(w){v.e(w)}finally{v.f()}}}ve(),void 0!==p&&void 0!==b&&(null==Y||Y(p,b)),void 0!==R&&void 0!==P&&(null==ee||ee(R,P))}null==re||re(e,t)}),[re,c,p,b,R,P,se,G,ue,J,Q,K,Y,ee,ve]),me=g.useMemo((function(){if(void 0!==p&&void 0!==b&&p!==b)return{src:p,dest:b}}),[p,b]),ye=g.useCallback((function(e){var t,n,r=null==ae?void 0:ae.current;if(void 0!==p&&void 0!==F)Math.abs(e.clientX-F)>20&&C(!0);else if(void 0!==R&&void 0!==W){Math.abs(e.clientY-W)>20&&_(!0)}else if(void 0!==c&&void 0!==i&&r){var o=r.getBoundingClientRect().width/r.offsetWidth,l=(e.clientX-i)/o,u=ue[c],s=Vn(u,l,J,Q);if(null==K||K(u,s,c,s+(null!=(t=u.growOffset)?t:0)),pe.current=l,(null==se?void 0:se.first())===c){var d,f=(0,a.Z)(se);try{for(f.s();!(d=f.n()).done;){var h=d.value;if(h!==c){var v=ue[h],g=Vn(v,pe.current,J,Q);null==K||K(v,g,h,g+(null!=(n=v.growOffset)?n:0))}}}catch(m){f.e(m)}finally{f.f()}}}}),[p,F,R,W,c,i,ue,J,Q,K,se,ae]),be=g.useCallback((function(e,t){if(void 0===R||void 0===P)return X(e,t);var n=(0,u.Z)(e,2),r=n[0],i=n[1];return i===P?i=R:(i>P&&(i-=1),i>=R&&(i+=1)),X([r,i],t)}),[R,P,X]),we=g.useCallback((function(e){null==oe||oe(e),e.defaultPrevented()||ve()}),[ve,oe]);return g.createElement(_n,{accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,drawCustomCell:e.drawCustomCell,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,enableGroups:e.enableGroups,eventTargetRef:e.eventTargetRef,experimental:e.experimental,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,headerIcons:e.headerIcons,height:e.height,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isDraggable:e.isDraggable,isFilling:e.isFilling,isFocused:e.isFocused,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDrop:e.onDrop,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,theme:e.theme,trailingRowType:e.trailingRowType,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,width:e.width,getCellContent:be,isResizing:void 0!==c,onHeaderMenuClick:he,isDragging:D,onItemHovered:ce,onDragStart:we,onMouseDown:fe,allowResize:le,onMouseUp:ge,dragAndDropState:me,onMouseMoveRaw:ye,ref:q})};var Wn=(0,v.d)("div")({name:"ScrollRegionStyle",class:"s1jz82f8",vars:{"s1jz82f8-0":[function(e){return e.isSafari?"scroll":"auto"}]}});function jn(e){e.stopPropagation()}var Un=function(e){var t,n,r,i,o=e.children,l=e.clientHeight,c=e.scrollHeight,d=e.scrollWidth,f=e.update,h=e.draggable,p=e.className,v=e.preventDiagonalScrolling,m=void 0!==v&&v,y=e.paddingBottom,b=void 0===y?0:y,w=e.paddingRight,x=void 0===w?0:w,k=e.rightElement,D=e.rightElementProps,C=e.scrollRef,S=e.scrollToEnd,E=e.initialSize,F=e.minimap,M=[],Z=null!=(t=null==D?void 0:D.sticky)&&t,A=null!=(n=null==D?void 0:D.fill)&&n,R=g.useRef(0),I=g.useRef(0),T=g.useRef(null),O=window.devicePixelRatio;g.useEffect((function(){var e=T.current;null!==e&&!0===S&&(e.scrollLeft=e.scrollWidth-e.clientWidth)}),[S]);var P=g.useRef({scrollLeft:0,scrollTop:0,lockDirection:void 0}),H=g.useRef(null),L=function(e){var t=g.useState(!1),n=(0,u.Z)(t,2),r=n[0],i=n[1],o=g.useRef(0);return gt("touchstart",g.useCallback((function(){window.clearTimeout(o.current),i(!0)}),[]),window,!0,!1),gt("touchend",g.useCallback((function(t){0===t.touches.length&&(o.current=window.setTimeout((function(){return i(!1)}),e))}),[e]),window,!0,!1),r}(200),z=g.useState(!0),B=(0,u.Z)(z,2),_=B[0],V=B[1],N=g.useRef(0);g.useEffect((function(){if(_&&!L&&void 0!==P.current.lockDirection){var e=T.current;if(null!==e){var t=(0,u.Z)(P.current.lockDirection,2),n=t[0],r=t[1];void 0!==n?e.scrollLeft=n:void 0!==r&&(e.scrollTop=r),P.current.lockDirection=void 0}}}),[L,_]);var W=g.useCallback((function(){var e,t,n,r,i=T.current;if(null!==i){var o=i.scrollTop,a=i.scrollLeft,l=P.current.scrollTop,u=P.current.scrollLeft,s=a-u,d=o-l;L&&0!==s&&0!==d&&(Math.abs(s)>3||Math.abs(d)>3)&&m&&void 0===P.current.lockDirection&&(P.current.lockDirection=Math.abs(s)<Math.abs(d)?[u,void 0]:[void 0,l]);var h=P.current.lockDirection;a=null!=(e=null==h?void 0:h[0])?e:a,o=null!=(t=null==h?void 0:h[1])?t:o,P.current.scrollLeft=a,P.current.scrollTop=o;var p=o,v=I.current-p,g=i.scrollHeight-i.clientHeight;if(I.current=p,g>0&&(Math.abs(v)>2e3||0===p||p===g)&&c>i.scrollHeight+5){var y=p/g,w=(c-i.clientHeight)*y;R.current=w-p}void 0!==h&&(window.clearTimeout(N.current),V(!1),N.current=window.setTimeout((function(){return V(!0)}),200)),f({x:a,y:p+R.current,width:i.clientWidth-x,height:i.clientHeight-b,paddingRight:null!=(r=null==(n=H.current)?void 0:n.clientWidth)?r:0})}}),[b,x,c,f,m,L]),j=g.useRef(W);j.current=W;var U=g.useRef(),X=g.useRef(!1);g.useEffect((function(){X.current?W():X.current=!0}),[W,b,x]);var Y=g.useCallback((function(e){T.current=e,void 0!==C&&(C.current=e)}),[C]),K=0,$=0;for(M.push(g.createElement("div",{key:K++,style:{width:d,height:0}}));$<c;){var G=Math.min(5e6,c-$);M.push(g.createElement("div",{key:K++,style:{width:0,height:G}})),$+=G}var q=function(e){var t=(0,g.useRef)(null),n=(0,g.useState)({width:null==e?void 0:e[0],height:null==e?void 0:e[1]}),r=(0,u.Z)(n,2),i=r[0],o=r[1];return(0,g.useLayoutEffect)((function(){var e=new window.ResizeObserver((function(e){var t,n=(0,a.Z)(e);try{var r=function(){var e=t.value,n=e&&e.contentRect||{},r=n.width,i=n.height;o((function(e){return e.width===r&&e.height===i?e:{width:r,height:i}}))};for(n.s();!(t=n.n()).done;)r()}catch(i){n.e(i)}finally{n.f()}}));return t.current&&e.observe(t.current,void 0),function(){e.disconnect()}}),[t.current]),(0,s.Z)({ref:t},i)}(E),Q=q.ref,J=q.width,ee=q.height;return(null==(r=U.current)?void 0:r.height)===ee&&(null==(i=U.current)?void 0:i.width)===J||(window.setTimeout((function(){return j.current()}),0),U.current={width:J,height:ee}),0===(null!=J?J:0)||0===(null!=ee?ee:0)?g.createElement("div",{ref:Q}):g.createElement("div",{ref:Q},g.createElement(Wn,{isSafari:Ln.value},F,g.createElement("div",{className:"dvn-underlay"},o),g.createElement("div",{ref:Y,style:U.current,draggable:h,onDragStart:function(e){h||(e.stopPropagation(),e.preventDefault())},className:"dvn-scroller "+(null!=p?p:""),onScroll:W},g.createElement("div",{className:"dvn-scroll-inner"+(void 0===k?" hidden":"")},g.createElement("div",{className:"dvn-stack"},M),void 0!==k&&g.createElement(g.Fragment,null,!A&&g.createElement("div",{className:"dvn-spacer"}),g.createElement("div",{ref:H,onMouseDown:jn,onMouseUp:jn,onMouseMove:jn,style:{height:ee,maxHeight:l-Math.ceil(O%1),position:"sticky",top:0,paddingLeft:1,marginBottom:-40,marginRight:x,flexGrow:A?1:void 0,right:Z?null!=x?x:0:void 0,pointerEvents:"auto"}},k))))))},Xn=(0,v.d)("div")({name:"MinimapStyle",class:"m15w2ly5"}),Yn=function(e){var t,n,r,i=e.columns,o=e.rows,l=e.rowHeight,c=e.headerHeight,d=e.groupHeaderHeight,f=e.enableGroups,h=e.freezeColumns,p=e.experimental,v=e.clientSize,y=e.className,b=e.onVisibleRegionChanged,w=e.scrollToEnd,x=e.scrollRef,k=e.preventDiagonalScrolling,D=e.rightElement,C=e.rightElementProps,S=e.overscrollX,E=e.overscrollY,F=e.showMinimap,M=void 0!==F&&F,Z=e.initialSize,A=e.smoothScrollX,R=void 0!==A&&A,I=e.smoothScrollY,T=void 0!==I&&I,O=e.isDraggable,P=null!=p?p:{},H=P.paddingRight,L=P.paddingBottom,z=(0,u.Z)(v,2),B=z[0],_=z[1],V=g.useRef(),N=g.useRef(),W=g.useRef(),j=g.useRef(),U=g.useMemo((function(){var e,t=Math.max(0,null!=S?S:0),n=(0,a.Z)(i);try{for(n.s();!(e=n.n()).done;){t+=e.value.width}}catch(r){n.e(r)}finally{n.f()}return t}),[i,S]),X=f?c+d:c;if("number"===typeof l)X+=o*l;else for(var Y=0;Y<o;Y++)X+=l(Y);void 0!==E&&(X+=E);var K=g.useRef(),$=g.useCallback((function(){var e,t,n;if(void 0!==K.current){var r=(0,s.Z)({},K.current),u=0,c=r.x<0?-r.x:0,d=0,f=0;r.x=r.x<0?0:r.x;for(var p=0,v=0;v<h;v++)p+=i[v].width;var g,m=(0,a.Z)(i);try{for(m.s();!(g=m.n()).done;){var y=g.value,w=u-p;if(r.x>=w+y.width)u+=y.width,f++,d++;else if(r.x>w)u+=y.width,R?c+=w-r.x:f++,d++;else{if(!(r.x+r.width>w))break;u+=y.width,d++}}}catch(A){m.e(A)}finally{m.f()}var x=0,k=0,D=0;if("number"===typeof l)T?x=(k=Math.floor(r.y/l))*l-r.y:k=Math.ceil(r.y/l),D=Math.ceil(r.height/l)+k,x<0&&D++;else for(var C=0,S=0;S<o;S++){var E=l(S),F=C+(T?0:E/2);if(r.y>=C+E)C+=E,k++,D++;else if(r.y>F)C+=E,T?x+=F-r.y:k++,D++;else{if(!(r.y+r.height>E/2+C))break;C+=E,D++}}var M={x:f,y:k,width:d-f,height:D-k},Z=V.current;void 0!==Z&&Z.y===M.y&&Z.x===M.x&&Z.height===M.height&&Z.width===M.width&&N.current===c&&W.current===x&&r.width===(null==(e=j.current)?void 0:e[0])&&r.height===(null==(t=j.current)?void 0:t[1])||(null==b||b({x:f,y:k,width:d-f,height:D-k},r.width,r.height,null!=(n=r.paddingRight)?n:0,c,x),V.current=M,N.current=c,W.current=x,j.current=[r.width,r.height])}}),[i,l,o,b,h,R,T]),G=g.useCallback((function(e){K.current=e,$()}),[$]);g.useEffect((function(){$()}),[$]);var q=null!=(t=null==x?void 0:x.current)?t:void 0,Q=m(U/X,2/3,1.5),J=200,ee=Q>1?J:Math.ceil(J*Q),te=Q>1?Math.ceil(J/Q):J,ne=ee/U,re=te/X,ie=Math.min(B*Math.max(ne,.01),ee),oe=Math.min(_*Math.max(re,.01),te),ae=(null!=(n=null==q?void 0:q.scrollLeft)?n:0)/(U-B)*(ee-ie),le=(null!=(r=null==q?void 0:q.scrollTop)?r:0)/(X-_)*(te-oe),ue=g.useMemo((function(){if(M&&0!==ie&&0!==oe){var e=function(e){if(void 0!==q){var t=e.currentTarget.getBoundingClientRect(),n=e.clientX-t.x-ie/2,r=e.clientY-t.y-oe/2,i=(U-q.clientWidth)*(n/(ee-ie)),o=(X-q.clientHeight)*(r/(te-oe));q.scrollTo({left:i,top:o,behavior:"mousemove"===e.type?"auto":"smooth"})}};return g.createElement(Xn,{style:{width:ee,height:te},"data-testid":"minimap-container",onMouseMove:function(t){1===t.buttons&&e(t)},onClick:e},g.createElement("div",{className:"header"}),g.createElement("div",{className:"locationMarker",onDragStart:function(e){return e.preventDefault()},style:{left:ae,top:le,width:ie,height:oe,borderRadius:Math.min(ie,.2*oe,9)}}))}}),[te,X,ae,q,M,le,oe,ie,ee,U]);return g.createElement(Un,{scrollRef:x,minimap:ue,className:y,preventDiagonalScrolling:k,draggable:!0===O||"string"===typeof O,scrollWidth:U+(null!=H?H:0),scrollHeight:X+(null!=L?L:0),clientHeight:_,rightElement:D,paddingBottom:L,paddingRight:H,rightElementProps:C,update:G,initialSize:Z,scrollToEnd:w},g.createElement(Nn,{eventTargetRef:x,width:B,height:_,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,theme:e.theme,trailingRowType:e.trailingRowType,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,drawCustomCell:e.drawCustomCell,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY}))},Kn=(0,v.d)("div")({name:"SearchWrapper",class:"sxep88s",vars:{"sxep88s-0":[function(e){return e.showSearch?0:400},"px"]}}),$n=g.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},g.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 244l144-144 144 144M256 120v292"})),Gn=g.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},g.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 268l144 144 144-144M256 392V100"})),qn=g.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},g.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M368 368L144 144M368 144L144 368"})),Qn=function(e){var t=e.canvasRef,n=e.cellYOffset,r=e.rows,l=e.columns,c=e.searchInputRef,d=e.searchValue,f=e.searchResults,h=e.onSearchValueChange,p=e.getCellsForSelection,v=e.onSearchResultsChanged,m=e.showSearch,y=void 0!==m&&m,b=e.onSearchClose,w=g.useState((function(){return"search-box-"+Math.round(1e3*Math.random())})),x=(0,u.Z)(w,1)[0],k=g.useState(""),D=(0,u.Z)(k,2),C=D[0],S=D[1],E=null!=d?d:C,F=g.useCallback((function(e){S(e),null==h||h(e)}),[h]),M=g.useState(),Z=(0,u.Z)(M,2),A=Z[0],R=Z[1],I=g.useRef(A);I.current=A,g.useEffect((function(){void 0!==f&&(f.length>0?R((function(e){var t;return{rowsSearched:r,results:f.length,selectedIndex:null!=(t=null==e?void 0:e.selectedIndex)?t:-1}})):R(void 0))}),[r,f]);var T=g.useRef(new AbortController),O=g.useRef(),P=g.useState([]),H=(0,u.Z)(P,2),L=H[0],z=H[1],B=null!=f?f:L,_=g.useCallback((function(){void 0!==O.current&&(window.cancelAnimationFrame(O.current),O.current=void 0,T.current.abort())}),[]),V=g.useRef(n);V.current=n;var N=g.useCallback((function(e){var t=new RegExp(e.replace(/([$()*+.?[\\\]^{|}-])/g,"\\$1"),"i"),n=V.current,s=Math.min(10,r),c=0;R(void 0),z([]);var d=[],f=function(){var e=(0,o.Z)((0,i.Z)().mark((function e(){var o,h,g,m,y,b,w,x,k,D,C,S,E,F,M,Z,A,P,H,L,B,_;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0!==p){e.next=2;break}return e.abrupt("return");case 2:if(g=performance.now(),m=r-c,"function"!==typeof(y=p({x:0,y:n,width:l.length,height:Math.min(s,m,r-n)},T.current.signal))){e.next=9;break}return e.next=8,y();case 8:y=e.sent;case 9:b=!1,w=(0,a.Z)(y.entries()),e.prev=11,w.s();case 13:if((x=w.n()).done){e.next=47;break}k=(0,u.Z)(x.value,2),D=k[0],C=k[1],S=(0,a.Z)(C.entries()),e.prev=16,S.s();case 18:if((E=S.n()).done){e.next=37;break}F=(0,u.Z)(E.value,2),M=F[0],Z=F[1],A=void 0,e.t0=Z.kind,e.next=e.t0===Te.Text||e.t0===Te.Number?24:e.t0===Te.Uri||e.t0===Te.Markdown?26:e.t0===Te.Boolean?28:e.t0===Te.Image||e.t0===Te.Bubble?30:e.t0===Te.Custom?32:34;break;case 24:return A=Z.displayData,e.abrupt("break",34);case 26:return A=Z.data,e.abrupt("break",34);case 28:return A="boolean"===typeof Z.data?Z.data.toString():void 0,e.abrupt("break",34);case 30:return A=Z.data.join("\ud83d\udc33"),e.abrupt("break",34);case 32:return A=Z.copyData,e.abrupt("break",34);case 34:void 0!==A&&t.test(A)&&(d.push([M,D+n]),b=!0);case 35:e.next=18;break;case 37:e.next=42;break;case 39:e.prev=39,e.t1=e.catch(16),S.e(e.t1);case 42:return e.prev=42,S.f(),e.finish(42);case 45:e.next=13;break;case 47:e.next=52;break;case 49:e.prev=49,e.t2=e.catch(11),w.e(e.t2);case 52:return e.prev=52,w.f(),e.finish(52);case 55:P=performance.now(),b&&z([].concat(d)),De((c+=y.length)<=r),H=null!=(h=null==(o=I.current)?void 0:o.selectedIndex)?h:-1,R({results:d.length,rowsSearched:c,selectedIndex:H}),null==v||v(d,H),n+s>=r?n=0:n+=s,L=P-g,B=Math.max(L,1),_=10/B,s=Math.ceil(s*_),c<r&&d.length<1e3&&(O.current=window.requestAnimationFrame(f));case 68:case"end":return e.stop()}}),e,null,[[11,49,52,55],[16,39,42,45]])})));return function(){return e.apply(this,arguments)}}();_(),O.current=window.requestAnimationFrame(f)}),[_,l.length,p,v,r]),W=g.useCallback((function(){var e;null==b||b(),R(void 0),z([]),null==v||v([],-1),_(),null==(e=null==t?void 0:t.current)||e.focus()}),[_,t,b,v]),j=g.useCallback((function(e){F(e.target.value),void 0===f&&(""===e.target.value?(R(void 0),z([]),_()):N(e.target.value))}),[N,_,F,f]);g.useEffect((function(){y&&null!==c.current&&(F(""),c.current.focus({preventScroll:!0}))}),[y,c,F]);var U=g.useCallback((function(e){var t;if(null==(t=null==e?void 0:e.stopPropagation)||t.call(e),void 0!==A){var n=(A.selectedIndex+1)%A.results;R((0,s.Z)((0,s.Z)({},A),{},{selectedIndex:n})),null==v||v(B,n)}}),[A,v,B]),X=g.useCallback((function(e){var t;if(null==(t=null==e?void 0:e.stopPropagation)||t.call(e),void 0!==A){var n=(A.selectedIndex-1)%A.results;n<0&&(n+=A.results),R((0,s.Z)((0,s.Z)({},A),{},{selectedIndex:n})),null==v||v(B,n)}}),[v,B,A]),Y=g.useCallback((function(e){(e.ctrlKey||e.metaKey)&&"KeyF"===e.nativeEvent.code||"Escape"===e.key?(W(),e.stopPropagation(),e.preventDefault()):"Enter"===e.key&&(e.shiftKey?X():U())}),[W,U,X]);g.useEffect((function(){return function(){_()}}),[_]);var K=g.useMemo((function(){var e,t,n,i;void 0!==A&&(i=A.results>=1e3?"over 1000":"".concat(A.results," result").concat(1!==A.results?"s":""),A.selectedIndex>=0&&(i="".concat(A.selectedIndex+1," of ").concat(i)));var o=function(e){e.stopPropagation()},a=Math.floor((null!=(e=null==A?void 0:A.rowsSearched)?e:0)/r*100),l={width:"".concat(a,"%")};return g.createElement(Kn,{showSearch:y,onMouseDown:o,onMouseMove:o,onMouseUp:o,onClick:o},g.createElement("div",{className:"search-bar-inner"},g.createElement("input",{id:x,"aria-hidden":!y,"data-testid":"search-input",ref:c,onChange:j,value:E,tabIndex:y?void 0:-1,onKeyDownCapture:Y}),g.createElement("button",{"aria-label":"Previous Result","aria-hidden":!y,tabIndex:y?void 0:-1,onClick:X,disabled:0===(null!=(t=null==A?void 0:A.results)?t:0)},$n),g.createElement("button",{"aria-label":"Next Result","aria-hidden":!y,tabIndex:y?void 0:-1,onClick:U,disabled:0===(null!=(n=null==A?void 0:A.results)?n:0)},Gn),void 0!==b&&g.createElement("button",{"aria-label":"Close Search","aria-hidden":!y,"data-testid":"search-close-button",tabIndex:y?void 0:-1,onClick:W},qn)),void 0!==A?g.createElement(g.Fragment,null,g.createElement("div",{className:"search-status"},g.createElement("div",{"data-testid":"search-result-area"},i)),g.createElement("div",{className:"search-progress",style:l})):g.createElement("div",{className:"search-status"},g.createElement("label",{htmlFor:x},"Type to search")))}),[W,U,X,j,b,Y,r,A,E,y,x,c]);return g.createElement(g.Fragment,null,g.createElement(Yn,{prelightCells:B,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,className:e.className,clientSize:e.clientSize,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,initialSize:e.initialSize,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onMouseMove:e.onMouseMove,onVisibleRegionChanged:e.onVisibleRegionChanged,overscrollX:e.overscrollX,overscrollY:e.overscrollY,preventDiagonalScrolling:e.preventDiagonalScrolling,rightElement:e.rightElement,rightElementProps:e.rightElementProps,rowHeight:e.rowHeight,rows:e.rows,scrollRef:e.scrollRef,selection:e.selection,showMinimap:e.showMinimap,theme:e.theme,trailingRowType:e.trailingRowType,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,drawCustomCell:e.drawCustomCell,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,scrollToEnd:e.scrollToEnd}),K)},Jn=(0,v.d)("input")({name:"RenameInput",class:"r1kzy40b",vars:{"r1kzy40b-0":[function(e){return Math.max(16,e.targetHeight-10)},"px"]}}),er=function(e){var t=e.bounds,n=e.group,r=e.onClose,i=e.canvasBounds,o=e.onFinish,a=g.useState(n),l=(0,u.Z)(a,2),s=l[0],c=l[1];return g.createElement(Fe,{style:{position:"absolute",left:t.x-i.left+1,top:t.y-i.top,width:t.width-2,height:t.height},className:"c1sqdbw3",onClickOutside:r},g.createElement(Jn,{targetHeight:t.height,"data-testid":"group-rename-input",value:s,onBlur:r,onFocus:function(e){return e.target.setSelectionRange(0,s.length)},onChange:function(e){return c(e.target.value)},onKeyDown:function(e){"Enter"===e.key?o(s):"Escape"===e.key&&r()},autoFocus:!0}))},tr=150;function nr(e,t,n,r,i,o,a,u,c){var d,f=[];void 0!==i&&(d=f).push.apply(d,(0,l.Z)(i.map((function(e){return e[r]})).map((function(n){return function(e,t,n,r){var i,o,a=r(t);return null!=(o=null==(i=null==a?void 0:a.measure)?void 0:i.call(a,e,t,n))?o:tr}(e,n,t,c)}))));if(f.length>5&&u){var h=f.reduce((function(e,t){return e+t}))/f.length;f=f.filter((function(e){return e<2*h}))}f.push(e.measureText(n.title).width+16+(void 0===n.icon?0:28));var p=Math.max.apply(Math,(0,l.Z)(f)),v=Math.max(Math.ceil(o),Math.min(Math.floor(a),Math.ceil(p)));return(0,s.Z)((0,s.Z)({},n),{},{width:v})}function rr(e,t){if(0===e.length)return!1;var n=!1,r=!1,i=!1,o=!1,l=e.split("+");if(!function(e,t){if(void 0===e)return!1;if(e.length>1&&e.startsWith("_")){if(Number.parseInt(e.slice(1))!==t.keyCode)return!1}else if(e!==t.key)return!1;return!0}(l.pop(),t))return!1;var u,s=(0,a.Z)(l);try{for(s.s();!(u=s.n()).done;){switch(u.value){case"ctrl":n=!0;break;case"shift":r=!0;break;case"alt":i=!0;break;case"meta":o=!0;break;case"primary":zn.value?o=!0:n=!0}}}catch(c){s.e(c)}finally{s.f()}return t.altKey===i&&t.ctrlKey===n&&t.shiftKey===r&&t.metaKey===o}function ir(e){return e.startsWith('"')&&e.endsWith('"')&&(e=e.slice(1,-1).replace(/""/g,'"')),e}function or(e){var t,n;(n=t||(t={}))[n.None=0]="None",n[n.inString=1]="inString",n[n.inStringPostQuote=2]="inStringPostQuote";var r=[],i=[],o=0,l=0;e=e.replace(/\r\n/g,"\n");var u,s=0,c=(0,a.Z)(e);try{for(c.s();!(u=c.n()).done;){var d=u.value;switch(l){case 0:"\t"===d||"\n"===d?(i.push(e.slice(o,s)),o=s+1,"\n"===d&&(r.push(i),i=[])):'"'===d&&(l=1);break;case 1:'"'===d&&(l=2);break;case 2:'"'===d?l=1:"\t"===d||"\n"===d?(i.push(ir(e.slice(o,s))),o=s+1,"\n"===d&&(r.push(i),i=[]),l=0):l=0}s++}}catch(f){c.e(f)}finally{c.f()}return o<e.length&&i.push(ir(e.slice(o,e.length))),r.push(i),r}function ar(e){for(var t,n,r,i=[e],o=[];i.length>0;){var a=i.pop();if(void 0===a)break;a instanceof HTMLTableElement||"TBODY"===a.nodeName?i.push.apply(i,(0,l.Z)((0,l.Z)(a.children).reverse())):a instanceof HTMLTableRowElement?(void 0!==r&&o.push(r),r=[],i.push.apply(i,(0,l.Z)((0,l.Z)(a.children).reverse()))):a instanceof HTMLTableCellElement&&(null==r||r.push(null!=(n=null!=(t=a.innerText)?t:a.textContent)?n:""))}return void 0!==r&&o.push(r),o}function lr(e){return/[\t\n",]/.test(e)&&(e='"'.concat(e.replace(/"/g,'""'),'"')),e}var ur=function(e){switch(e){case!0:return"TRUE";case!1:return"FALSE";case ze:return"INDETERMINATE";case Le:return"";default:Ce(0,"A boolean was formated with invalid type: ".concat(typeof e))}};function sr(e,t,n,r){var i,o,a=r[t];if(void 0!==e.span&&e.span[0]!==a)return"";if(void 0!==e.copyData)return lr(e.copyData);switch(e.kind){case Te.Text:case Te.Number:return lr(n?null!=(o=null==(i=e.data)?void 0:i.toString())?o:"":e.displayData);case Te.Markdown:case Te.RowID:case Te.Uri:return lr(e.data);case Te.Image:case Te.Bubble:return 0===e.data.length?"":e.data.reduce((function(e,t){return"".concat(lr(e),",").concat(lr(t))}));case Te.Boolean:return ur(e.data);case Te.Loading:return n?"":"#LOADING";case Te.Protected:return n?"":"************";case Te.Drilldown:return 0===e.data.length?"":e.data.map((function(e){return e.text})).reduce((function(e,t){return"".concat(lr(e),",").concat(lr(t))}));case Te.Custom:return lr(e.copyData);default:Ce(0,"A cell was passed with an invalid kind: ".concat(e.kind))}}function cr(e,t,n){var r,i,o,l,s=function(e,t){return e.map((function(e){return e.map((function(e,n){return sr(e,n,!1,t)})).join("\t")})).join("\n")}(e,t);if(void 0!==(null==(r=window.navigator.clipboard)?void 0:r.write)||void 0!==n){var c,d=document.createElement("tbody"),f=(0,a.Z)(e);try{for(f.s();!(c=f.n()).done;){var h,p=c.value,v=document.createElement("tr"),g=(0,a.Z)(p.entries());try{for(g.s();!(h=g.n()).done;){var m=(0,u.Z)(h.value,2),y=m[0],b=m[1],w=document.createElement("td");if(b.kind===Te.Uri){var x=document.createElement("a");x.href=b.data,x.innerText=b.data,w.append(x)}else w.innerText=sr(b,y,!0,t);v.append(w)}}catch(k){g.e(k)}finally{g.f()}d.append(v)}}catch(k){f.e(k)}finally{f.f()}if(void 0!==(null==(i=window.navigator.clipboard)?void 0:i.write))window.navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([s],{type:"text/plain"}),"text/html":new Blob(["<table>".concat(d.outerHTML,"</table>")],{type:"text/html"})})]);else if(void 0!==n&&null!==(null==n?void 0:n.clipboardData))try{n.clipboardData.setData("text/plain",s),n.clipboardData.setData("text/html","<table>".concat(d.outerHTML,"</table>"))}catch(D){null==(o=window.navigator.clipboard)||o.writeText(s)}}else null==(l=window.navigator.clipboard)||l.writeText(s);null==n||n.preventDefault()}function dr(e){return"string"===typeof e?e:"".concat(e,"px")}var fr=(0,v.d)("div")({name:"Wrapper",class:"wzg2m5k",vars:{"wzg2m5k-0":[function(e){return e.innerWidth}],"wzg2m5k-1":[function(e){return e.innerHeight}]}}),hr=function(e){var t=e.inWidth,n=e.inHeight,r=e.children,i=(0,c.Z)(e,we);return g.createElement(fr,(0,s.Z)({innerHeight:dr(n),innerWidth:dr(t)},i),r)};function pr(e){return!0!==e}var vr={getAccessibilityString:function(e){var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:"false"},kind:Te.Boolean,needsHover:!0,useLabel:!1,needsHoverPosition:!0,measure:function(){return 50},draw:function(e){var t;return function(e,t,n,r){if(n||t!==Le){var i=e.ctx,o=e.hoverAmount,a=e.theme,l=e.rect,u=e.highlighted,s=e.hoverX,c=e.hoverY,d=e.cell.contentAlign,f=l.x,h=l.y,p=l.width,v=l.height,g=n?.65+.35*o:.4;t===Le&&(g*=o),0!==g&&(i.globalAlpha=g,qt(i,a,t,f,h,p,v,u,s,c,r,d),i.globalAlpha=1)}}(e,e.cell.data,nt(e.cell),null!=(t=e.cell.maxSize)?t:20)},onDelete:function(e){return(0,s.Z)((0,s.Z)({},e),{},{data:!1})},onClick:function(e){var t,n,r=e.cell,i=e.posX,o=e.posY,a=e.bounds,l=e.theme,u=a.width,c=a.height,d=a.x,f=a.y,h=null!=(t=r.maxSize)?t:20,p=Math.floor(a.y+c/2),v=kt(h,c,l.cellVerticalPadding),g=xt(null!=(n=r.contentAlign)?n:"center",d,u,l.cellHorizontalPadding,v),m=wt(g,p,v),y=Dt(d+i,f+o,m);if(nt(r)&&y)return(0,s.Z)((0,s.Z)({},r),{},{data:pr(r.data)})},onPaste:function(e,t){var n=Le;return"true"===e.toLowerCase()?n=!0:"false"===e.toLowerCase()?n=!1:"indeterminate"===e.toLowerCase()&&(n=ze),n===t.data?void 0:(0,s.Z)((0,s.Z)({},t),{},{data:n})}};var gr=(0,v.d)("div")({name:"BubblesOverlayEditorStyle",class:"b1bqsp5z"}),mr=function(e){var t=e.bubbles;return g.createElement(gr,null,t.map((function(e,t){return g.createElement("div",{key:t,className:"boe-bubble"},e)})),g.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},yr={getAccessibilityString:function(e){var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:Te.Bubble,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:function(e,t,n){return t.data.reduce((function(t,n){return e.measureText(n).width+t+20}),0)+2*n.cellHorizontalPadding-4},draw:function(e){return function(e,t){var n,r=e.rect,i=e.theme,o=e.ctx,l=e.highlighted,s=r.x,c=r.y,d=r.width,f=r.height,h=s+i.cellHorizontalPadding,p=[],v=(0,a.Z)(t);try{for(v.s();!(n=v.n()).done;){var g=n.value;if(h>s+d)break;var m=Nt(g,o,"".concat(i.baseFontStyle," ").concat(i.fontFamily)).width;p.push({x:h,width:m}),h+=m+16+4}}catch(E){v.e(E)}finally{v.f()}o.beginPath();for(var y=0,b=p;y<b.length;y++){var w=b[y];Jt(o,w.x,c+(f-20)/2,w.width+16,20,10)}o.fillStyle=l?i.bgBubbleSelected:i.bgBubble,o.fill();var x,k=(0,a.Z)(p.entries());try{for(k.s();!(x=k.n()).done;){var D=(0,u.Z)(x.value,2),C=D[0],S=D[1];o.beginPath(),o.fillStyle=i.textBubble,o.fillText(t[C],S.x+8,c+f/2+Wt(o,i))}}catch(E){k.e(E)}finally{k.f()}}(e,e.cell.data)},provideEditor:function(){return function(e){var t=e.value;return g.createElement(mr,{bubbles:t.data})}},onPaste:function(){}},br=(0,v.d)("div")({name:"DrilldownOverlayEditorStyle",class:"df2kt4a"}),wr=function(e){var t=e.drilldowns;return g.createElement(br,null,t.map((function(e,t){return g.createElement("div",{key:t,className:"doe-bubble"},void 0!==e.img&&g.createElement("img",{src:e.img}),g.createElement("div",null,e.text))})))},xr={getAccessibilityString:function(e){return e.data.map((function(e){return e.text})).join(", ")},kind:Te.Drilldown,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:function(e,t,n){return t.data.reduce((function(t,n){return e.measureText(n.text).width+t+20+(void 0!==n.img?18:0)}),0)+2*n.cellHorizontalPadding-4},draw:function(e){return tn(e,e.cell.data)},provideEditor:function(){return function(e){var t=e.value;return g.createElement(wr,{drilldowns:t.data})}},onPaste:function(){}},kr=(0,v.d)("div")({name:"ImageOverlayEditorStyle",class:"i1eozt10"}),Dr=function(e){var t=e.urls,n=e.canWrite,r=e.onEditClick,i=e.renderImage,o=t.filter((function(e){return""!==e}));if(0===o.length)return null;var a=o.length>1;return g.createElement(kr,{"data-testid":"GDG-default-image-overlay-editor"},g.createElement(R.lr,{showArrows:a,showThumbs:!1,swipeable:a,emulateTouch:a,infiniteLoop:a},o.map((function(e){var t,n=null!=(t=null==i?void 0:i(e))?t:g.createElement("img",{draggable:!1,src:e});return g.createElement("div",{className:"centering-container",key:e},n)}))),n&&r&&g.createElement("button",{className:"edit-icon",onClick:r},g.createElement(Ct,null)))},Cr={getAccessibilityString:function(e){return e.data.join(", ")},kind:Te.Image,needsHover:!1,useLabel:!1,needsHoverPosition:!1,draw:function(e){var t;return function(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:4,r=arguments.length>3?arguments[3]:void 0,i=e.rect,o=e.col,a=e.row,l=e.theme,u=e.ctx,s=e.imageLoader,c=i.x,d=i.y,f=i.height,h=i.width,p=f-2*l.cellVerticalPadding,v=[],g=0,m=0;m<t.length;m++){var y=t[m];if(0!==y.length){var b=s.loadOrGetImage(y,o,a);void 0!==b&&(v[m]=b,g+=b.width*(p/b.height)+4)}}if(0!==g){g-=4;var w=c+l.cellHorizontalPadding;"right"===r?w=Math.floor(c+h-l.cellHorizontalPadding-g):"center"===r&&(w=Math.floor(c+h/2-g/2));for(var x=0,k=v;x<k.length;x++){var D=k[x];if(void 0!==D){var C=D.width*(p/D.height);n>0&&(Jt(u,w,d+l.cellVerticalPadding,C,p,n),u.save(),u.clip()),u.drawImage(D,w,d+l.cellVerticalPadding,C,p),n>0&&u.restore(),w+=C+4}}}}(e,null!=(t=e.cell.displayData)?t:e.cell.data,e.cell.rounding,e.cell.contentAlign)},measure:function(e,t){return 50*t.data.length},onDelete:function(e){return(0,s.Z)((0,s.Z)({},e),{},{data:[]})},provideEditor:function(){return function(e){var t=e.value,n=e.onFinishedEditing,r=e.imageEditorOverride,i=null!=r?r:Dr;return g.createElement(i,{urls:t.data,canWrite:t.allowAdd,onCancel:n,onChange:function(e){n((0,s.Z)((0,s.Z)({},t),{},{data:[e]}))}})}},onPaste:function(e,t){var n=(e=e.trim()).split(",").map((function(e){try{return new URL(e),e}catch(t){return}})).filter((function(e){return void 0!==e}));if(n.length!==t.data.length||!n.every((function(e,n){return e===t.data[n]})))return(0,s.Z)((0,s.Z)({},t),{},{data:n})}},Sr={getAccessibilityString:function(){return""},kind:Te.Loading,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:function(){return 120},draw:function(){},onPaste:function(){}},Er=(0,v.d)("div")({name:"MarkdownContainer",class:"mlbeo71"}),Fr=function(e){(0,h.Z)(n,e);var t=(0,p.Z)(n);function n(){var e;return(0,d.Z)(this,n),(e=t.apply(this,arguments)).targetElement=null,e.containerRefHook=function(t){e.targetElement=t,e.renderMarkdownIntoDiv()},e}return(0,f.Z)(n,[{key:"renderMarkdownIntoDiv",value:function(){var e=this.targetElement,t=this.props;if(null!==e){var n=t.contents,r=t.createNode,i=ve(n),o=document.createRange();o.selectNodeContents(e),o.deleteContents();var l=null==r?void 0:r(i);if(void 0===l){var u=document.createElement("template");u.innerHTML=i,l=u.content}e.append(l);var s,c=e.getElementsByTagName("a"),d=(0,a.Z)(c);try{for(d.s();!(s=d.n()).done;){var f=s.value;f.target="_blank",f.rel="noreferrer noopener"}}catch(h){d.e(h)}finally{d.f()}}}},{key:"render",value:function(){return this.renderMarkdownIntoDiv(),g.createElement(Er,{ref:this.containerRefHook})}}]),n}(g.PureComponent),Mr=(0,v.d)("textarea")({name:"InputBox",class:"ijuk0po"}),Zr=(0,v.d)("div")({name:"ShadowBox",class:"saq3p5l"}),Ar=(0,v.d)("div")({name:"GrowingEntryStyle",class:"gf8vzix"}),Rr=0,Ir=function(e){var t=e.placeholder,n=e.value,r=e.onKeyDown,i=e.highlight,o=e.altNewline,a=e.validatedSelection,l=(0,c.Z)(e,xe),d=l.onChange,f=l.className,h=g.useRef(null),p=null!=n?n:"";De(void 0!==d,"GrowingEntry must be a controlled input area");var v=g.useState((function(){return"input-box-"+(Rr=(Rr+1)%1e7)})),m=(0,u.Z)(v,1)[0];g.useEffect((function(){var e=h.current;if(null!==e&&!e.disabled){var t=p.toString().length;e.focus(),e.setSelectionRange(i?0:t,t)}}),[]),g.useLayoutEffect((function(){var e;if(void 0!==a){var t="number"===typeof a?[a,null]:a;null==(e=h.current)||e.setSelectionRange(t[0],t[1])}}),[a]);var y=g.useCallback((function(e){"Enter"===e.key&&e.shiftKey&&!0===o||null==r||r(e)}),[o,r]);return g.createElement(Ar,{className:"gdg-growing-entry"},g.createElement(Zr,{className:f},p+"\n"),g.createElement(Mr,(0,s.Z)((0,s.Z)({},l),{},{className:(null!=f?f:"")+" gdg-input",id:m,ref:h,onKeyDown:y,value:p,placeholder:t,dir:"auto"})))},Tr=(0,v.d)("div")({name:"MarkdownOverlayEditorStyle",class:"mdwzdl1",vars:{"mdwzdl1-0":[function(e){return e.targetWidth},"px"]}}),Or=function(e){var t=e.value,n=e.onChange,r=e.forceEditMode,i=e.createNode,o=e.targetRect,a=e.onFinish,l=e.validatedSelection,s=t.data,c=!0===t.readonly,d=g.useState(""===s||r),f=(0,u.Z)(d,2),h=f[0],p=f[1],v=g.useCallback((function(){p((function(e){return!e}))}),[]),m=s?"ml-6":"";return h?g.createElement(Tr,{targetWidth:o.width-20},g.createElement(Ir,{autoFocus:!0,highlight:!1,validatedSelection:l,value:s,onKeyDown:function(e){"Enter"===e.key&&e.stopPropagation()},onChange:n}),g.createElement("div",{className:"edit-icon checkmark-hover ".concat(m),onClick:function(){return a(t)}},g.createElement(St,null))):g.createElement(Tr,{targetWidth:o.width},g.createElement(Fr,{contents:s,createNode:i}),!c&&g.createElement(g.Fragment,null,g.createElement("div",{className:"spacer"}),g.createElement("div",{className:"edit-icon edit-hover ".concat(m),onClick:v},g.createElement(Ct,null))),g.createElement("textarea",{className:"md-edit-textarea gdg-input",autoFocus:!0}))},Pr={getAccessibilityString:function(e){var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:Te.Markdown,needsHover:!1,needsHoverPosition:!1,drawPrep:Xt,measure:function(e,t,n){var r=t.data.split("\n")[0];return e.measureText(r).width+2*n.cellHorizontalPadding},draw:function(e){return Gt(e,e.cell.data,e.cell.contentAlign)},onDelete:function(e){return(0,s.Z)((0,s.Z)({},e),{},{data:""})},provideEditor:function(){return function(e){var t=e.onChange,n=e.value,r=e.target,i=e.onFinishedEditing,o=e.markdownDivCreateNode,a=e.forceEditMode,l=e.validatedSelection;return g.createElement(Or,{onFinish:i,targetRect:r,value:n,validatedSelection:l,onChange:function(e){return t((0,s.Z)((0,s.Z)({},n),{},{data:e.target.value}))},forceEditMode:a,createNode:o})}},onPaste:function(e,t){return e===t.data?void 0:(0,s.Z)((0,s.Z)({},t),{},{data:e})}},Hr={getAccessibilityString:function(e){return e.row.toString()},kind:Ne.Marker,needsHover:!0,needsHoverPosition:!1,drawPrep:function(e,t){var n=e.ctx,r=e.theme,i="9px ".concat(r.fontFamily),o=null!=t?t:{};return(null==o?void 0:o.font)!==i&&(n.font=i,o.font=i),o.deprep=Qt,n.textAlign="center",o},measure:function(){return 44},draw:function(e){return function(e,t,n,r,i){var o=e.ctx,a=e.rect,l=e.hoverAmount,u=e.theme,s=a.x,c=a.y,d=a.width,f=a.height,h=n?1:"checkbox-visible"===r?.6+.4*l:l;if("number"!==r&&h>0){o.globalAlpha=h;var p=7*(n?l:1);if(qt(o,u,n,i?s+p:s,c,i?d-p:d,f,!0,void 0,void 0,18),i){o.globalAlpha=l,o.beginPath();for(var v=0,g=[3,6];v<g.length;v++)for(var m=g[v],y=0,b=[-5,-1,3];y<b.length;y++){var w=b[y];o.rect(s+m,c+f/2+w,2,2)}o.fillStyle=u.textLight,o.fill(),o.beginPath()}o.globalAlpha=1}if("number"===r||"both"===r&&!n){var x=t.toString(),k="".concat(u.markerFontStyle," ").concat(u.fontFamily),D=s+d/2;"both"===r&&0!==l&&(o.globalAlpha=1-l),o.fillStyle=u.textLight,o.font=k,o.fillText(x,D,c+f/2+Wt(o,k)),0!==l&&(o.globalAlpha=1)}}(e,e.cell.row,e.cell.checked,e.cell.markerKind,e.cell.drawHandle)},onClick:function(e){var t=e.bounds,n=e.cell,r=e.posX,i=e.posY,o=t.width,a=t.height,l=n.drawHandle?7+(o-7)/2:o/2,u=a/2;if(Math.abs(r-l)<=10&&Math.abs(i-u)<=10)return(0,s.Z)((0,s.Z)({},n),{},{checked:!n.checked})},onPaste:function(){}},Lr={getAccessibilityString:function(){return""},kind:Ne.NewRow,needsHover:!0,needsHoverPosition:!1,measure:function(){return 200},draw:function(e){return function(e,t,n){var r=e.ctx,i=e.rect,o=e.hoverAmount,a=e.theme,l=e.spriteManager,u=i.x,s=i.y,c=i.width,d=i.height;r.beginPath(),r.globalAlpha=o,r.rect(u,s,c,d),r.fillStyle=a.bgHeaderHovered,r.fill(),r.globalAlpha=1,r.beginPath();var f=""!==t,h=0;if(void 0!==n){var p=d-8,v=u+4,g=s+4;l.drawSprite(n,"normal",r,v,g,p,a,f?1:o),h=p}else{h=24;var m=f?12:12*o,y=f?0:12*(1-o)*.5,b=a.cellHorizontalPadding+4;m>0&&(r.moveTo(u+b+y,s+d/2),r.lineTo(u+b+y+m,s+d/2),r.moveTo(u+b+y+.5*m,s+d/2-.5*m),r.lineTo(u+b+y+.5*m,s+d/2+.5*m),r.lineWidth=2,r.strokeStyle=a.bgIconHeader,r.lineCap="round",r.stroke())}r.fillStyle=a.textMedium,r.fillText(t,h+u+a.cellHorizontalPadding+.5,s+d/2+Wt(r,a)),r.beginPath()}(e,e.cell.hint,e.cell.icon)},onPaste:function(){}},zr=g.lazy((0,o.Z)((0,i.Z)().mark((function e(){return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.e(1011).then(n.bind(n,41011));case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))),Br={getAccessibilityString:function(e){var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:Te.Number,needsHover:!1,needsHoverPosition:!1,useLabel:!0,drawPrep:Xt,draw:function(e){return Gt(e,e.cell.displayData,e.cell.contentAlign)},measure:function(e,t){return e.measureText(t.displayData).width+16},onDelete:function(e){return(0,s.Z)((0,s.Z)({},e),{},{data:void 0})},provideEditor:function(){return function(e){var t=e.isHighlighted,n=e.onChange,r=e.value,i=e.validatedSelection;return g.createElement(g.Suspense,{fallback:null},g.createElement(zr,{highlight:t,disabled:!0===r.readonly,value:r.data,fixedDecimals:r.fixedDecimals,allowNegative:r.allowNegative,thousandSeparator:r.thousandSeparator,decimalSeparator:r.decimalSeparator,validatedSelection:i,onChange:function(e){var t;return n((0,s.Z)((0,s.Z)({},r),{},{data:Number.isNaN(null!=(t=e.floatValue)?t:0)?0:e.floatValue}))}}))}},onPaste:function(e,t){var n=Number.parseFloat(e);if(!Number.isNaN(n)&&t.data!==n)return(0,s.Z)((0,s.Z)({},t),{},{data:n})}},_r={getAccessibilityString:function(){return""},measure:function(){return 108},kind:Te.Protected,needsHover:!1,needsHoverPosition:!1,draw:function(e){var t=e.ctx,n=e.theme,r=e.rect,i=r.x,o=r.y,a=r.height;t.beginPath();for(var l=2.5,u=i+n.cellHorizontalPadding+l,s=o+a/2,c=Math.cos(bt(30))*l,d=Math.sin(bt(30))*l,f=0;f<12;f++)t.moveTo(u,s-l),t.lineTo(u,s+l),t.moveTo(u+c,s-d),t.lineTo(u-c,s+d),t.moveTo(u-c,s-d),t.lineTo(u+c,s+d),u+=8;t.lineWidth=1.1,t.lineCap="square",t.strokeStyle=n.textLight,t.stroke()},onPaste:function(){}},Vr={getAccessibilityString:function(e){var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:Te.RowID,needsHover:!1,needsHoverPosition:!1,drawPrep:function(e,t){return Xt(e,t,e.theme.textLight)},draw:function(e){return Gt(e,e.cell.data,e.cell.contentAlign)},measure:function(e,t){return e.measureText(t.data).width+16},provideEditor:function(){return function(e){var t=e.isHighlighted,n=e.onChange,r=e.value,i=e.validatedSelection;return g.createElement(Ir,{highlight:t,autoFocus:!0!==r.readonly,disabled:!1!==r.readonly,value:r.data,validatedSelection:i,onChange:function(e){return n((0,s.Z)((0,s.Z)({},r),{},{data:e.target.value}))}})}},onPaste:function(){}},Nr={getAccessibilityString:function(e){var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:Te.Text,needsHover:!1,needsHoverPosition:!1,drawPrep:Xt,useLabel:!0,draw:function(e){return Gt(e,e.cell.displayData,e.cell.contentAlign,e.cell.allowWrapping,e.hyperWrapping),!0},measure:function(e,t,n){var r=t.displayData.split("\n").slice(0,!0===t.allowWrapping?void 0:1);return Math.max.apply(Math,(0,l.Z)(r.map((function(t){return e.measureText(t).width+2*n.cellHorizontalPadding}))))},onDelete:function(e){return(0,s.Z)((0,s.Z)({},e),{},{data:""})},provideEditor:function(e){return{disablePadding:!0===e.allowWrapping,editor:function(t){var n=t.isHighlighted,r=t.onChange,i=t.value,o=t.validatedSelection;return g.createElement(Ir,{style:!0===e.allowWrapping?{padding:"3px 8.5px"}:void 0,highlight:n,autoFocus:!0!==i.readonly,disabled:!0===i.readonly,altNewline:!0,value:i.data,validatedSelection:o,onChange:function(e){return r((0,s.Z)((0,s.Z)({},i),{},{data:e.target.value}))}})}}},onPaste:function(e,t){return e===t.data?void 0:(0,s.Z)((0,s.Z)({},t),{},{data:e})}},Wr=(0,v.d)("div")({name:"UriOverlayEditorStyle",class:"uf0sjo8"}),jr=function(e){var t=e.uri,n=e.onChange,r=e.forceEditMode,i=e.readonly,o=e.validatedSelection,a=e.preview,l=g.useState(""===t||r),s=(0,u.Z)(l,2),c=s[0],d=s[1],f=g.useCallback((function(){d(!0)}),[]);return c?g.createElement(Ir,{validatedSelection:o,highlight:!0,autoFocus:!0,value:t,onChange:n}):g.createElement(Wr,null,g.createElement("a",{className:"link-area",href:t,target:"_blank",rel:"noopener noreferrer"},a),!i&&g.createElement("div",{className:"edit-icon",onClick:f},g.createElement(Ct,null)),g.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},Ur={getAccessibilityString:function(e){var t,n;return null!=(n=null==(t=e.data)?void 0:t.toString())?n:""},kind:Te.Uri,needsHover:!1,needsHoverPosition:!1,useLabel:!0,drawPrep:Xt,draw:function(e){return Gt(e,e.cell.data,e.cell.contentAlign)},measure:function(e,t,n){return e.measureText(t.data).width+2*n.cellHorizontalPadding},onDelete:function(e){return(0,s.Z)((0,s.Z)({},e),{},{data:""})},provideEditor:function(){return function(e){var t,n=e.onChange,r=e.value,i=e.forceEditMode,o=e.validatedSelection;return g.createElement(jr,{forceEditMode:i,uri:r.data,preview:null!=(t=r.displayData)?t:r.data,validatedSelection:o,readonly:!0===r.readonly,onChange:function(e){return n((0,s.Z)((0,s.Z)({},r),{},{data:e.target.value}))}})}},onPaste:function(e,t){return e===t.data?void 0:(0,s.Z)((0,s.Z)({},t),{},{data:e})}},Xr=(ge={},(0,r.Z)(ge,Ne.Marker,Hr),(0,r.Z)(ge,Ne.NewRow,Lr),(0,r.Z)(ge,Te.Boolean,vr),(0,r.Z)(ge,Te.Bubble,yr),(0,r.Z)(ge,Te.Drilldown,xr),(0,r.Z)(ge,Te.Image,Cr),(0,r.Z)(ge,Te.Loading,Sr),(0,r.Z)(ge,Te.Markdown,Pr),(0,r.Z)(ge,Te.Number,Br),(0,r.Z)(ge,Te.Protected,_r),(0,r.Z)(ge,Te.RowID,Vr),(0,r.Z)(ge,Te.Text,Nr),(0,r.Z)(ge,Te.Uri,Ur),ge),Yr=0;function Kr(e,t){return void 0===e||0===t||0===e.columns.length&&void 0===e.current?e:{current:void 0===e.current?void 0:{cell:[e.current.cell[0]+t,e.current.cell[1]],range:(0,s.Z)((0,s.Z)({},e.current.range),{},{x:e.current.range.x+t}),rangeStack:e.current.rangeStack.map((function(e){return(0,s.Z)((0,s.Z)({},e),{},{x:e.x+t})}))},rows:e.rows,columns:e.columns.offset(t)}}var $r={selectAll:!0,selectRow:!0,selectColumn:!0,downFill:!1,rightFill:!1,pageUp:!1,pageDown:!1,clear:!0,copy:!0,paste:!0,search:!1,first:!0,last:!0},Gr={kind:Te.Loading,allowOverlay:!1},qr={columns:it.empty(),rows:it.empty(),current:void 0},Qr=g.forwardRef((function(e,t){var n,r,c,d,f,h=g.useState(qr),p=(0,u.Z)(h,2),v=p[0],k=p[1],D=g.useState(),C=(0,u.Z)(D,2),S=C[0],E=C[1],F=g.useRef(null),M=g.useRef(null),Z=g.useState(),A=(0,u.Z)(Z,2),R=A[0],I=A[1],T=g.useRef(null),O=g.useRef(),P=e.rowMarkers,H=void 0===P?"none":P,L=e.rowMarkerWidth,z=e.imageEditorOverride,B=e.getRowThemeOverride,_=e.markdownDivCreateNode,V=e.width,N=e.height,W=e.columns,j=e.rows,U=e.getCellContent,X=e.onCellClicked,Y=e.onCellActivated,K=e.onFinishedEditing,$=e.coercePasteValue,G=e.drawHeader,q=e.onHeaderClicked,Q=e.spanRangeBehavior,J=void 0===Q?"default":Q,ee=e.onGroupHeaderClicked,te=e.onCellContextMenu,ne=e.className,re=e.onHeaderContextMenu,ie=e.getCellsForSelection,oe=e.onGroupHeaderContextMenu,ae=e.onGroupHeaderRenamed,le=e.onCellEdited,ue=e.onCellsEdited,se=e.onSearchResultsChanged,ce=e.searchResults,de=e.onSearchValueChange,fe=e.searchValue,he=e.onKeyDown,pe=e.onKeyUp,ve=e.keybindings,ge=e.onRowAppended,me=e.onColumnMoved,ye=e.validateCell,be=e.highlightRegions,we=e.drawCell,xe=e.rangeSelect,ke=void 0===xe?"rect":xe,Se=e.columnSelect,Ee=void 0===Se?"multi":Se,Fe=e.rowSelect,Ze=void 0===Fe?"multi":Fe,Ie=e.rangeSelectionBlending,Oe=void 0===Ie?"exclusive":Ie,Pe=e.columnSelectionBlending,He=void 0===Pe?"exclusive":Pe,Le=e.rowSelectionBlending,ze=void 0===Le?"exclusive":Le,We=e.onDelete,je=e.onDragStart,Ue=e.onMouseMove,qe=e.onPaste,nt=e.copyHeaders,rt=void 0!==nt&&nt,ot=e.freezeColumns,at=void 0===ot?0:ot,ut=e.rowSelectionMode,st=void 0===ut?"auto":ut,ct=e.rowMarkerStartIndex,dt=void 0===ct?1:ct,ft=e.rowMarkerTheme,ht=e.onHeaderMenuClick,pt=e.getGroupDetails,vt=e.onSearchClose,yt=e.onItemHovered,bt=e.onSelectionCleared,wt=e.showSearch,xt=e.onVisibleRegionChanged,kt=e.gridSelection,Dt=e.onGridSelectionChange,Ct=e.minColumnWidth,St=void 0===Ct?50:Ct,Et=e.maxColumnWidth,Ft=void 0===Et?500:Et,Mt=e.maxColumnAutoWidth,Zt=e.provideEditor,At=e.trailingRowOptions,Ot=e.scrollOffsetX,Pt=e.scrollOffsetY,Ht=e.verticalBorder,Lt=e.onDragOverCell,zt=e.onDrop,Bt=e.onColumnResize,_t=e.onColumnResizeEnd,Vt=e.onColumnResizeStart,Nt=e.customRenderers,Wt=e.fillHandle,jt=e.drawFocusRing,Ut=e.experimental,Xt=e.fixedShadowX,Yt=e.fixedShadowY,Kt=e.headerIcons,$t=e.imageWindowLoader,Gt=e.initialSize,qt=e.isDraggable,Qt=e.onDragLeave,Jt=e.onRowMoved,en=e.overscrollX,tn=e.overscrollY,nn=e.preventDiagonalScrolling,rn=e.rightElement,on=e.rightElementProps,an=e.showMinimap,ln=e.smoothScrollX,un=e.smoothScrollY,sn=e.scrollToEnd,cn=e.scaleToRem,dn=void 0!==cn&&cn,fn=e.rowHeight,hn=void 0===fn?34:fn,pn=e.headerHeight,vn=void 0===pn?36:pn,gn=e.groupHeaderHeight,mn=void 0===gn?vn:gn,yn=e.theme,bn=e.isOutsideClick,wn=Math.max(St,20),xn=Math.max(Ft,wn),kn=Math.max(null!=Mt?Mt:xn,wn),Dn=g.useMemo((function(){return"undefined"===typeof window?{fontSize:"16px"}:window.getComputedStyle(document.documentElement)}),[]).fontSize,Cn=g.useMemo((function(){return Number.parseFloat(Dn)}),[Dn]),Sn=g.useMemo((function(){var e,t,n;if(!dn||16===Cn)return[hn,vn,mn,yn,en,tn];var r=Cn/16,i=hn,o=Ae();return["number"===typeof i?i*r:function(e){return Math.ceil(i(e)*r)},Math.ceil(vn*r),Math.ceil(mn*r),(0,s.Z)((0,s.Z)({},yn),{},{headerIconSize:(null!=(e=null==yn?void 0:yn.headerIconSize)?e:o.headerIconSize)*r,cellHorizontalPadding:(null!=(t=null==yn?void 0:yn.cellHorizontalPadding)?t:o.cellHorizontalPadding)*r,cellVerticalPadding:(null!=(n=null==yn?void 0:yn.cellVerticalPadding)?n:o.cellVerticalPadding)*r}),Math.ceil((null!=en?en:0)*r),Math.ceil((null!=tn?tn:0)*r)]}),[mn,vn,en,tn,Cn,hn,dn,yn]),En=(0,u.Z)(Sn,6),Fn=En[0],Mn=En[1],Zn=En[2],An=En[3],Rn=En[4],In=En[5],Tn=g.useMemo((function(){return void 0===ve?$r:(0,s.Z)((0,s.Z)({},$r),ve)}),[ve]),On=null!=L?L:j>1e4?48:j>1e3?44:j>100?36:32,Pn="none"!==H,Hn=Pn?1:0,Ln=void 0!==ge,Bn=!0===(null==At?void 0:At.sticky),_n=g.useState(!1),Vn=(0,u.Z)(_n,2),Nn=Vn[0],Wn=Vn[1],jn=null!=wt?wt:Nn,Un=g.useCallback((function(){void 0!==vt?vt():Wn(!1)}),[vt]),Xn=g.useMemo((function(){return void 0===kt?void 0:Kr(kt,Hn)}),[kt,Hn]),Yn=null!=Xn?Xn:v,Kn=g.useRef(new AbortController);g.useEffect((function(){return function(){null==Kn||Kn.current.abort()}}),[]);var $n=function(e,t,n,r,a){var u=g.useCallback((function(n){var i;if(!0===e){for(var o=[],l=n.y;l<n.y+n.height;l++){for(var u=[],s=n.x;s<n.x+n.width;s++)s<0||l>=a?u.push({kind:Te.Loading,allowOverlay:!1}):u.push(t([s,l]));o.push(u)}return o}return null!=(i=null==e?void 0:e(n,r.signal))?i:[]}),[r.signal,t,e,a]),c=void 0!==e?u:void 0,d=g.useCallback((function(e){if(void 0===c)return[];var t=(0,s.Z)((0,s.Z)({},e),{},{x:e.x-n});if(t.x<0){t.x=0,t.width--;var a=c(t,r.signal);return"function"===typeof a?(0,o.Z)((0,i.Z)().mark((function e(){return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a();case 2:return e.abrupt("return",e.sent.map((function(e){return[{kind:Te.Loading,allowOverlay:!1}].concat((0,l.Z)(e))})));case 3:case"end":return e.stop()}}),e)}))):a.map((function(e){return[{kind:Te.Loading,allowOverlay:!1}].concat((0,l.Z)(e))}))}return c(t,r.signal)}),[r.signal,c,n]);return[void 0!==e?d:void 0,c]}(ie,U,Hn,Kn.current,j),Gn=(0,u.Z)($n,2),qn=Gn[0],Jn=Gn[1],ir=g.useCallback((function(e,t,n){if(void 0===ye)return!0;var r=[e[0]-Hn,e[1]];return null==ye?void 0:ye(r,t,n)}),[Hn,ye]),lr=g.useRef(kt),ur=g.useCallback((function(e,t){t&&(e=function(e,t,n,r,i){var o,u,s=e;if("allowPartial"===r||void 0===e.current)return e;if(void 0!==t){var c=!1;do{if(void 0===(null==e?void 0:e.current))break;var d=null==(o=e.current)?void 0:o.range,f=[];if(d.width>2){var h=t({x:d.x,y:d.y,width:1,height:d.height},i.signal);if("function"===typeof h)return s;f.push.apply(f,(0,l.Z)(h));var p=t({x:d.x+d.width-1,y:d.y,width:1,height:d.height},i.signal);if("function"===typeof p)return s;f.push.apply(f,(0,l.Z)(p))}else{var v=t({x:d.x,y:d.y,width:d.width,height:d.height},i.signal);if("function"===typeof v)return s;f.push.apply(f,(0,l.Z)(v))}for(var g=d.x-n,m=d.x+d.width-1-n,y=0,b=f;y<b.length;y++){var w,x=b[y],k=(0,a.Z)(x);try{for(k.s();!(w=k.n()).done;){var D=w.value;void 0!==D.span&&(g=Math.min(D.span[0],g),m=Math.max(D.span[1],m))}}catch(C){k.e(C)}finally{k.f()}}g===d.x-n&&m===d.x+d.width-1-n?c=!0:e={current:{cell:null!=(u=e.current.cell)?u:[0,0],range:{x:g+n,y:d.y,width:m-g+1,height:d.height},rangeStack:e.current.rangeStack},columns:e.columns,rows:e.rows}}while(!c)}return e}(e,qn,Hn,J,Kn.current)),void 0!==Dt?(lr.current=Kr(e,-Hn),Dt(lr.current)):k(e)}),[Dt,qn,Hn,J]),sr=mt(Bt,g.useCallback((function(e,t,n,r){null==Bt||Bt(W[n-Hn],t,n-Hn,r)}),[Bt,Hn,W])),dr=mt(_t,g.useCallback((function(e,t,n,r){null==_t||_t(W[n-Hn],t,n-Hn,r)}),[_t,Hn,W])),fr=mt(Vt,g.useCallback((function(e,t,n,r){null==Vt||Vt(W[n-Hn],t,n-Hn,r)}),[Vt,Hn,W])),vr=mt(G,g.useCallback((function(e){var t;return null!=(t=null==G?void 0:G((0,s.Z)((0,s.Z)({},e),{},{columnIndex:e.columnIndex-Hn})))&&t}),[G,Hn])),gr=g.useCallback((function(e){if(void 0!==We){var t=We(Kr(e,-Hn));return"boolean"===typeof t?t:Kr(t,Hn)}return!0}),[We,Hn]),mr=function(e,t,n,r,i,o){return[g.useCallback((function(a,u,c,d){var f,h;"cell"!==o&&"multi-cell"!==o||void 0===a||(a=(0,s.Z)((0,s.Z)({},a),{},{range:{x:a.cell[0],y:a.cell[1],width:1,height:1}}));var p="mixed"===n&&(c||"drag"===d),v="mixed"===r&&p,g="mixed"===i&&p,m={current:void 0===a?void 0:(0,s.Z)((0,s.Z)({},a),{},{rangeStack:"drag"===d&&null!=(h=null==(f=e.current)?void 0:f.rangeStack)?h:[]}),columns:v?e.columns:it.empty(),rows:g?e.rows:it.empty()};c&&("multi-rect"===o||"multi-cell"===o)&&void 0!==m.current&&void 0!==e.current&&(m=(0,s.Z)((0,s.Z)({},m),{},{current:(0,s.Z)((0,s.Z)({},m.current),{},{rangeStack:[].concat((0,l.Z)(e.current.rangeStack),[e.current.range])})})),t(m,u)}),[r,e,n,o,i,t]),g.useCallback((function(o,a,l){var u;if(o=null!=o?o:e.rows,void 0!==a&&(o=o.add(a)),"exclusive"===i&&o.length>0)u={current:void 0,columns:it.empty(),rows:o};else{var s=l&&"mixed"===r;u={current:l&&"mixed"===n?e.current:void 0,columns:s?e.columns:it.empty(),rows:o}}t(u,!1)}),[r,e,n,i,t]),g.useCallback((function(o,a,l){var u;if(o=null!=o?o:e.columns,void 0!==a&&(o=o.add(a)),"exclusive"===r&&o.length>0)u={current:void 0,rows:it.empty(),columns:o};else{var s=l&&"mixed"===i;u={current:l&&"mixed"===n?e.current:void 0,rows:s?e.rows:it.empty(),columns:o}}t(u,!1)}),[r,e,n,i,t])]}(Yn,ur,Oe,He,ze,ke),yr=(0,u.Z)(mr,3),br=yr[0],wr=yr[1],xr=yr[2],kr=g.useMemo((function(){return(0,s.Z)((0,s.Z)({},Ae()),An)}),[An]),Dr=g.useState([10,10,0]),Cr=(0,u.Z)(Dr,2),Sr=Cr[0],Er=Cr[1],Fr=g.useCallback((function(e){return e.kind!==Te.Custom?Xr[e.kind]:null==Nt?void 0:Nt.find((function(t){return t.isMatch(e)}))}),[Nt]),Mr=function(e,t,n,r,c,d,f,h,p){var v=g.useRef(t),m=g.useRef(n),y=g.useRef(f);v.current=t,m.current=n,y.current=f;var b=g.useMemo((function(){if("undefined"===typeof window)return[null,null];var e=document.createElement("canvas");return e.style.display="none",e.style.opacity="0",e.style.position="fixed",[e,e.getContext("2d",{alpha:!1})]}),[]),w=(0,u.Z)(b,2),x=w[0],k=w[1];g.useLayoutEffect((function(){return x&&document.documentElement.append(x),function(){null==x||x.remove()}}),[x]);var D=g.useRef({}),C=g.useRef(),S=g.useState(),E=(0,u.Z)(S,2),F=E[0],M=E[1];return g.useLayoutEffect((function(){var t=m.current;if(void 0!==t&&!e.every($e)){var n=Math.max(1,10-Math.floor(e.length/1e4)),r=0;n<v.current&&n>1&&(n--,r=1);var a={x:0,y:0,width:e.length,height:Math.min(v.current,n)},u={x:0,y:v.current-1,width:e.length,height:1},s=function(){var n=(0,o.Z)((0,i.Z)().mark((function n(){var o,s,c;return(0,i.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(o=t(a,p.signal),s=r>0?t(u,p.signal):void 0,"object"!==typeof o){n.next=6;break}c=o,n.next=9;break;case 6:return n.next=8,Ge(o);case 8:c=n.sent;case 9:if(void 0===s){n.next=22;break}if("object"!==typeof s){n.next=14;break}c=[].concat((0,l.Z)(c),(0,l.Z)(s)),n.next=22;break;case 14:return n.t0=[],n.t1=(0,l.Z)(c),n.t2=l.Z,n.next=19,Ge(s);case 19:n.t3=n.sent,n.t4=(0,n.t2)(n.t3),c=n.t0.concat.call(n.t0,n.t1,n.t4);case 22:C.current=e,M(c);case 24:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}();s()}}),[p.signal,e]),g.useMemo((function(){var t,n,i=e.every($e)?e:null===k?e.map((function(e){return $e(e)?e:(0,s.Z)((0,s.Z)({},e),{},{width:tr})})):(k.font="".concat(y.current.baseFontStyle," ").concat(y.current.fontFamily),e.map((function(t,n){if($e(t))return t;if(void 0!==D.current[t.id])return(0,s.Z)((0,s.Z)({},t),{},{width:D.current[t.id]});if(void 0===F||C.current!==e||void 0===t.id)return(0,s.Z)((0,s.Z)({},t),{},{width:tr});var r=nr(k,f,t,n,F,c,d,!0,h);return D.current[t.id]=r.width,r}))),o=0,p=0,v=[],g=(0,a.Z)(i.entries());try{for(g.s();!(n=g.n()).done;){var m=(0,u.Z)(n.value,2),b=m[0],w=m[1];o+=w.width,void 0!==w.grow&&w.grow>0&&(p+=w.grow,v.push(b))}}catch(I){g.e(I)}finally{g.f()}if(o<r&&v.length>0){for(var x=(0,l.Z)(i),S=r-o,E=S,M=0;M<v.length;M++){var Z=v[M],A=(null!=(t=i[Z].grow)?t:0)/p,R=M===v.length-1?E:Math.min(E,Math.floor(S*A));x[Z]=(0,s.Z)((0,s.Z)({},i[Z]),{},{growOffset:R,width:i[Z].width+R}),E-=R}i=x}return i}),[r,e,k,F,f,c,d,h])}(W,j,Jn,Sr[0]-(0===Hn?0:On)-Sr[2],wn,kn,kr,Fr,Kn.current),Zr=g.useMemo((function(){return Mr.some((function(e){return void 0!==e.group}))}),[Mr]),Ar=Zr?Mn+Zn:Mn,Rr=Yn.rows.length,Ir="none"===H?"":0===Rr?Ye:Rr===j?Xe:Ke,Tr=g.useMemo((function(){return"none"===H?Mr:[{title:Ir,width:On,icon:void 0,hasMenu:!1,style:"normal",themeOverride:ft}].concat((0,l.Z)(Mr))}),[Mr,On,H,Ir,ft]),Or=g.useMemo((function(){return[void 0!==Pt&&"number"===typeof Fn?Math.floor(Pt/Fn):0,void 0!==Pt&&"number"===typeof Fn?-Pt%Fn:0]}),[Pt,Fn]),Pr=(0,u.Z)(Or,2),Hr=Pr[0],Lr=Pr[1],zr=g.useRef({height:1,width:1,x:0,y:0}),Br=g.useMemo((function(){var e,t;return{x:zr.current.x,y:Hr,width:null!=(e=zr.current.width)?e:1,height:null!=(t=zr.current.height)?t:1,ty:Lr}}),[Lr,Hr]),_r=g.useRef(!1),Vr=function(e){var t=g.useRef([It,e]);t.current[1]!==e&&(t.current[0]=e),t.current[1]=e;var n=g.useState(e),r=(0,u.Z)(n,2),i=r[0],o=r[1],a=g.useState(),l=(0,u.Z)(a,2)[1],s=g.useCallback((function(e){var n=t.current[0];n!==It&&(e="function"===typeof e?e(n):e)===n||(n!==It&&l({}),o((function(t){return"function"===typeof e?e(n===It?t:n):e})),t.current[0]=It)}),[]),c=g.useCallback((function(){t.current[0]=It,l({})}),[]);return[t.current[0]===It?i:t.current[0],s,c]}(Br),Nr=(0,u.Z)(Vr,3),Wr=Nr[0],jr=Nr[1],Ur=Nr[2],Qr=(null!=(n=Wr.height)?n:1)>1;g.useLayoutEffect((function(){if(void 0!==Pt&&null!==T.current&&Qr){if(T.current.scrollTop===Pt)return;T.current.scrollTop=Pt,T.current.scrollTop!==Pt&&Ur(),_r.current=!0}}),[Pt,Qr,Ur]);var Jr=(null!=(r=Wr.width)?r:1)>1;g.useLayoutEffect((function(){if(void 0!==Ot&&null!==T.current&&Jr){if(T.current.scrollLeft===Ot)return;T.current.scrollLeft=Ot,T.current.scrollLeft!==Ot&&Ur(),_r.current=!0}}),[Ot,Jr,Ur]);var ei=Wr.x+Hn,ti=Wr.y,ni=g.useRef(null),ri=g.useCallback((function(e){var t;!0===e?null==(t=ni.current)||t.focus():window.requestAnimationFrame((function(){var e;null==(e=ni.current)||e.focus()}))}),[]),ii=Ln?j+1:j,oi=g.useCallback((function(e){var t=0===Hn?e:e.map((function(e){return(0,s.Z)((0,s.Z)({},e),{},{location:[e.location[0]-Hn,e.location[1]]})})),n=null==ue?void 0:ue(t);if(!0!==n){var r,i=(0,a.Z)(t);try{for(i.s();!(r=i.n()).done;){var o=r.value;null==le||le(o.location,o.value)}}catch(l){i.e(l)}finally{i.f()}}return n}),[le,ue,Hn]),ai=g.useMemo((function(){if(void 0!==be)return 0===Hn?be:be.map((function(e){var t=Tr.length-e.range.x-Hn;if(!(t<=0))return{color:e.color,range:(0,s.Z)((0,s.Z)({},e.range),{},{x:e.range.x+Hn,width:Math.min(t,e.range.width)}),style:e.style}})).filter((function(e){return void 0!==e}))}),[be,Tr.length,Hn]),li=g.useRef(Tr);li.current=Tr;var ui=g.useCallback((function(e){var t,n,r,i,o,a,l,c,d,f,h=(0,u.Z)(e,2),p=h[0],v=h[1],g=arguments.length>1&&void 0!==arguments[1]&&arguments[1],m=Ln&&v===ii-1;if(0===p&&Pn)return m?Gr:{kind:Ne.Marker,allowOverlay:!1,checked:!0===(null==Yn?void 0:Yn.rows.hasIndex(v)),markerKind:"clickable-number"===H?"number":H,row:dt+v,drawHandle:void 0!==Jt,cursor:"clickable-number"===H?"pointer":void 0};if(m){var y=p===Hn&&null!=(t=null==At?void 0:At.hint)?t:"",b=li.current[p];if(!0===(null==(n=null==b?void 0:b.trailingRowOptions)?void 0:n.disabled))return Gr;var w=null!=(i=null==(r=null==b?void 0:b.trailingRowOptions)?void 0:r.hint)?i:y,x=null!=(a=null==(o=null==b?void 0:b.trailingRowOptions)?void 0:o.addIcon)?a:null==At?void 0:At.addIcon;return{kind:Ne.NewRow,hint:w,allowOverlay:!1,icon:x}}var k=p-Hn;if(g||!0===(null==Ut?void 0:Ut.strict)){var D=zr.current,C=D.x>k||k>D.x+D.width||D.y>v||v>D.y+D.height,S=k===(null==(c=null==(l=D.extras)?void 0:l.selected)?void 0:c[0])&&v===(null==(d=D.extras)?void 0:d.selected[1]),E=void 0===(null==(f=D.extras)?void 0:f.freezeRegion)||D.extras.freezeRegion.x>k||k>D.extras.freezeRegion.x+D.extras.freezeRegion.width||D.extras.freezeRegion.y>v||v>D.extras.freezeRegion.y+D.extras.freezeRegion.height;if(C&&!S&&E)return{kind:Te.Loading,allowOverlay:!1}}var F=U([k,v]);return 0!==Hn&&void 0!==F.span&&(F=(0,s.Z)((0,s.Z)({},F),{},{span:[F.span[0]+Hn,F.span[1]+Hn]})),F}),[Ln,ii,Pn,null==Yn?void 0:Yn.rows,Jt,H,Hn,null==At?void 0:At.hint,null==At?void 0:At.addIcon,null==Ut?void 0:Ut.strict,U,dt]),si=g.useCallback((function(e){var t,n,r=null!=(t=null==pt?void 0:pt(e))?t:{name:e};return void 0!==ae&&""!==e&&(r={icon:r.icon,name:r.name,overrideTheme:r.overrideTheme,actions:[].concat((0,l.Z)(null!=(n=r.actions)?n:[]),[{title:"Rename",icon:"renameIcon",onClick:function(e){return Ri({group:r.name,bounds:e.bounds})}}])}),r}),[pt,ae]),ci=g.useCallback((function(e){var t,n=(0,u.Z)(e.cell,2),r=n[0],i=n[1],o=Tr[r],a=void 0!==(null==o?void 0:o.group)?null==(t=si(o.group))?void 0:t.overrideTheme:void 0,l=null==o?void 0:o.themeOverride,c=null==B?void 0:B(i);E((0,s.Z)((0,s.Z)({},e),{},{theme:(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},kr),a),l),c),e.content.themeOverride)}))}),[B,Tr,si,kr]),di=g.useCallback((function(e,t,n){var r;if(void 0!==Yn.current){var i=(0,u.Z)(Yn.current.cell,2),o=i[0],a=i[1],l=ui([o,a]);if(l.kind!==Te.Boolean&&l.allowOverlay){var c=l;if(void 0!==n)switch(c.kind){case Te.Number:var d=function(e,t){try{return e()}catch(n){return t}}((function(){return"-"===n?-0:Number.parseFloat(n)}),0);c=(0,s.Z)((0,s.Z)({},c),{},{data:Number.isNaN(d)?0:d});break;case Te.Text:case Te.Markdown:case Te.Uri:c=(0,s.Z)((0,s.Z)({},c),{},{data:n})}ci({target:e,content:c,initialValue:n,cell:[o,a],highlight:void 0===n,forceEditMode:void 0!==n})}else l.kind===Te.Boolean&&t&&!0!==l.readonly&&(oi([{location:Yn.current.cell,value:(0,s.Z)((0,s.Z)({},l),{},{data:pr(l.data)})}]),null==(r=ni.current)||r.damage([{cell:Yn.current.cell}]))}}),[ui,Yn,oi,ci]),fi=g.useCallback((function(e,t){var n,r=null==(n=ni.current)?void 0:n.getBounds(e,t);if(void 0!==r&&null!==T.current){var i=ui([e,t]);i.allowOverlay&&ci({target:r,content:i,initialValue:void 0,highlight:!0,cell:[e,t],forceEditMode:!0})}}),[ui,ci]),hi=g.useCallback((function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"both",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0;if(null!==T.current){var l=ni.current,u=M.current,c="number"!==typeof e?"cell"===e.unit?e.amount:void 0:e,d="number"!==typeof t?"cell"===t.unit?t.amount:void 0:t,f="number"!==typeof e&&"px"===e.unit?e.amount:void 0,h="number"!==typeof t&&"px"===t.unit?t.amount:void 0;if(null!==l&&null!==u){var p={x:0,y:0,width:0,height:0},v=0,g=0;if((void 0!==c||void 0!==d)&&(0===(p=null!=(n=l.getBounds((null!=c?c:0)+Hn,null!=d?d:0))?n:p).width||0===p.height))return;var m=u.getBoundingClientRect(),y=m.width/u.offsetWidth;if(void 0!==f&&(p=(0,s.Z)((0,s.Z)({},p),{},{x:f-m.left-T.current.scrollLeft,width:1})),void 0!==h&&(p=(0,s.Z)((0,s.Z)({},p),{},{y:h+m.top-T.current.scrollTop,height:1})),void 0!==p){for(var b={x:p.x-i,y:p.y-o,width:p.width+2*i,height:p.height+2*o},w=0,x=0;x<at;x++)w+=Mr[x].width;var k=0;Bn&&(k="number"===typeof Fn?Fn:Fn(j));var D=w*y+m.left+Hn*On*y,C=m.right,S=m.top+Ar*y,E=m.bottom-k*y,F=p.width+2*i;switch(null==a?void 0:a.hAlign){case"start":C=D+F;break;case"end":D=C-F;break;case"center":C=(D=Math.floor((D+C)/2)-F/2)+F}var Z=p.height+2*o;switch(null==a?void 0:a.vAlign){case"start":E=S+Z;break;case"end":S=E-Z;break;case"center":E=(S=Math.floor((S+E)/2)-Z/2)+Z}D>b.x?v=b.x-D:C<b.x+b.width&&(v=b.x+b.width-C),S>b.y?g=b.y-S:E<b.y+b.height&&(g=b.y+b.height-E),"vertical"===r||"number"===typeof e&&e<at?v=0:"horizontal"===r&&(g=0),0===v&&0===g||(1!==y&&(v/=y,g/=y),T.current.scrollTo(v+T.current.scrollLeft,g+T.current.scrollTop))}}}}),[Hn,On,Ar,Bn,at,Mr,Fn,j]),pi=g.useRef(fi),vi=g.useRef(U),gi=g.useRef(j);pi.current=fi,vi.current=U,gi.current=j;var mi=g.useCallback(function(){var e=(0,o.Z)((0,i.Z)().mark((function e(t){var n,r,o,a,l,u,s,c=arguments;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=!(c.length>1&&void 0!==c[1])||c[1],o=Tr[t],!0!==(null==(r=null==o?void 0:o.trailingRowOptions)?void 0:r.disabled)){e.next=4;break}return e.abrupt("return");case 4:if(a=null==ge?void 0:ge(),l=void 0,u=!0,void 0===a){e.next=13;break}return e.next=10,a;case 10:"top"===(l=e.sent)&&(u=!1),"number"===typeof l&&(u=!1);case 13:s=0,function e(){if(gi.current<=j)return s<500&&window.setTimeout(e,s),void(s=50+2*s);var r="number"===typeof l?l:u?j:0;hi(t-Hn,r),br({cell:[t,r],range:{x:t,y:r,width:1,height:1}},!1,!1,"edit");var i=vi.current([t-Hn,r]);i.allowOverlay&&et(i)&&!0!==i.readonly&&n&&window.setTimeout((function(){pi.current(t,r)}),0)}();case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[Tr,ge,Hn,j,hi,br]),yi=g.useCallback((function(e){var t,n,r,i=null!=(r=null==(n=null==(t=Mr[e])?void 0:t.trailingRowOptions)?void 0:n.targetColumn)?r:null==At?void 0:At.targetColumn;if("number"===typeof i)return i+(Pn?1:0);if("object"===typeof i){var o=W.indexOf(i);if(o>=0)return o+(Pn?1:0)}}),[Mr,W,Pn,null==At?void 0:At.targetColumn]),bi=g.useRef(),wi=g.useRef(),xi=g.useCallback((function(e,t){var n,r=(0,u.Z)(t,2),i=r[0],o=r[1];return(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},kr),null==(n=Tr[i])?void 0:n.themeOverride),null==B?void 0:B(o)),e.themeOverride)}),[B,Tr,kr]),ki=g.useCallback((function(e){var t,n,r,i,o=zn.value?e.metaKey:e.ctrlKey,a=o&&"multi"===Ze,l=o&&"multi"===Ee,c=(0,u.Z)(e.location,2),d=c[0],f=c[1],h=Yn.columns,p=Yn.rows,v=null!=(n=null==(t=Yn.current)?void 0:t.cell)?n:[],g=(0,u.Z)(v,2),m=g[0],y=g[1];if("cell"===e.kind){if(wi.current=void 0,Ci.current=[d,f],0===d&&Pn){if(!0===Ln&&f===j||"number"===H||"none"===Ze)return;var b=ui(e.location);if(b.kind!==Ne.Marker)return;if(void 0!==Jt){var w=Fr(b);De((null==w?void 0:w.kind)===Ne.Marker);var x=null==(r=null==w?void 0:w.onClick)?void 0:r.call(w,(0,s.Z)((0,s.Z)({},e),{},{cell:b,posX:e.localEventX,posY:e.localEventY,bounds:e.bounds,theme:xi(b,e.location),preventDefault:function(){}}));if(void 0===x||x.checked===b.checked)return}E(void 0),ri();var k=p.hasIndex(f),D=bi.current;if("multi"===Ze&&(e.shiftKey||!0===e.isLongTouch)&&void 0!==D&&p.hasIndex(D)){var C=[Math.min(D,f),Math.max(D,f)+1];a||"multi"===st?wr(void 0,C,!0):wr(it.fromSingleSelection(C),void 0,a)}else a||e.isTouch||"multi"===st?k?wr(p.remove(f),void 0,!0):(wr(void 0,f,!0),bi.current=f):k&&1===p.length?wr(it.empty(),void 0,o):(wr(it.fromSingleSelection(f),void 0,o),bi.current=f)}else if(d>=Hn&&Ln&&f===j){var S=yi(d);mi(null!=S?S:d)}else if(m!==d||y!==f){var F=ui(e.location),M=Fr(F);if(void 0!==(null==M?void 0:M.onSelect)){var Z=!1;if(M.onSelect((0,s.Z)((0,s.Z)({},e),{},{cell:F,posX:e.localEventX,posY:e.localEventY,bounds:e.bounds,preventDefault:function(){return Z=!0},theme:xi(F,e.location)})),Z)return}var A=Bn&&f===j,R=Bn&&void 0!==Yn&&(null==(i=Yn.current)?void 0:i.cell[1])===j;if(!e.shiftKey&&!0!==e.isLongTouch||void 0===m||void 0===y||void 0===Yn.current||R)br({cell:[d,f],range:{x:d,y:f,width:1,height:1}},!0,o,"click"),bi.current=void 0,E(void 0),ri();else{if(A)return;var I=Math.min(d,m),T=Math.max(d,m),O=Math.min(f,y),P=Math.max(f,y);br((0,s.Z)((0,s.Z)({},Yn.current),{},{range:{x:I,y:O,width:T-I+1,height:P-O+1}}),!0,o,"click"),bi.current=void 0,ri()}}}else if("header"===e.kind)if(Ci.current=[d,f],E(void 0),Pn&&0===d)bi.current=void 0,wi.current=void 0,"multi"===Ze&&(p.length!==j?wr(it.fromSingleSelection([0,j]),void 0,o):wr(it.empty(),void 0,o),ri());else{var L=wi.current;if("multi"===Ee&&(e.shiftKey||!0===e.isLongTouch)&&void 0!==L&&h.hasIndex(L)){var z=[Math.min(L,d),Math.max(L,d)+1];l?xr(void 0,z,o):xr(it.fromSingleSelection(z),void 0,o)}else l?(h.hasIndex(d)?xr(h.remove(d),void 0,o):xr(void 0,d,o),wi.current=d):"none"!==Ee&&(xr(it.fromSingleSelection(d),void 0,o),wi.current=d);bi.current=void 0,ri()}else e.kind===_e?Ci.current=[d,f]:e.kind===Ve&&(ur(qr,!1),E(void 0),ri(),null==bt||bt(),bi.current=void 0,wi.current=void 0)}),[mi,Ee,ri,Fr,yi,ui,Yn,Pn,Bn,bt,Jt,Hn,H,Ze,st,j,br,ur,xr,wr,Ln,xi]),Di=g.useRef(!1),Ci=g.useRef(),Si=g.useRef(Wr),Ei=g.useRef(),Fi=g.useCallback((function(e){var t,n;if(Oi.current=!1,Si.current=zr.current,0===e.button||1===e.button){var r=performance.now(),i=r-(null!=(n=null==(t=Ei.current)?void 0:t.time)?n:-1e3)<250;Ei.current={wasDoubleClick:i,button:e.button,time:r,location:e.location},"header"===(null==e?void 0:e.kind)&&(Di.current=!0);var o="cell"===e.kind&&e.isFillHandle;!o&&"cell"!==e.kind&&e.isEdge||(I({previousSelection:Yn,fillHandle:o}),Ci.current=void 0,e.isTouch||0!==e.button?e.isTouch||1!==e.button||(Ci.current=e.location):ki(e))}else Ei.current=void 0}),[Yn,ki]),Mi=g.useState(),Zi=(0,u.Z)(Mi,2),Ai=Zi[0],Ri=Zi[1],Ii=g.useCallback((function(e){if(e.kind===_e&&"multi"===Ee){var t=zn.value?e.metaKey:e.ctrlKey,n=(0,u.Z)(e.location,1)[0],r=Yn.columns;if(!(n<Hn)){for(var i=Tr[n],o=n,a=n,l=n-1;l>=Hn&&Tt(i.group,Tr[l].group);l--)o--;for(var s=n+1;s<Tr.length&&Tt(i.group,Tr[s].group);s++)a++;if(ri(),t)if(r.hasAll([o,a+1])){for(var c=r,d=o;d<=a;d++)c=c.remove(d);xr(c,void 0,t)}else xr(void 0,[o,a+1],t);else xr(it.fromSingleSelection([o,a+1]),void 0,t)}}}),[Ee,ri,Yn.columns,Tr,Hn,xr]),Ti=g.useCallback((function(e){var t;if(void 0!==Yn.current){for(var n=[],r=Yn.current.range,i=0;i<r.width;i++){var o=i+r.x,a=ui([o,e?r.y+r.height-1:r.y]);if(!Je(a)&&et(a))for(var l=1;l<r.height;l++){var u=[o,e?r.y+r.height-(l+1):l+r.y];n.push({location:u,value:(0,s.Z)({},a)})}}oi(n),null==(t=ni.current)||t.damage(n.map((function(e){return{cell:e.location}})))}}),[ui,Yn,oi]),Oi=g.useRef(!1),Pi=g.useCallback(function(){var e=(0,o.Z)((0,i.Z)().mark((function e(t){var n,r,o,a,l,u,s,c,d,f=arguments;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=f.length>1&&void 0!==f[1]&&f[1],!0!==(null==(r=Ei.current)?void 0:r.wasDoubleClick)&&!n||void 0===qn||void 0===sr){e.next=13;break}if(o=zr.current.y,a=zr.current.height,"object"===typeof(l=qn({x:t,y:o,width:1,height:Math.min(a,j-o)},Kn.current.signal))){e.next=9;break}return e.next=8,l();case 8:l=e.sent;case 9:u=Mr[t-Hn],s=document.createElement("canvas"),null!==(c=s.getContext("2d",{alpha:!1}))&&(c.font="".concat(kr.baseFontStyle," ").concat(kr.fontFamily),d=nr(c,kr,u,0,l,wn,xn,!1,Fr),null==sr||sr(u,d.width,t,d.width));case 13:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[Mr,qn,xn,kr,wn,sr,Hn,j,Fr]),Hi=g.useState(),Li=(0,u.Z)(Hi,2),zi=Li[0],Bi=Li[1],_i=g.useCallback((function(e,t){var n,r,i,o=R;if(I(void 0),Bi(void 0),Di.current=!1,!t)if(!0!==(null==o?void 0:o.fillHandle)||void 0===Yn.current){var a=(0,u.Z)(e.location,2),l=a[0],c=a[1],d=null!=(n=Ci.current)?n:[],f=(0,u.Z)(d,2),h=f[0],p=f[1],v=function(){Oi.current=!0},g=function(t){var n,r,i;if((t.isTouch||h===l&&p===c)&&(null==X||X([l-Hn,c],(0,s.Z)((0,s.Z)({},t),{},{preventDefault:v}))),1===t.button)return!Oi.current;if(!Oi.current){var a=ui(e.location),d=Fr(a);if(void 0!==d&&void 0!==d.onClick){var f=d.onClick((0,s.Z)((0,s.Z)({},t),{},{cell:a,posX:t.localEventX,posY:t.localEventY,bounds:t.bounds,theme:xi(a,e.location),preventDefault:v}));void 0!==f&&!Je(f)&&Qe(f)&&(oi([{location:t.location,value:f}]),null==(n=ni.current)||n.damage([{cell:t.location}]))}if(!Oi.current&&void 0!==(null==(i=null==(r=null==o?void 0:o.previousSelection)?void 0:r.current)?void 0:i.cell)&&void 0!==Yn.current){var g=(0,u.Z)(Yn.current.cell,2),m=g[0],y=g[1],b=(0,u.Z)(o.previousSelection.current.cell,2),w=b[0],x=b[1];if(l===m&&l===w&&c===y&&c===x)return null==Y||Y([l-Hn,c]),di(t.bounds,!1),!0}}return!1},m=e.location[0]-Hn;if(e.isTouch){var y=zr.current,b=Si.current;if(y.x!==b.x||y.y!==b.y)return;if(!0===e.isLongTouch){if("cell"===e.kind&&(null==(r=null==Yn?void 0:Yn.current)?void 0:r.cell[0])===l&&(null==(i=null==Yn?void 0:Yn.current)?void 0:i.cell[1])===c)return void(null==te||te([m,e.location[1]],(0,s.Z)((0,s.Z)({},e),{},{preventDefault:v})));if("header"===e.kind&&Yn.columns.hasIndex(l))return void(null==re||re(m,(0,s.Z)((0,s.Z)({},e),{},{preventDefault:v})));if(e.kind===_e){if(m<0)return;return void(null==oe||oe(m,(0,s.Z)((0,s.Z)({},e),{},{preventDefault:v})))}}"cell"===e.kind?g(e)||ki(e):e.kind===_e?null==ee||ee(m,(0,s.Z)((0,s.Z)({},e),{},{preventDefault:v})):(e.kind===Be&&(null==q||q(m,(0,s.Z)((0,s.Z)({},e),{},{preventDefault:v}))),ki(e))}else{if("header"===e.kind){if(m<0)return;e.isEdge?Pi(l):0===e.button&&l===h&&c===p&&(null==q||q(m,(0,s.Z)((0,s.Z)({},e),{},{preventDefault:v})))}if(e.kind===_e){if(m<0)return;0===e.button&&l===h&&c===p&&(null==ee||ee(m,(0,s.Z)((0,s.Z)({},e),{},{preventDefault:v})),Oi.current||Ii(e))}"cell"!==e.kind||0!==e.button&&1!==e.button||g(e),Ci.current=void 0}}else Ti(Yn.current.cell[1]!==Yn.current.range.y)}),[R,Hn,Yn,X,Ti,ui,Fr,xi,oi,Y,di,te,re,oe,ki,ee,Pi,q,Ii]),Vi=g.useCallback((function(e){var t=(0,s.Z)((0,s.Z)({},e),{},{location:[e.location[0]-Hn,e.location[1]]});null==Ue||Ue(t),Bi((function(t){var n,r;return Di.current?[e.scrollEdge[0],0]:e.scrollEdge[0]===(null==t?void 0:t[0])&&e.scrollEdge[1]===t[1]?t:void 0===R||(null!=(r=null==(n=Ei.current)?void 0:n.location[0])?r:0)<Hn?void 0:e.scrollEdge}))}),[R,Ue,Hn]);!function(e,t){var n=g.useRef(0),r=null!=e?e:[0,0],i=(0,u.Z)(r,2),o=i[0],a=i[1];g.useEffect((function(){if(0!==o||0!==a){var e=0,r=window.requestAnimationFrame((function i(l){var u;if(0===e)e=l;else{var s=l-e;n.current=Math.min(1,n.current+s/1300);var c=Math.pow(n.current,1.618)*s*2;null==(u=t.current)||u.scrollBy(o*c,a*c),e=l}r=window.requestAnimationFrame(i)}));return function(){return window.cancelAnimationFrame(r)}}n.current=0}),[t,o,a])}(zi,T);var Ni=g.useCallback((function(e,t){null==ht||ht(e-Hn,t)}),[ht,Hn]),Wi=null==(c=null==Yn?void 0:Yn.current)?void 0:c.cell,ji=g.useCallback((function(e,t,n,r,i,o){_r.current=!1;var a=Wi;void 0!==a&&(a=[a[0]-Hn,a[1]]);var l={x:e.x-Hn,y:e.y,width:e.width,height:Ln&&e.y+e.height>=j?e.height-1:e.height,tx:i,ty:o,extras:{selected:a,freezeRegion:0===at?void 0:{x:0,y:e.y,width:at,height:e.height}}};zr.current=l,jr(l),Er([t,n,r]),null==xt||xt(l,l.tx,l.ty,l.extras)}),[Wi,Hn,Ln,j,at,jr,xt]),Ui=mt(me,g.useCallback((function(e,t){null==me||me(e-Hn,t-Hn),"none"!==Ee&&xr(it.fromSingleSelection(t),void 0,!0)}),[Ee,me,Hn,xr])),Xi=g.useRef(!1),Yi=g.useCallback((function(e){0===e.location[0]&&Hn>0?e.preventDefault():(null==je||je((0,s.Z)((0,s.Z)({},e),{},{location:[e.location[0]-Hn,e.location[1]]})),e.defaultPrevented()||(Xi.current=!0),I(void 0))}),[je,Hn]),Ki=g.useCallback((function(){Xi.current=!1}),[]),$i=g.useCallback((function(e){var t,n;if(!(void 0!==(null==(t=null==Ei?void 0:Ei.current)?void 0:t.button)&&Ei.current.button>=1)){if(void 0!==R&&0===(null==(n=Ei.current)?void 0:n.location[0])&&0===e.location[0]&&1===Hn&&"multi"===Ze&&R.previousSelection&&!R.previousSelection.rows.hasIndex(Ei.current.location[1])&&Yn.rows.hasIndex(Ei.current.location[1])){var r=Math.min(Ei.current.location[1],e.location[1]),i=Math.max(Ei.current.location[1],e.location[1])+1;wr(it.fromSingleSelection([r,i]),void 0,!1)}if(void 0!==R&&void 0!==Yn.current&&!Xi.current&&("rect"===ke||"multi-rect"===ke)){var o=(0,u.Z)(Yn.current.cell,2),a=o[0],l=o[1],c=(0,u.Z)(e.location,2),d=c[0],f=c[1];if(f<0&&(f=zr.current.y),Bn&&l===j)return;if(Bn&&f===j){if(e.kind!==Ve)return;f--}var h=(d=Math.max(d,Hn))-a,p=f-l,v={x:h>=0?a:d,y:p>=0?l:f,width:Math.abs(h)+1,height:Math.abs(p)+1};br((0,s.Z)((0,s.Z)({},Yn.current),{},{range:v}),!0,!1,"drag")}null==yt||yt((0,s.Z)((0,s.Z)({},e),{},{location:[e.location[0]-Hn,e.location[1]]}))}}),[R,Hn,Ze,Yn,ke,yt,wr,Bn,j,br]),Gi=g.useCallback((function(e){if(void 0!==Yn.current){var t=(0,u.Z)(e,2),n=t[0],r=t[1],i=(0,u.Z)(Yn.current.cell,2),o=i[0],a=i[1],l=Yn.current.range,s=l.x,c=l.x+l.width,d=l.y,f=l.y+l.height;if(0!==r)switch(r){case 2:d=a,hi(0,f=j,"vertical");break;case-2:f=a+1,hi(0,d=0,"vertical");break;case 1:d<a?(d++,hi(0,d,"vertical")):(f=Math.min(j,f+1),hi(0,f,"vertical"));break;case-1:f>a+1?(f--,hi(0,f,"vertical")):(d=Math.max(0,d-1),hi(0,d,"vertical"));break;default:Ce()}if(0!==n)if(2===n)c=Tr.length,s=o,hi(c-1-Hn,0,"horizontal");else if(-2===n)c=o+1,hi((s=Hn)-Hn,0,"horizontal");else{var h=[];if(void 0!==qn){var p=qn({x:s,y:d,width:c-s-Hn,height:f-d},Kn.current.signal);"object"===typeof p&&(h=function(e){return y(b(b(e).filter((function(e){return void 0!==e.span})).map((function(e){var t,n,r,i;return w((null!=(n=null==(t=e.span)?void 0:t[0])?n:0)+1,(null!=(i=null==(r=e.span)?void 0:r[1])?i:0)+1)}))))}(p))}if(1===n){var v=!1;if(s<o){if(h.length>0){var g=w(s+1,o+1).find((function(e){return!h.includes(e-Hn)}));void 0!==g&&(s=g,v=!0)}else s++,v=!0;v&&hi(s,0,"horizontal")}v||(c=Math.min(Tr.length,c+1),hi(c-1-Hn,0,"horizontal"))}else if(-1===n){var m=!1;if(c>o+1){if(h.length>0){var x=w(c-1,o,-1).find((function(e){return!h.includes(e-Hn)}));void 0!==x&&(c=x,m=!0)}else c--,m=!0;m&&hi(c-Hn,0,"horizontal")}m||(s=Math.max(Hn,s-1),hi(s-Hn,0,"horizontal"))}else Ce()}br({cell:Yn.current.cell,range:{x:s,y:d,width:c-s,height:f-d}},!0,!1,"keyboard-select")}}),[qn,Yn,Tr.length,Hn,j,hi,br]),qi=g.useCallback((function(e,t,n,r){var i=ii-(n?0:1);if(e=m(e,Hn,Mr.length-1+Hn),t=m(t,0,i),e===(null==Wi?void 0:Wi[0])&&t===(null==Wi?void 0:Wi[1]))return!1;if(r&&void 0!==Yn.current){var o=(0,l.Z)(Yn.current.rangeStack);(Yn.current.range.width>1||Yn.current.range.height>1)&&o.push(Yn.current.range),ur((0,s.Z)((0,s.Z)({},Yn),{},{current:{cell:[e,t],range:{x:e,y:t,width:1,height:1},rangeStack:o}}),!0)}else br({cell:[e,t],range:{x:e,y:t,width:1,height:1}},!0,!1,"keyboard-nav");return void 0!==O.current&&O.current[0]===e&&O.current[1]===t&&(O.current=void 0),hi(e-Hn,t),!0}),[ii,Hn,Mr.length,Wi,Yn,hi,ur,br]),Qi=g.useCallback((function(e,t){void 0!==(null==S?void 0:S.cell)&&void 0!==e&&Qe(e)&&(oi([{location:S.cell,value:e}]),window.requestAnimationFrame((function(){var e;null==(e=ni.current)||e.damage([{cell:S.cell}])}))),ri(!0),E(void 0);var n=(0,u.Z)(t,2),r=n[0],i=n[1];if(void 0!==Yn.current&&(0!==r||0!==i)){var o=Yn.current.cell[1]===ii-1&&void 0!==e;qi(m(Yn.current.cell[0]+r,0,Tr.length-1),m(Yn.current.cell[1]+i,0,ii-1),o,!1)}null==K||K(e,t)}),[null==S?void 0:S.cell,ri,Yn,K,oi,ii,qi,Tr.length]),Ji=g.useMemo((function(){return"gdg-overlay-".concat(Yr++)}),[]),eo=g.useCallback((function(e){var t=function(){var t=(0,o.Z)((0,i.Z)().mark((function t(){var n,r,o,l,c,d,f,h,p,v,g,m,y,b,w,x,k,D,C,M,Z,A,R,I,T,O,P,H,L,z,B,_,V,N,X,K,$,G,q,Q,J,ee,te,ne,re,ie,oe,ae,le;return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(I=function(e){var t,n,r,i;ri();for(var o=[],a=e.x;a<e.x+e.width;a++)for(var l=e.y;l<e.y+e.height;l++){var u=U([a-Hn,l]);if(u.allowOverlay||u.kind===Te.Boolean){var s=void 0;if(u.kind===Te.Custom){var c=Fr(u),d=null==(t=null==c?void 0:c.provideEditor)?void 0:t.call(c,u);void 0!==(null==c?void 0:c.onDelete)?s=c.onDelete(u):tt(d)&&(s=null==(n=null==d?void 0:d.deletedValue)?void 0:n.call(d,u))}else if(Qe(u)&&u.allowOverlay||u.kind===Te.Boolean){var f=Fr(u);s=null==(r=null==f?void 0:f.onDelete)?void 0:r.call(f,u)}void 0!==s&&!Je(s)&&Qe(s)&&o.push({location:[a,l],value:s})}}oi(o),null==(i=ni.current)||i.damage(o.map((function(e){return{cell:e.location}})))},d=!1,void 0!==he&&he((0,s.Z)((0,s.Z)({},e),{},{cancel:function(){d=!0}})),!d){t.next=5;break}return t.abrupt("return");case 5:if(f=function(){e.stopPropagation(),e.preventDefault()},h=void 0!==S,p=e.altKey,v=e.shiftKey,g=e.metaKey,m=e.ctrlKey,y=e.key,b=e.bounds,w=zn.value,x=w?g:m,k="Delete"===y||w&&"Backspace"===y,D=zr.current,C=Yn.columns,M=Yn.rows,"Escape"!==y){t.next=19;break}return h?E(void 0):Tn.clear&&(ur(qr,!1),null==bt||bt()),t.abrupt("return");case 19:if(!rr("primary+a",e)||!Tn.selectAll){t.next=25;break}return h?null!==(Z=document.getElementById(Ji))&&(A=window.getSelection(),(R=document.createRange()).selectNodeContents(Z),null==A||A.removeAllRanges(),null==A||A.addRange(R)):ur({columns:it.empty(),rows:it.empty(),current:{cell:null!=(r=null==(n=Yn.current)?void 0:n.cell)?r:[Hn,0],range:{x:Hn,y:0,width:W.length,height:j},rangeStack:[]}},!1),f(),t.abrupt("return");case 25:rr("primary+f",e)&&Tn.search&&(f(),null==(o=null==F?void 0:F.current)||o.focus({preventScroll:!0}),Wn(!0));case 26:if(!k){t.next=31;break}if(T=null==(l=null==gr?void 0:gr(Yn))||l,f(),!1!==T){if(void 0!==(O=!0===T?Yn:T).current){I(O.current.range),P=(0,a.Z)(O.current.rangeStack);try{for(P.s();!(H=P.n()).done;)L=H.value,I(L)}catch(i){P.e(i)}finally{P.f()}}z=(0,a.Z)(O.rows);try{for(z.s();!(B=z.n()).done;)_=B.value,I({x:Hn,y:_,width:Tr.length-Hn,height:1})}catch(i){z.e(i)}finally{z.f()}V=(0,a.Z)(O.columns);try{for(V.s();!(N=V.n()).done;)X=N.value,I({x:X,y:0,width:1,height:j})}catch(i){V.e(i)}finally{V.f()}}return t.abrupt("return");case 31:if(void 0!==Yn.current){t.next=33;break}return t.abrupt("return");case 33:if(K=(0,u.Z)(Yn.current.cell,2),$=K[0],G=K[1],q=!1,!Tn.selectColumn||!rr("ctrl+ ",e)||"none"===Ee){t.next=39;break}C.hasIndex($)?xr(C.remove($),void 0,!0):"single"===Ee?xr(it.fromSingleSelection($),void 0,!0):xr(void 0,$,!0),t.next=136;break;case 39:if(!Tn.selectRow||!rr("shift+ ",e)||"none"===Ze){t.next=43;break}M.hasIndex(G)?wr(M.remove(G),void 0,!0):"single"===Ze?wr(it.fromSingleSelection(G),void 0,!0):wr(void 0,G,!0),t.next=136;break;case 43:if(!(rr("Enter",e)||rr(" ",e)||rr("shift+Enter",e))||void 0===b){t.next=47;break}h?(E(void 0),rr("Enter",e)?G++:rr("shift+Enter",e)&&G--):G===j&&Ln?window.setTimeout((function(){var e=yi($);mi(null!=e?e:$)}),0):(null==Y||Y([$-Hn,G]),di(b,!0),f()),t.next=136;break;case 47:if(!(Tn.downFill&&rr("primary+_68",e)&&Yn.current.range.height>1)){t.next=52;break}Ti(!1),f(),t.next=136;break;case 52:if(!(Tn.rightFill&&rr("primary+_82",e)&&Yn.current.range.width>1)){t.next=70;break}Q=[],J=Yn.current.range,ee=0;case 56:if(!(ee<J.height)){t.next=65;break}if(te=ee+J.y,!Je(ne=ui([J.x,te]))&&et(ne)){t.next=61;break}return t.abrupt("continue",62);case 61:for(re=1;re<J.width;re++)ie=re+J.x,oe=[ie,te],Q.push({location:oe,value:(0,s.Z)({},ne)});case 62:ee++,t.next=56;break;case 65:oi(Q),null==(c=ni.current)||c.damage(Q.map((function(e){return{cell:e.location}}))),f(),t.next=136;break;case 70:if(!Tn.pageDown||!rr("PageDown",e)){t.next=75;break}G+=Math.max(1,zr.current.height-4),f(),t.next=136;break;case 75:if(!Tn.pageUp||!rr("PageUp",e)){t.next=80;break}G-=Math.max(1,zr.current.height-4),f(),t.next=136;break;case 80:if(!Tn.first||!rr("primary+Home",e)){t.next=86;break}E(void 0),G=0,$=0,t.next=136;break;case 86:if(!Tn.last||!rr("primary+End",e)){t.next=92;break}E(void 0),G=Number.MAX_SAFE_INTEGER,$=Number.MAX_SAFE_INTEGER,t.next=136;break;case 92:if(!Tn.first||!rr("primary+shift+Home",e)){t.next=97;break}E(void 0),Gi([-2,-2]),t.next=136;break;case 97:if(!Tn.last||!rr("primary+shift+End",e)){t.next=102;break}E(void 0),Gi([2,2]),t.next=136;break;case 102:if("ArrowDown"!==y){t.next=109;break}if(!m||!p){t.next=105;break}return t.abrupt("return");case 105:E(void 0),!v||"rect"!==ke&&"multi-rect"!==ke?(p&&!x&&(q=!0),x&&!p?G=j-1:G+=1):Gi([0,x&&!p?2:1]),t.next=136;break;case 109:if("ArrowUp"!==y&&"Home"!==y){t.next=115;break}ae="Home"===y||x,E(void 0),!v||"rect"!==ke&&"multi-rect"!==ke?(p&&!ae&&(q=!0),G+=ae&&!p?Number.MIN_SAFE_INTEGER:-1):Gi([0,ae&&!p?-2:-1]),t.next=136;break;case 115:if("ArrowRight"!==y&&"End"!==y){t.next=121;break}le="End"===y||x,E(void 0),!v||"rect"!==ke&&"multi-rect"!==ke?(p&&!le&&(q=!0),$+=le&&!p?Number.MAX_SAFE_INTEGER:1):Gi([le&&!p?2:1,0]),t.next=136;break;case 121:if("ArrowLeft"!==y){t.next=126;break}E(void 0),!v||"rect"!==ke&&"multi-rect"!==ke?(p&&!x&&(q=!0),$+=x&&!p?Number.MIN_SAFE_INTEGER:-1):Gi([x&&!p?-2:-1,0]),t.next=136;break;case 126:if("Tab"!==y){t.next=131;break}E(void 0),v?$--:$++,t.next=136;break;case 131:if(g||m||void 0===Yn.current||1!==y.length||!/[ -~]/g.test(y)||void 0===b||!et(U([$-Hn,Math.max(0,G-1)]))){t.next=136;break}if(Bn&&G===j||!(D.y>G||G>D.y+D.height||D.x>$||$>D.x+D.width)){t.next=134;break}return t.abrupt("return");case 134:di(b,!0,y),f();case 136:qi($,G,!1,q)&&f();case 138:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();t()}),[he,S,Yn,Tn.selectAll,Tn.search,Tn.selectColumn,Tn.selectRow,Tn.downFill,Tn.rightFill,Tn.pageDown,Tn.pageUp,Tn.first,Tn.last,Tn.clear,Ee,Ze,U,Hn,qi,ur,bt,W.length,j,Ji,ri,oi,Fr,gr,Tr.length,xr,wr,Ln,yi,mi,Y,di,Ti,ui,Gi,ke,Bn]),to=g.useCallback((function(e,t){var n=e.location[0]-Hn;if("header"===e.kind&&(null==re||re(n,(0,s.Z)((0,s.Z)({},e),{},{preventDefault:t}))),e.kind===_e){if(n<0)return;null==oe||oe(n,(0,s.Z)((0,s.Z)({},e),{},{preventDefault:t}))}if("cell"===e.kind){var r=(0,u.Z)(e.location,2),i=r[0],o=r[1];null==te||te([n,o],(0,s.Z)((0,s.Z)({},e),{},{preventDefault:t})),function(e,t){var n=(0,u.Z)(t,2),r=n[0],i=n[1];if(e.columns.hasIndex(r)||e.rows.hasIndex(i))return!0;if(void 0!==e.current){if(e.current.cell[0]===r&&e.current.cell[1]===i)return!0;var o,s=[e.current.range].concat((0,l.Z)(e.current.rangeStack)),c=(0,a.Z)(s);try{for(c.s();!(o=c.n()).done;){var d=o.value;if(r>=d.x&&r<d.x+d.width&&i>=d.y&&i<d.y+d.height)return!0}}catch(f){c.e(f)}finally{c.f()}}return!1}(Yn,e.location)||qi(i,o,!1,!1)}}),[Yn,te,oe,re,Hn,qi]),no=g.useCallback(function(){var e=(0,o.Z)((0,i.Z)().mark((function e(t){var n,r,o,l,c,d,f,h,p,v,g,m,y,b,w,x,k,D,C,S,E,F,Z,A,R,I,O,P,H,L,z,B,_,V,N,W,U,X,Y,K,G,q,Q,J,ee,te,ne;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(h=function(e,t,n){var r,i;if(!Je(e)&&et(e)&&!0!==e.readonly){var o=null==$?void 0:$(n,e);if(void 0!==o&&Qe(o))return o.kind!==e.kind&&console.warn("Coercion should not change cell kind."),{location:t,value:o};var a=Fr(e);if(void 0===a)return;if(a.kind===Te.Custom){De(e.kind===Te.Custom);var l=null==(r=a.onPaste)?void 0:r.call(a,n,e.data);if(void 0===l)return;return{location:t,value:(0,s.Z)((0,s.Z)({},e),{},{data:l})}}var u=null==(i=a.onPaste)?void 0:i.call(a,n,e);if(void 0===u)return;return De(u.kind===e.kind),{location:t,value:u}}},Tn.paste){e.next=3;break}return e.abrupt("return");case 3:if(p=Yn.columns,v=Yn.rows,g=!0===(null==(n=T.current)?void 0:n.contains(document.activeElement))||!0===(null==(r=M.current)?void 0:r.contains(document.activeElement)),void 0===(m=null==(o=Yn.current)?void 0:o.cell)&&1===p.length&&(m=[null!=(l=p.first())?l:0,0]),void 0===m&&1===v.length&&(m=[Hn,null!=(c=v.first())?c:0]),!g||void 0===m){e.next=99;break}if(w="text/plain",x="text/html",void 0===navigator.clipboard.read){e.next=52;break}return e.next=15,navigator.clipboard.read();case 15:k=e.sent,D=(0,a.Z)(k),e.prev=17,D.s();case 19:if((C=D.n()).done){e.next=42;break}if(!(S=C.value).types.includes(x)){e.next=34;break}return e.next=24,S.getType(x);case 24:return E=e.sent,e.next=27,E.text();case 27:if(F=e.sent,(Z=document.createElement("html")).innerHTML=F,null===(A=Z.querySelector("table"))){e.next=34;break}return y=ar(A),e.abrupt("break",42);case 34:if(!S.types.includes(w)){e.next=40;break}return e.next=37,S.getType(w);case 37:return e.next=39,e.sent.text();case 39:b=e.sent;case 40:e.next=19;break;case 42:e.next=47;break;case 44:e.prev=44,e.t0=e.catch(17),D.e(e.t0);case 47:return e.prev=47,D.f(),e.finish(47);case 50:e.next=64;break;case 52:if(void 0===navigator.clipboard.readText){e.next=58;break}return e.next=55,navigator.clipboard.readText();case 55:b=e.sent,e.next=64;break;case 58:if(void 0===t||null===(null==t?void 0:t.clipboardData)){e.next=63;break}t.clipboardData.types.includes(x)&&(R=t.clipboardData.getData(x),(I=document.createElement("html")).innerHTML=R,null!==(O=I.querySelector("table"))&&(y=ar(O))),void 0===y&&t.clipboardData.types.includes(w)&&(b=t.clipboardData.getData(w)),e.next=64;break;case 63:return e.abrupt("return");case 64:P=m,H=(0,u.Z)(P,2),L=H[0],z=H[1],B=[];case 66:if(void 0!==qe){e.next=71;break}return _=ui(m),void 0!==(V=h(_,m,null!=(d=null!=b?b:null==y?void 0:y.map((function(e){return e.join("\t")})).join("\t"))?d:""))&&B.push(V),e.abrupt("break",97);case 71:if(void 0!==y){e.next=75;break}if(void 0!==b){e.next=74;break}return e.abrupt("return");case 74:y=or(b);case 75:if(!1!==qe&&("function"!==typeof qe||!0===(null==qe?void 0:qe([m[0]-Hn,m[1]],y)))){e.next=77;break}return e.abrupt("return");case 77:N=(0,a.Z)(y.entries()),e.prev=78,N.s();case 80:if((W=N.n()).done){e.next=88;break}if(U=(0,u.Z)(W.value,2),X=U[0],Y=U[1],!(X+z>=j)){e.next=84;break}return e.abrupt("break",88);case 84:K=(0,a.Z)(Y.entries());try{for(K.s();!(G=K.n()).done;)q=(0,u.Z)(G.value,2),Q=q[0],J=q[1],te=ui(ee=[Q+L,X+z]),void 0!==(ne=h(te,ee,J))&&B.push(ne)}catch(i){K.e(i)}finally{K.f()}case 86:e.next=80;break;case 88:e.next=93;break;case 90:e.prev=90,e.t1=e.catch(78),N.e(e.t1);case 93:return e.prev=93,N.f(),e.finish(93);case 96:0;case 97:oi(B),null==(f=ni.current)||f.damage(B.map((function(e){return{cell:e.location}})));case 99:case"end":return e.stop()}}),e,null,[[17,44,47,50],[78,90,93,96]])})));return function(t){return e.apply(this,arguments)}}(),[$,Fr,ui,Yn,Tn.paste,oi,qe,Hn,j]);gt("paste",no,window,!1,!0);var ro=g.useCallback(function(){var e=(0,o.Z)((0,i.Z)().mark((function e(t,n){var r,o,u,s,c,d,f,h,p,v,g,m,y,b,x,k,D;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Tn.copy){e.next=2;break}return e.abrupt("return");case 2:if(u=!0===n||!0===(null==(r=T.current)?void 0:r.contains(document.activeElement))||!0===(null==(o=M.current)?void 0:o.contains(document.activeElement)),s=Yn.columns,c=Yn.rows,d=function(e,n){rt?cr([n.map((function(e){return{kind:Te.Text,data:W[e].title,displayData:W[e].title,allowOverlay:!1}}))].concat((0,l.Z)(e)),n,t):cr(e,n,t)},!u||void 0===qn){e.next=55;break}if(void 0===Yn.current){e.next=16;break}if("object"===typeof(f=qn(Yn.current.range,Kn.current.signal))){e.next=13;break}return e.next=12,f();case 12:f=e.sent;case 13:d(f,w(Yn.current.range.x-Hn,Yn.current.range.x+Yn.current.range.width-Hn)),e.next=55;break;case 16:if(!(void 0!==c&&c.length>0)){e.next=29;break}if(h=(0,l.Z)(c),p=h.map((function(e){var t=qn({x:Hn,y:e,width:W.length,height:1},Kn.current.signal);return"object"===typeof t?t[0]:t().then((function(e){return e[0]}))})),!p.some((function(e){return e instanceof Promise}))){e.next=26;break}return e.next=22,Promise.all(p);case 22:v=e.sent,d(v,w(W.length)),e.next=27;break;case 26:d(p,w(W.length));case 27:e.next=55;break;case 29:if(!(s.length>0)){e.next=55;break}g=[],m=[],y=(0,a.Z)(s),e.prev=33,y.s();case 35:if((b=y.n()).done){e.next=46;break}if(x=b.value,"object"===typeof(k=qn({x:x,y:0,width:1,height:j},Kn.current.signal))){e.next=42;break}return e.next=41,k();case 41:k=e.sent;case 42:g.push(k),m.push(x-Hn);case 44:e.next=35;break;case 46:e.next=51;break;case 48:e.prev=48,e.t0=e.catch(33),y.e(e.t0);case 51:return e.prev=51,y.f(),e.finish(51);case 54:1===g.length?d(g[0],m):(D=g.reduce((function(e,t){return e.map((function(e,n){return[].concat((0,l.Z)(e),(0,l.Z)(t[n]))}))})),d(D,m));case 55:case"end":return e.stop()}}),e,null,[[33,48,51,54]])})));return function(t,n){return e.apply(this,arguments)}}(),[W,qn,Yn,Tn.copy,Hn,j,rt]);gt("copy",ro,window,!1,!1);var io=g.useCallback((function(e,t){if(void 0!==se)return 0!==Hn&&(e=e.map((function(e){return[e[0]-Hn,e[1]]}))),void se(e,t);if(0!==e.length&&-1!==t){var n=(0,u.Z)(e[t],2),r=n[0],i=n[1];void 0!==O.current&&O.current[0]===r&&O.current[1]===i||(O.current=[r,i],qi(r,i,!1,!1))}}),[se,Hn,qi]),oo=null!=(f=null==(d=null==kt?void 0:kt.current)?void 0:d.cell)?f:[],ao=(0,u.Z)(oo,2),lo=ao[0],uo=ao[1],so=g.useRef(hi);so.current=hi,g.useLayoutEffect((function(){var e,t,n,r;_r.current||void 0===lo||void 0===uo||lo===(null==(t=null==(e=lr.current)?void 0:e.current)?void 0:t.cell[0])&&uo===(null==(r=null==(n=lr.current)?void 0:n.current)?void 0:r.cell[1])||so.current(lo,uo),_r.current=!1}),[lo,uo]);var co=void 0!==Yn.current&&(Yn.current.cell[0]>=Tr.length||Yn.current.cell[1]>=ii);g.useLayoutEffect((function(){co&&ur(qr,!1)}),[co,ur]);var fo=g.useMemo((function(){return!0===Ln&&!0===(null==At?void 0:At.tint)?it.fromSingleSelection(ii-1):it.empty()}),[ii,Ln,null==At?void 0:At.tint]),ho=g.useCallback((function(e){var t;return"boolean"===typeof Ht?Ht:null==(t=null==Ht?void 0:Ht(e-Hn))||t}),[Hn,Ht]),po=g.useMemo((function(){if(void 0===Ai||null===M.current)return null;var e=Ai.bounds,t=Ai.group,n=M.current.getBoundingClientRect();return g.createElement(er,{bounds:e,group:t,canvasBounds:n,onClose:function(){return Ri(void 0)},onFinish:function(e){Ri(void 0),null==ae||ae(t,e)}})}),[ae,Ai]),vo=Math.min(Tr.length,at+(Pn?1:0));g.useImperativeHandle(t,(function(){return{appendRow:function(e,t){return mi(e+Hn,t)},updateCells:function(e){var t;return 0!==Hn&&(e=e.map((function(e){return{cell:[e.cell[0]+Hn,e.cell[1]]}}))),null==(t=ni.current)?void 0:t.damage(e)},getBounds:function(e,t){var n;return null==(n=ni.current)?void 0:n.getBounds(e+Hn,t)},focus:function(){var e;return null==(e=ni.current)?void 0:e.focus()},emit:function(){var e=(0,o.Z)((0,i.Z)().mark((function e(t){return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:e.t0=t,e.next="delete"===e.t0?3:"fill-right"===e.t0?5:"fill-down"===e.t0?7:"copy"===e.t0?9:"paste"===e.t0?12:15;break;case 3:return eo({bounds:void 0,cancel:function(){},stopPropagation:function(){},preventDefault:function(){},ctrlKey:!1,key:"Delete",keyCode:46,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0}),e.abrupt("break",15);case 5:return eo({bounds:void 0,cancel:function(){},stopPropagation:function(){},preventDefault:function(){},ctrlKey:!0,key:"r",keyCode:82,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0}),e.abrupt("break",15);case 7:return eo({bounds:void 0,cancel:function(){},stopPropagation:function(){},preventDefault:function(){},ctrlKey:!0,key:"d",keyCode:68,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0}),e.abrupt("break",15);case 9:return e.next=11,ro(void 0,!0);case 11:case 14:return e.abrupt("break",15);case 12:return e.next=14,no();case 15:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),scrollTo:hi,remeasureColumns:function(e){var t,n=(0,a.Z)(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;Pi(r+Hn,!0)}}catch(i){n.e(i)}finally{n.f()}}}}),[mi,Pi,ro,eo,no,Hn,hi]);var go=null!=Wi?Wi:[],mo=(0,u.Z)(go,2),yo=mo[0],bo=mo[1],wo=g.useCallback((function(e){var t=(0,u.Z)(e,2),n=t[0],r=t[1];-1!==r?yo===n&&bo===r||(br({cell:e,range:{x:n,y:r,width:1,height:1}},!0,!1,"keyboard-nav"),hi(n,r)):"none"!==Ee&&(xr(it.fromSingleSelection(n),void 0,!1),ri())}),[Ee,ri,hi,yo,bo,br,xr]),xo=g.useState(!1),ko=(0,u.Z)(xo,2),Do=ko[0],Co=ko[1],So=g.useRef(x((function(e){Co(e)}),5)),Eo=g.useCallback((function(){So.current(!0),void 0===Yn.current&&0===Yn.columns.length&&0===Yn.rows.length&&void 0===R&&br({cell:[Hn,ti],range:{x:Hn,y:ti,width:1,height:1}},!0,!1,"keyboard-select")}),[ti,Yn,R,Hn,br]),Fo=g.useCallback((function(){So.current(!1)}),[]),Mo=g.useMemo((function(){var e,t,n=null!=(e=null==Ut?void 0:Ut.scrollbarWidthOverride)?e:function(){if(void 0!==Rt)return Rt;var e=document.createElement("p");e.style.width="100%",e.style.height="200px";var t=document.createElement("div");t.id="testScrollbar",t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.append(e),document.body.append(t);var n=e.offsetWidth;t.style.overflow="scroll";var r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),Rt=n-r}(),r=j+(Ln?1:0);if("number"===typeof Fn)t=Ar+r*Fn;else{for(var i=0,o=Math.min(r,10),a=0;a<o;a++)i+=Fn(a);i=Math.floor(i/o),t=Ar+r*i}t+=n;var l=Tr.reduce((function(e,t){return t.width+e}),0)+n;return["".concat(Math.min(1e5,l),"px"),"".concat(Math.min(1e5,t),"px")]}),[Tr,null==Ut?void 0:Ut.scrollbarWidthOverride,Fn,j,Ln,Ar]),Zo=(0,u.Z)(Mo,2),Ao=Zo[0],Ro=Zo[1];return g.createElement(Re.Provider,{value:kr},g.createElement(hr,{style:Me(kr),className:ne,inWidth:null!=V?V:Ao,inHeight:null!=N?N:Ro},g.createElement(Qn,{fillHandle:Wt,drawFocusRing:jt,experimental:Ut,fixedShadowX:Xt,fixedShadowY:Yt,getRowThemeOverride:B,headerIcons:Kt,imageWindowLoader:$t,initialSize:Gt,isDraggable:qt,onDragLeave:Qt,onRowMoved:Jt,overscrollX:Rn,overscrollY:In,preventDiagonalScrolling:nn,rightElement:rn,rightElementProps:on,showMinimap:an,smoothScrollX:ln,smoothScrollY:un,className:ne,enableGroups:Zr,onCanvasFocused:Eo,onCanvasBlur:Fo,canvasRef:M,onContextMenu:to,theme:kr,cellXOffset:ei,cellYOffset:ti,accessibilityHeight:Wr.height,onDragEnd:Ki,columns:Tr,drawCustomCell:we,drawHeader:vr,disabledRows:fo,freezeColumns:vo,lockColumns:Hn,firstColAccessible:0===Hn,getCellContent:ui,minColumnWidth:wn,maxColumnWidth:xn,searchInputRef:F,showSearch:jn,onSearchClose:Un,highlightRegions:ai,getCellsForSelection:qn,getGroupDetails:si,headerHeight:Mn,isFocused:Do,groupHeaderHeight:Zr?Zn:0,trailingRowType:Ln?!0===(null==At?void 0:At.sticky)?"sticky":"appended":"none",onColumnResize:sr,onColumnResizeEnd:dr,onColumnResizeStart:fr,onCellFocused:wo,onColumnMoved:Ui,onDragStart:Yi,onHeaderMenuClick:Ni,onItemHovered:$i,isFilling:!0===(null==R?void 0:R.fillHandle),onMouseMove:Vi,onKeyDown:eo,onKeyUp:pe,onMouseDown:Fi,onMouseUp:_i,onDragOverCell:Lt,onDrop:zt,onSearchResultsChanged:io,onVisibleRegionChanged:ji,clientSize:[Sr[0],Sr[1]],rowHeight:Fn,searchResults:ce,searchValue:fe,onSearchValueChange:de,rows:ii,scrollRef:T,selection:Yn,translateX:Wr.tx,translateY:Wr.ty,verticalBorder:ho,gridRef:ni,getCellRenderer:Fr,scrollToEnd:sn}),po,void 0!==S&&g.createElement(lt,(0,s.Z)((0,s.Z)({},S),{},{validateCell:ir,id:Ji,getCellRenderer:Fr,className:!0===(null==Ut?void 0:Ut.isSubGrid)?"click-outside-ignore":void 0,provideEditor:Zt,imageEditorOverride:z,onFinishEditing:Qi,markdownDivCreateNode:_,isOutsideClick:bn}))))}));function Jr(e){return(0,s.Z)((0,s.Z)({},e),{},{kind:Te.Custom})}function ei(e){return{customRenderers:g.useMemo((function(){return e.map(Jr)}),[e])}}},46979:function(e,t,n){"use strict";n.r(t),n.d(t,{FileSystemDirectoryHandle:function(){return b}});var r,i=n(649),o=n(11026),a=n(11092),l=n(27791),u=n(22951),s=n(91976),c=n(47169),d=n(67591),f=n(94337),h=n(64649),p=n(38692),v=n(22265),g=n(187),m=n(93232),y=Symbol("adapter");r=Symbol.asyncIterator;var b=function(e){(0,d.Z)(m,e);var t=(0,f.Z)(m);function m(e){var n;return(0,u.Z)(this,m),n=t.call(this,e),(0,h.Z)((0,c.Z)(n),y,void 0),n[y]=e,n}return(0,s.Z)(m,[{key:"getDirectoryHandle",value:function(){var e=(0,l.Z)((0,a.Z)().mark((function e(t){var n,r,i=arguments;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=i.length>1&&void 0!==i[1]?i[1]:{},""!==t){e.next=3;break}throw new TypeError("Name can't be an empty string.");case 3:if("."!==t&&".."!==t&&!t.includes("/")){e.next=5;break}throw new TypeError("Name contains invalid characters.");case 5:return n.create=!!n.create,e.next=8,this[y].getDirectoryHandle(t,n);case 8:return r=e.sent,e.abrupt("return",new m(r));case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"entries",value:function(){var e=this;return(0,v.Z)((0,a.Z)().mark((function t(){var r,i,l,u,s,c,d,f,h;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,(0,p.Z)(Promise.resolve().then(n.bind(n,8192)));case 2:r=t.sent,i=r.FileSystemFileHandle,l=!1,u=!1,t.prev=6,c=(0,g.Z)(e[y].entries());case 8:return t.next=10,(0,p.Z)(c.next());case 10:if(!(l=!(d=t.sent).done)){t.next=17;break}return f=(0,o.Z)(d.value,2),f[0],h=f[1],t.next=14,[h.name,"file"===h.kind?new i(h):new m(h)];case 14:l=!1,t.next=8;break;case 17:t.next=23;break;case 19:t.prev=19,t.t0=t.catch(6),u=!0,s=t.t0;case 23:if(t.prev=23,t.prev=24,!l||null==c.return){t.next=28;break}return t.next=28,(0,p.Z)(c.return());case 28:if(t.prev=28,!u){t.next=31;break}throw s;case 31:return t.finish(28);case 32:return t.finish(23);case 33:case"end":return t.stop()}}),t,null,[[6,19,23,33],[24,,28,32]])})))()}},{key:"getEntries",value:function(){var e=this;return(0,v.Z)((0,a.Z)().mark((function t(){var r,i,o,l,u,s,c,d;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,(0,p.Z)(Promise.resolve().then(n.bind(n,8192)));case 2:r=t.sent,i=r.FileSystemFileHandle,console.warn("deprecated, use .entries() instead"),o=!1,l=!1,t.prev=7,s=(0,g.Z)(e[y].entries());case 9:return t.next=11,(0,p.Z)(s.next());case 11:if(!(o=!(c=t.sent).done)){t.next=18;break}return d=c.value,t.next=15,"file"===d.kind?new i(d):new m(d);case 15:o=!1,t.next=9;break;case 18:t.next=24;break;case 20:t.prev=20,t.t0=t.catch(7),l=!0,u=t.t0;case 24:if(t.prev=24,t.prev=25,!o||null==s.return){t.next=29;break}return t.next=29,(0,p.Z)(s.return());case 29:if(t.prev=29,!l){t.next=32;break}throw u;case 32:return t.finish(29);case 33:return t.finish(24);case 34:case"end":return t.stop()}}),t,null,[[7,20,24,34],[25,,29,33]])})))()}},{key:"getFileHandle",value:function(){var e=(0,l.Z)((0,a.Z)().mark((function e(t){var r,i,o,l,u=arguments;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=u.length>1&&void 0!==u[1]?u[1]:{},e.next=3,Promise.resolve().then(n.bind(n,8192));case 3:if(i=e.sent,o=i.FileSystemFileHandle,""!==t){e.next=7;break}throw new TypeError("Name can't be an empty string.");case 7:if("."!==t&&".."!==t&&!t.includes("/")){e.next=9;break}throw new TypeError("Name contains invalid characters.");case 9:return r.create=!!r.create,e.next=12,this[y].getFileHandle(t,r);case 12:return l=e.sent,e.abrupt("return",new o(l));case 14:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"removeEntry",value:function(){var e=(0,l.Z)((0,a.Z)().mark((function e(t){var n,r=arguments;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=r.length>1&&void 0!==r[1]?r[1]:{},""!==t){e.next=3;break}throw new TypeError("Name can't be an empty string.");case 3:if("."!==t&&".."!==t&&!t.includes("/")){e.next=5;break}throw new TypeError("Name contains invalid characters.");case 5:return n.recursive=!!n.recursive,e.abrupt("return",this[y].removeEntry(t,n));case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"resolve",value:function(){var e=(0,l.Z)((0,a.Z)().mark((function e(t){var n,r,o,l,u,s,c,d,f,h;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.isSameEntry(this);case 2:if(!e.sent){e.next=4;break}return e.abrupt("return",[]);case 4:n=[{handle:this,path:[]}];case 5:if(!n.length){e.next=41;break}r=n.pop(),o=r.handle,l=r.path,u=!1,s=!1,e.prev=9,d=(0,g.Z)(o.values());case 11:return e.next=13,d.next();case 13:if(!(u=!(f=e.sent).done)){e.next=23;break}return h=f.value,e.next=17,h.isSameEntry(t);case 17:if(!e.sent){e.next=19;break}return e.abrupt("return",[].concat((0,i.Z)(l),[h.name]));case 19:"directory"===h.kind&&n.push({handle:h,path:[].concat((0,i.Z)(l),[h.name])});case 20:u=!1,e.next=11;break;case 23:e.next=29;break;case 25:e.prev=25,e.t0=e.catch(9),s=!0,c=e.t0;case 29:if(e.prev=29,e.prev=30,!u||null==d.return){e.next=34;break}return e.next=34,d.return();case 34:if(e.prev=34,!s){e.next=37;break}throw c;case 37:return e.finish(34);case 38:return e.finish(29);case 39:e.next=5;break;case 41:return e.abrupt("return",null);case 42:case"end":return e.stop()}}),e,this,[[9,25,29,39],[30,,34,38]])})));return function(t){return e.apply(this,arguments)}}()},{key:"keys",value:function(){var e=this;return(0,v.Z)((0,a.Z)().mark((function t(){var n,r,i,l,u,s,c;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=!1,r=!1,t.prev=2,l=(0,g.Z)(e[y].entries());case 4:return t.next=6,(0,p.Z)(l.next());case 6:if(!(n=!(u=t.sent).done)){t.next=13;break}return s=(0,o.Z)(u.value,1),c=s[0],t.next=10,c;case 10:n=!1,t.next=4;break;case 13:t.next=19;break;case 15:t.prev=15,t.t0=t.catch(2),r=!0,i=t.t0;case 19:if(t.prev=19,t.prev=20,!n||null==l.return){t.next=24;break}return t.next=24,(0,p.Z)(l.return());case 24:if(t.prev=24,!r){t.next=27;break}throw i;case 27:return t.finish(24);case 28:return t.finish(19);case 29:case"end":return t.stop()}}),t,null,[[2,15,19,29],[20,,24,28]])})))()}},{key:"values",value:function(){var e=this;return(0,v.Z)((0,a.Z)().mark((function t(){var n,r,i,l,u,s,c;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=!1,r=!1,t.prev=2,l=(0,g.Z)(e);case 4:return t.next=6,(0,p.Z)(l.next());case 6:if(!(n=!(u=t.sent).done)){t.next=13;break}return s=(0,o.Z)(u.value,2),s[0],c=s[1],t.next=10,c;case 10:n=!1,t.next=4;break;case 13:t.next=19;break;case 15:t.prev=15,t.t0=t.catch(2),r=!0,i=t.t0;case 19:if(t.prev=19,t.prev=20,!n||null==l.return){t.next=24;break}return t.next=24,(0,p.Z)(l.return());case 24:if(t.prev=24,!r){t.next=27;break}throw i;case 27:return t.finish(24);case 28:return t.finish(19);case 29:case"end":return t.stop()}}),t,null,[[2,15,19,29],[20,,24,28]])})))()}},{key:r,value:function(){return this.entries()}}]),m}(m.Z);Object.defineProperty(b.prototype,Symbol.toStringTag,{value:"FileSystemDirectoryHandle",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(b.prototype,{getDirectoryHandle:{enumerable:!0},entries:{enumerable:!0},getFileHandle:{enumerable:!0},removeEntry:{enumerable:!0}}),t.default=b},8192:function(e,t,n){"use strict";n.r(t),n.d(t,{FileSystemFileHandle:function(){return p}});var r=n(11092),i=n(27791),o=n(22951),a=n(91976),l=n(47169),u=n(67591),s=n(94337),c=n(64649),d=n(93232),f=n(82572),h=Symbol("adapter"),p=function(e){(0,u.Z)(n,e);var t=(0,s.Z)(n);function n(e){var r;return(0,o.Z)(this,n),r=t.call(this,e),(0,c.Z)((0,l.Z)(r),h,void 0),r[h]=e,r}return(0,a.Z)(n,[{key:"createWritable",value:function(){var e=(0,i.Z)((0,r.Z)().mark((function e(){var t,n=arguments;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.t0=f.Z,e.next=4,this[h].createWritable(t);case 4:return e.t1=e.sent,e.abrupt("return",new e.t0(e.t1));case 6:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getFile",value:function(){var e=(0,i.Z)((0,r.Z)().mark((function e(){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this[h].getFile());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),n}(d.Z);Object.defineProperty(p.prototype,Symbol.toStringTag,{value:"FileSystemFileHandle",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(p.prototype,{createWritable:{enumerable:!0},getFile:{enumerable:!0}}),t.default=p},93232:function(e,t,n){"use strict";var r=n(11092),i=n(27791),o=n(22951),a=n(91976),l=n(64649),u=Symbol("adapter"),s=function(){function e(t){(0,o.Z)(this,e),(0,l.Z)(this,u,void 0),(0,l.Z)(this,"name",void 0),(0,l.Z)(this,"kind",void 0),this.kind=t.kind,this.name=t.name,this[u]=t}return(0,a.Z)(e,[{key:"queryPermission",value:function(){var e=(0,i.Z)((0,r.Z)().mark((function e(){var t,n,i,o=arguments;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=(o.length>0&&void 0!==o[0]?o[0]:{}).mode,n=void 0===t?"read":t,!(i=this[u]).queryPermission){e.next=4;break}return e.abrupt("return",i.queryPermission({mode:n}));case 4:if("read"!==n){e.next=8;break}return e.abrupt("return","granted");case 8:if("readwrite"!==n){e.next=12;break}return e.abrupt("return",i.writable?"granted":"denied");case 12:throw new TypeError("Mode ".concat(n," must be 'read' or 'readwrite'"));case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"requestPermission",value:function(){var e=(0,i.Z)((0,r.Z)().mark((function e(){var t,n,i,o=arguments;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=(o.length>0&&void 0!==o[0]?o[0]:{}).mode,n=void 0===t?"read":t,!(i=this[u]).requestPermission){e.next=4;break}return e.abrupt("return",i.requestPermission({mode:n}));case 4:if("read"!==n){e.next=8;break}return e.abrupt("return","granted");case 8:if("readwrite"!==n){e.next=12;break}return e.abrupt("return",i.writable?"granted":"denied");case 12:throw new TypeError("Mode ".concat(n," must be 'read' or 'readwrite'"));case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"remove",value:function(){var e=(0,i.Z)((0,r.Z)().mark((function e(){var t,n=arguments;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:{},e.next=3,this[u].remove(t);case 3:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"isSameEntry",value:function(){var e=(0,i.Z)((0,r.Z)().mark((function e(t){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this!==t){e.next=2;break}return e.abrupt("return",!0);case 2:if(t&&"object"===typeof t&&this.kind===t.kind&&t[u]){e.next=4;break}return e.abrupt("return",!1);case 4:return e.abrupt("return",this[u].isSameEntry(t[u]));case 5:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}();Object.defineProperty(s.prototype,Symbol.toStringTag,{value:"FileSystemHandle",writable:!1,enumerable:!1,configurable:!0}),t.Z=s},82572:function(e,t,n){"use strict";var r=n(22951),i=n(91976),o=n(47169),a=n(67591),l=n(94337),u=function(e){(0,a.Z)(n,e);var t=(0,l.Z)(n);function n(){var e;(0,r.Z)(this,n);for(var i=arguments.length,a=new Array(i),l=0;l<i;l++)a[l]=arguments[l];return e=t.call.apply(t,[this].concat(a)),Object.setPrototypeOf((0,o.Z)(e),n.prototype),e._closed=!1,e}return(0,i.Z)(n,[{key:"close",value:function(){this._closed=!0;var e=this.getWriter(),t=e.close();return e.releaseLock(),t}},{key:"seek",value:function(e){return this.write({type:"seek",position:e})}},{key:"truncate",value:function(e){return this.write({type:"truncate",size:e})}},{key:"write",value:function(e){if(this._closed)return Promise.reject(new TypeError("Cannot write to a CLOSED writable stream"));var t=this.getWriter(),n=t.write(e);return t.releaseLock(),n}}]),n}(n(60643).Z.WritableStream);Object.defineProperty(u.prototype,Symbol.toStringTag,{value:"FileSystemWritableFileStream",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(u.prototype,{close:{enumerable:!0},seek:{enumerable:!0},truncate:{enumerable:!0},write:{enumerable:!0}}),t.Z=u},60643:function(e,t){"use strict";var n={ReadableStream:globalThis.ReadableStream,WritableStream:globalThis.WritableStream,TransformStream:globalThis.TransformStream,DOMException:globalThis.DOMException,Blob:globalThis.Blob,File:globalThis.File};t.Z=n},95345:function(e,t,n){"use strict";n.d(t,{Kr:function(){return l}});globalThis.showDirectoryPicker;globalThis.showOpenFilePicker;var r=n(11092),i=n(27791),o=globalThis.showSaveFilePicker;function a(){return a=(0,i.Z)((0,r.Z)().mark((function e(){var t,i,a,l,u,s=arguments;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=s.length>0&&void 0!==s[0]?s[0]:{},!o||t._preferPolyfill){e.next=3;break}return e.abrupt("return",o(t));case 3:return t._name&&(console.warn("deprecated _name, spec now have `suggestedName`"),t.suggestedName=t._name),e.next=6,Promise.resolve().then(n.bind(n,8192));case 6:return i=e.sent,a=i.FileSystemFileHandle,e.next=10,n.e(474).then(n.bind(n,60474));case 10:return l=e.sent,u=l.FileHandle,e.abrupt("return",new a(new u(t.suggestedName)));case 13:case"end":return e.stop()}}),e)}))),a.apply(this,arguments)}var l=function(){return a.apply(this,arguments)},u=n(11026);globalThis.DataTransferItem&&!DataTransferItem.prototype.getAsFileSystemHandle&&(DataTransferItem.prototype.getAsFileSystemHandle=(0,i.Z)((0,r.Z)().mark((function e(){var t,i,o,a,l,s,c,d;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.webkitGetAsEntry(),e.next=3,Promise.all([n.e(3631).then(n.bind(n,13053)),Promise.resolve().then(n.bind(n,46979)),Promise.resolve().then(n.bind(n,8192))]);case 3:return i=e.sent,o=(0,u.Z)(i,3),a=o[0],l=a.FileHandle,s=a.FolderHandle,c=o[1].FileSystemDirectoryHandle,d=o[2].FileSystemFileHandle,e.abrupt("return",t.isFile?new d(new l(t,!1)):new c(new s(t,!1)));case 11:case"end":return e.stop()}}),e,this)}))));n(46979),n(8192),n(93232),n(82572)}}]);