(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[7805],{32508:function(t,e,i){"use strict";i.r(e),i.d(e,{default:function(){return s}});var n=i(66845),a=i(18157),h=i(91487),r=i(23849),c=i(62622),o=(0,i(1515).Z)("div",{target:"e1uym6o70"})((function(t){return{"& *":{fontFamily:t.theme.genericFonts.bodyFont,fontSize:"9.6px"},"& svg":{maxWidth:"100%"}}}),""),u=i(40864);h.graphviz;var s=(0,c.Z)((function(t){var e=t.width,i=t.element,c=t.height,s="graphviz-chart-".concat(i.elementId),d=0,g=0,f=function(){var t=g,n=d;return c?(t=e,n=c):i.useContainerWidth&&(t=e),{chartWidth:t,chartHeight:n}},p=function(){try{var t=(0,h.graphviz)("#".concat(s)).zoom(!1).fit(!0).scale(1).engine(i.engine).renderDot(i.spec).on("end",(function(){var t=(0,a.Ys)("#".concat(s," > svg")).node();t&&(d=t.getBBox().height,g=t.getBBox().width)})),e=f(),n=e.chartHeight,c=e.chartWidth;n>0&&t.height(n),c>0&&t.width(c)}catch(o){(0,r.H)(o)}};(0,n.useEffect)((function(){p()}));var v=f(),l=v.chartWidth?v.chartWidth:e,m=v.chartHeight?v.chartHeight:c;return(0,u.jsx)(o,{className:"graphviz stGraphVizChart","data-testid":"stGraphVizChart",id:s,style:{width:l,height:m}})}))},47318:function(){},57516:function(){},38728:function(){}}]);