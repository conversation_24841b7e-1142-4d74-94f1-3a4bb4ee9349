(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[7956],{20607:function(e,t,r){"use strict";r.d(t,{W:function(){return a}});var n=r(25773),o=r(66845),i=r(69),a=o.forwardRef((function(e,t){return o.createElement(i.D,(0,n.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},e,{ref:t}),o.createElement("path",{d:"M0 3v2h8V3H0z"}))}));a.displayName="Minus"},89997:function(e,t,r){"use strict";r.d(t,{v:function(){return a}});var n=r(25773),o=r(66845),i=r(69),a=o.forwardRef((function(e,t){return o.createElement(i.D,(0,n.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},e,{ref:t}),o.createElement("path",{d:"M3 0v3H0v2h3v3h2V5h3V3H5V0H3z"}))}));a.displayName="Plus"},82534:function(e,t,r){"use strict";var n=r(66845),o=r(80318),i=r(32510),a=r(9656),s=r(98479),c=r(38254);function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}var l=["Root","StartEnhancer","EndEnhancer"],p=["startEnhancer","endEnhancer","overrides"];function f(){return f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},f.apply(this,arguments)}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,i=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(c){s=!0,o=c}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return d(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function b(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function g(e,t){return g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},g(e,t)}function m(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=w(e);if(t){var o=w(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===u(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return v(e)}(this,r)}}function v(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function w(e){return w=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},w(e)}function j(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var x=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&g(e,t)}(w,e);var t,r,u,d=m(w);function w(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,w);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return j(v(e=d.call.apply(d,[this].concat(r))),"state",{isFocused:e.props.autoFocus||!1}),j(v(e),"onFocus",(function(t){e.setState({isFocused:!0}),e.props.onFocus(t)})),j(v(e),"onBlur",(function(t){e.setState({isFocused:!1}),e.props.onBlur(t)})),e}return t=w,(r=[{key:"render",value:function(){var e=this.props,t=e.startEnhancer,r=e.endEnhancer,u=e.overrides,d=u.Root,h=u.StartEnhancer,g=u.EndEnhancer,m=b(u,l),v=b(e,p),w=y((0,o.jb)(d,s.fC),2),j=w[0],x=w[1],E=y((0,o.jb)(h,s.Fp),2),k=E[0],S=E[1],F=y((0,o.jb)(g,s.Fp),2),P=F[0],C=F[1],I=(0,i.t)(this.props,this.state);return n.createElement(j,f({"data-baseweb":"input"},I,x,{$adjoined:O(t,r),$hasIconTrailing:this.props.clearable||"password"==this.props.type}),_(t)&&n.createElement(k,f({},I,S,{$position:c.Xf.start}),"function"===typeof t?t(I):t),n.createElement(a.Z,f({},v,{overrides:m,adjoined:O(t,r),onFocus:this.onFocus,onBlur:this.onBlur})),_(r)&&n.createElement(P,f({},I,C,{$position:c.Xf.end}),"function"===typeof r?r(I):r))}}])&&h(t.prototype,r),u&&h(t,u),Object.defineProperty(t,"prototype",{writable:!1}),w}(n.Component);function O(e,t){return _(e)&&_(t)?c.y4.both:_(e)?c.y4.left:_(t)?c.y4.right:c.y4.none}function _(e){return Boolean(e||0===e)}j(x,"defaultProps",{autoComplete:"on",autoFocus:!1,disabled:!1,name:"",onBlur:function(){},onFocus:function(){},overrides:{},required:!1,size:c.NO.default,startEnhancer:null,endEnhancer:null,clearable:!1,type:"text",readOnly:!1}),t.Z=x},52347:function(e,t,r){var n;!function(){"use strict";var o={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function i(e){return function(e,t){var r,n,a,s,c,u,l,p,f,y=1,d=e.length,b="";for(n=0;n<d;n++)if("string"===typeof e[n])b+=e[n];else if("object"===typeof e[n]){if((s=e[n]).keys)for(r=t[y],a=0;a<s.keys.length;a++){if(void 0==r)throw new Error(i('[sprintf] Cannot access property "%s" of undefined value "%s"',s.keys[a],s.keys[a-1]));r=r[s.keys[a]]}else r=s.param_no?t[s.param_no]:t[y++];if(o.not_type.test(s.type)&&o.not_primitive.test(s.type)&&r instanceof Function&&(r=r()),o.numeric_arg.test(s.type)&&"number"!==typeof r&&isNaN(r))throw new TypeError(i("[sprintf] expecting number but found %T",r));switch(o.number.test(s.type)&&(p=r>=0),s.type){case"b":r=parseInt(r,10).toString(2);break;case"c":r=String.fromCharCode(parseInt(r,10));break;case"d":case"i":r=parseInt(r,10);break;case"j":r=JSON.stringify(r,null,s.width?parseInt(s.width):0);break;case"e":r=s.precision?parseFloat(r).toExponential(s.precision):parseFloat(r).toExponential();break;case"f":r=s.precision?parseFloat(r).toFixed(s.precision):parseFloat(r);break;case"g":r=s.precision?String(Number(r.toPrecision(s.precision))):parseFloat(r);break;case"o":r=(parseInt(r,10)>>>0).toString(8);break;case"s":r=String(r),r=s.precision?r.substring(0,s.precision):r;break;case"t":r=String(!!r),r=s.precision?r.substring(0,s.precision):r;break;case"T":r=Object.prototype.toString.call(r).slice(8,-1).toLowerCase(),r=s.precision?r.substring(0,s.precision):r;break;case"u":r=parseInt(r,10)>>>0;break;case"v":r=r.valueOf(),r=s.precision?r.substring(0,s.precision):r;break;case"x":r=(parseInt(r,10)>>>0).toString(16);break;case"X":r=(parseInt(r,10)>>>0).toString(16).toUpperCase()}o.json.test(s.type)?b+=r:(!o.number.test(s.type)||p&&!s.sign?f="":(f=p?"+":"-",r=r.toString().replace(o.sign,"")),u=s.pad_char?"0"===s.pad_char?"0":s.pad_char.charAt(1):" ",l=s.width-(f+r).length,c=s.width&&l>0?u.repeat(l):"",b+=s.align?f+r+c:"0"===u?f+c+r:c+f+r)}return b}(function(e){if(s[e])return s[e];var t,r=e,n=[],i=0;for(;r;){if(null!==(t=o.text.exec(r)))n.push(t[0]);else if(null!==(t=o.modulo.exec(r)))n.push("%");else{if(null===(t=o.placeholder.exec(r)))throw new SyntaxError("[sprintf] unexpected placeholder");if(t[2]){i|=1;var a=[],c=t[2],u=[];if(null===(u=o.key.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(a.push(u[1]);""!==(c=c.substring(u[0].length));)if(null!==(u=o.key_access.exec(c)))a.push(u[1]);else{if(null===(u=o.index_access.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");a.push(u[1])}t[2]=a}else i|=2;if(3===i)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");n.push({placeholder:t[0],param_no:t[1],keys:t[2],sign:t[3],pad_char:t[4],align:t[5],width:t[6],precision:t[7],type:t[8]})}r=r.substring(t[0].length)}return s[e]=n}(e),arguments)}function a(e,t){return i.apply(null,[e].concat(t||[]))}var s=Object.create(null);t.sprintf=i,t.vsprintf=a,"undefined"!==typeof window&&(window.sprintf=i,window.vsprintf=a,void 0===(n=function(){return{sprintf:i,vsprintf:a}}.call(t,r,t,e))||(e.exports=n))}()}}]);