"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[9758],{69758:function(e,n,r){r.r(n),r.d(n,{config:function(){return o},errors:function(){return s},fromDataTransfer:function(){return u},getDirHandlesFromInput:function(){return c},getFileHandlesFromInput:function(){return p}});var t=r(11092),a=r(11026),i=r(27791),s={INVALID:["seeking position failed.","InvalidStateError"],GONE:["A requested file or directory could not be found at the time an operation was processed.","NotFoundError"],MISMATCH:["The path supplied exists, but was not an entry of requested type.","TypeMismatchError"],MOD_ERR:["The object can not be modified in this way.","InvalidModificationError"],SYNTAX:function(e){return["Failed to execute 'write' on 'UnderlyingSinkBase': Invalid params passed. ".concat(e),"SyntaxError"]},SECURITY:["It was determined that certain files are unsafe for access within a Web application, or that too many calls are being made on file resources.","SecurityError"],DISALLOWED:["The request is not allowed by the user agent or the platform in the current context.","NotAllowedError"]},o={writable:globalThis.WritableStream};function u(e){return l.apply(this,arguments)}function l(){return(l=(0,i.Z)((0,t.Z)().mark((function e(n){var i,s,o,u,l,c;return(0,t.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.warn("deprecated fromDataTransfer - use `dt.items[0].getAsFileSystemHandle()` instead"),e.next=3,Promise.all([r.e(8724).then(r.bind(r,88724)),r.e(3053).then(r.bind(r,13053)),Promise.resolve().then(r.bind(r,46979))]);case 3:return i=e.sent,s=(0,a.Z)(i,3),o=s[0],u=s[1],l=s[2],(c=new o.FolderHandle("",!1))._entries=n.map((function(e){return e.isFile?new u.FileHandle(e,!1):new u.FolderHandle(e,!1)})),e.abrupt("return",new l.FileSystemDirectoryHandle(c));case 11:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function c(e){return d.apply(this,arguments)}function d(){return(d=(0,i.Z)((0,t.Z)().mark((function e(n){var a,i,s,o,u,l,c,d;return(0,t.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.e(8724).then(r.bind(r,88724));case 2:return a=e.sent,i=a.FolderHandle,s=a.FileHandle,e.next=7,Promise.resolve().then(r.bind(r,46979));case 7:return o=e.sent,u=o.FileSystemDirectoryHandle,l=Array.from(n.files),c=l[0].webkitRelativePath.split("/",1)[0],d=new i(c,!1),l.forEach((function(e){var n=e.webkitRelativePath.split("/");n.shift();var r=n.pop();n.reduce((function(e,n){return e._entries[n]||(e._entries[n]=new i(n,!1)),e._entries[n]}),d)._entries[r]=new s(e.name,e,!1)})),e.abrupt("return",new u(d));case 14:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return f.apply(this,arguments)}function f(){return(f=(0,i.Z)((0,t.Z)().mark((function e(n){var a,i,s,o;return(0,t.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.e(8724).then(r.bind(r,88724));case 2:return a=e.sent,i=a.FileHandle,e.next=6,Promise.resolve().then(r.bind(r,8192));case 6:return s=e.sent,o=s.FileSystemFileHandle,e.abrupt("return",Array.from(n.files).map((function(e){return new o(new i(e.name,e,!1))})));case 9:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);